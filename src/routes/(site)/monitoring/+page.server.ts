// import { PUBLIC_BACKEND_URL } from "$env/static/public";
// import { env as publicEnv } from '$env/dynamic/public';
import { getBackendUrl } from '$src/lib/config';

import { services } from "$lib/api/features";
import { redirect, error, fail } from '@sveltejs/kit';
import type { PageServerLoad } from "./$types";
import type { Actions } from "@sveltejs/kit";

export const load: PageServerLoad = async ({ cookies }) => {
    let access_token = cookies.get('access_token')
    let refresh_token = cookies.get('refresh_token');

    if (!access_token) {
        return {
            tickets: [],
            users: [],
            statuses: [],
            priorities: [],
            ticket_topics: [],
            error: 'No access token available'
        };
    }

    for (let repeatCount = 0; repeatCount < 3; repeatCount++) {
        try {
            // Remove tickets.getAll() - let client handle all ticket fetching with pagination
            const response_users = await services.users.getAll(access_token);
            const response_statuses = await services.statuses.getAll(access_token);
            const response_priorities = await services.ticket_priority.getAll(access_token);
            const response_ticket_topics = await services.tickets.getAllTicketTopics(access_token);
            const response_selfUserInfo = await services.users.getUserInfo(access_token);

            if (response_users.res_status === 401 || response_statuses.res_status === 401 || response_priorities.res_status === 401 || response_ticket_topics.res_status == 401 || response_selfUserInfo.res_status === 401) {
                throw error(401, 'Invalid access token!!!');
            } 

            return {
                tickets: [], // Start with empty array - client will fetch paginated tickets
                users: response_users.users || [],
                statuses: response_statuses.statuses || [],
                priorities: response_priorities.priorities || [],
                ticket_topics : response_ticket_topics.ticket_topics || [],
                loginUser: response_selfUserInfo.users,
                token: access_token 
            }
        } catch (err) {
            // console.error('Error fetching user details:', err);
            // error(500, 'Failed to load user details');

            const refreshResponse = await services.users.refreshToken(refresh_token);
            const login_token = refreshResponse.login_token;

            console.log("Refresh Response: ", refreshResponse);


            if (login_token.length === 0) {
                cookies.set("isLogin", 'false', { path: '/' })
                throw redirect(302, '/login');
            } else {
                access_token = login_token.access;
                refresh_token = login_token.refresh;

                cookies.set("access_token", access_token, { path: '/' });
                cookies.set("refresh_token", refresh_token, { path: '/' })
            }
        }
    }
};

export const actions: Actions = {
    ticket_priority_change: async ({ request, fetch, cookies }) => {
        const formData = await request.formData();
        const access_token = cookies.get('access_token');
        const api_key = cookies.get('api_key'); // Assuming the token is stored in cookies
        const ticket_id = formData.get('ticket_id');
        const priority_id = formData.get('new_priority_id');

            
        const url = `${getBackendUrl()}/ticket/api/tickets/${ticket_id}/priority/`;

        try {
            const response = await fetch(url, {
                method: 'PUT',
                headers: {
                    'Authorization': `Bearer ${access_token}`,
                    'Content-Type': 'application/json',
                    'X-API-Key': api_key
                },
                body: JSON.stringify({
                    "priority_id": priority_id
                })
            });

            if (!response.ok) {
                const errorData = await response.json();
                // throw new Error(`HTTP error! status: ${response.status} - ${errorData.message}`);
                throw new Error(`Status: ${errorData.message} (${response.status})`);
            }

            const responseData = await response.json();
            return { success: true, message: responseData.message || 'Priority updated successfully' };
        } catch (error) {
            console.error('Change Ticket Priority error:', error);
            return fail(500, { error: `${error.message}` });
        }
    },

    ticket_transfer_owner: async ({ request, fetch, cookies }) => {
        const formData = await request.formData();
        const access_token = cookies.get('access_token')
        const ticket_id = formData.get('ticket_id');
        const new_owner_id = formData.get('new_owner_id');

            
        const url = `${getBackendUrl()}/ticket/ticket_transfer_owner/${ticket_id}/`

        try {
            const response = await fetch(url, {
                method: 'PUT',
                headers: {
                    'Authorization': `Bearer ${access_token}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    "new_owner_id": new_owner_id
                })
            });
            if (!response.ok) {
                const errorData = await response.json();
                // throw new Error(`HTTP error! status: ${response.status} - ${errorData.message}`);
                throw new Error(`Status: ${errorData.message} (${response.status})`);
            }
            const responseData = await response.json();
            return { success: true, message: responseData.message || 'Owner transferred successfully' };
        } catch (error) {
            console.error('Transfer Ticket Owner error:', error);
            return fail(500, { error: `${error.message}` });
        }
    },

    change_ticket_status: async ({ request, fetch, cookies }) => {
        const formData = await request.formData();
        const access_token = cookies.get('access_token')
        const ticket_id = formData.get('ticket_id');
        const new_status_id = Number(formData.get('new_status_id')); // Convert to number
        const new_ticket_topic = formData.get('new_ticket_topic');

        // When it closes (status ID 6 is "closed", not 3)
        if (new_status_id === 6) { 
            // Only process ticket topics if they are provided
            if (new_ticket_topic && new_ticket_topic.trim() !== '') {
                const new_ticket_topic_array = new_ticket_topic.split(',').map(topic => Number(topic));
                
                const url_ticket_topic = `${getBackendUrl()}/ticket/api/tickets/${ticket_id}/topics/`    
                try {
                    const response_ticket_topic = await fetch(url_ticket_topic, {
                        method: 'PUT',
                        headers: {
                            'Authorization': `Bearer ${access_token}`,
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            "topic_ids": new_ticket_topic_array
                        })
                    });
                    if (!response_ticket_topic.ok) {
                        const errorData = await response_ticket_topic.json();
                        throw new Error(`Status: ${errorData.message} (${response_ticket_topic.status})`);
                    }
                } catch (error) {
                    console.error('Change Ticket Topic error:', error);
                    return fail(500, { error: `${error.message}` });
                }
            }
        }

        const url = `${getBackendUrl()}/ticket/ticket_change_status/${ticket_id}/`
        try {
            const response = await fetch(url, {
                method: 'PUT',
                headers: {
                    'Authorization': `Bearer ${access_token}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    "new_status_id": new_status_id // Now sending as number
                })
            });
            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(`Status: ${errorData.message} (${response.status})`);
            }
            const responseData = await response.json();
            return { success: true, message: responseData.message || 'Status updated successfully' };
        } catch (error) {
            console.error('Change Ticket Status error:', error);
            return fail(500, { error: `${error.message}` });
        }
    }
};
