// import { conversationStore } from '$lib/stores/conversationStore';
// import { customerDetailStore } from '$lib/stores/customerDetailStore';
// import type { Message } from '$lib/types/customer';
// import { getBackendUrl } from '$src/lib/config';

// class PlatformWebSocket {
//     private ws: WebSocket | null = null;
//     private customerId: number | null = null;
//     private platformId: number | null = null;
//     private reconnectTimeout: number | null = null; // Browser setTimeout returns number
//     private reconnectAttempts = 0;
//     private maxReconnectAttempts = 5;
//     private reconnectDelay = 1000;
//     private heartbeatInterval: number | null = null;
    
//     onConnectionChange?: (connected: boolean) => void;

//     connect(customerId: number, platformId: number) {
//         this.customerId = customerId;
//         this.platformId = platformId;
//         this.connectWebSocket();
//     }

//     private connectWebSocket() {
//         if (this.ws?.readyState === WebSocket.OPEN) {
//             return;
//         }

//         // const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
//         // const wsUrl = `${protocol}//${window.location.host}/ws/customer/${this.customerId}/platform/${this.platformId}/`;

//         // Determine WebSocket protocol (ws: or wss:) based on backend URL
//         const backendUrl = getBackendUrl() ?? (typeof window !== 'undefined' ? window.location.origin : '');
// 		const wsProtocol = backendUrl.startsWith('https') ? 'wss:' : 'ws:';
// 		const wsUrl = backendUrl.replace(/^https?:/, wsProtocol);
        
//         // TODO - Delete this
//         console.log('Connecting to WebSocket:', wsUrl);

//         try {
//             // this.ws = new WebSocket(wsUrl);
//             this.ws = new WebSocket(`${wsUrl}/ws/customer/${this.customerId}/platform/${this.platformId}/`);

//             this.ws.onopen = () => {
//                 console.log('Platform WebSocket connected');
//                 this.reconnectAttempts = 0;
//                 this.reconnectDelay = 1000;
//                 this.onConnectionChange?.(true);
//                 customerDetailStore.updateConnectionStatus(this.platformId!, 'connected');
//                 this.startHeartbeat();
//             };

//             this.ws.onmessage = (event) => {
//                 try {
//                     const data = JSON.parse(event.data);
//                     this.handleMessage(data);
//                 } catch (error) {
//                     console.error('Error parsing WebSocket message:', error);
//                 }
//             };

//             this.ws.onerror = (error) => {
//                 console.error('Platform WebSocket error:', error);
//             };

//             this.ws.onclose = () => {
//                 console.log('Platform WebSocket closed');
//                 this.onConnectionChange?.(false);
//                 customerDetailStore.updateConnectionStatus(this.platformId!, 'disconnected');
//                 this.stopHeartbeat();
//                 this.handleReconnect();
//             };
//         } catch (error) {
//             console.error('Error creating WebSocket:', error);
//             this.handleReconnect();
//         }
//     }

//     private handleReconnect() {
//         if (this.reconnectAttempts < this.maxReconnectAttempts) {
//             this.reconnectAttempts++;
//             console.log(`Attempting to reconnect... (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
            
//             this.reconnectTimeout = window.setTimeout(() => {
//                 this.connectWebSocket();
//             }, this.reconnectDelay);
            
//             // Exponential backoff
//             this.reconnectDelay = Math.min(this.reconnectDelay * 2, 30000);
//         } else {
//             console.error('Max reconnection attempts reached');
//         }
//     }

//     private startHeartbeat() {
//         this.heartbeatInterval = window.setInterval(() => {
//             if (this.ws?.readyState === WebSocket.OPEN) {
//                 this.ws.send(JSON.stringify({ action: 'ping' }));
//             }
//         }, 30000); // Ping every 30 seconds
//     }

//     private stopHeartbeat() {
//         if (this.heartbeatInterval) {
//             window.clearInterval(this.heartbeatInterval);
//             this.heartbeatInterval = null;
//         }
//     }

//     private handleMessage(data: any) {
//         const { type, ...payload } = data;

//         switch (type) {
//             case 'new_message':
//                 this.handleNewMessage(payload.message);
//                 break;
            
//             case 'message_status_update':
//                 this.handleMessageStatusUpdate(payload);
//                 break;
            
//             case 'typing_indicator':
//                 this.handleTypingIndicator(payload);
//                 break;
            
//             case 'connection_status':
//                 this.handleConnectionStatus(payload);
//                 break;
            
//             case 'initial_messages':
//                 this.handleInitialMessages(payload);
//                 break;
            
//             case 'more_messages':
//                 this.handleMoreMessages(payload);
//                 break;
                
//             case 'pong':
//                 // Heartbeat response
//                 break;
            
//             default:
//                 console.warn('Unknown WebSocket message type:', type);
//         }
//     }

//     private handleNewMessage(message: Message) {
//         if (this.platformId) {
//             conversationStore.addMessage(this.platformId, message);
//             customerDetailStore.updateLatestMessage(this.platformId, message);
            
//             // Update unread count if message is from customer
//             if (!message.is_self) {
//                 customerDetailStore.incrementUnreadCount(this.platformId);
//             }
//         }
//     }

//     private handleMessageStatusUpdate(data: {
//         message_id: number;
//         status: string;
//         timestamp?: string;
//     }) {
//         if (this.platformId) {
//             conversationStore.updateMessageStatus(this.platformId, data.message_id, data.status);
//         }
//     }

//     private handleTypingIndicator(data: {
//         is_typing: boolean;
//         user_name?: string;
//     }) {
//         if (this.platformId && data.user_name) {
//             conversationStore.setTypingIndicator(this.platformId, data.user_name, data.is_typing);
//         }
//     }

//     private handleConnectionStatus(data: {
//         connected: boolean;
//     }) {
//         this.onConnectionChange?.(data.connected);
//         if (this.platformId) {
//             customerDetailStore.updateConnectionStatus(
//                 this.platformId,
//                 data.connected ? 'connected' : 'disconnected'
//             );
//         }
//     }

//     private handleInitialMessages(data: {
//         messages: Message[];
//         has_more: boolean;
//     }) {
//         if (this.platformId) {
//             conversationStore.prependMessages(this.platformId, data.messages, data.has_more);
//         }
//     }

//     private handleMoreMessages(data: {
//         messages: Message[];
//         has_more: boolean;
//     }) {
//         if (this.platformId) {
//             conversationStore.prependMessages(this.platformId, data.messages, data.has_more);
//         }
//     }

//     sendMessage(message: any) {
//         if (this.ws?.readyState === WebSocket.OPEN) {
//             this.ws.send(JSON.stringify({
//                 action: 'send_message',
//                 ...message
//             }));
//         } else {
//             console.warn('WebSocket is not connected');
//         }
//     }

//     sendTypingIndicator(isTyping: boolean) {
//         if (this.ws?.readyState === WebSocket.OPEN) {
//             this.ws.send(JSON.stringify({
//                 action: 'typing_indicator',
//                 is_typing: isTyping
//             }));
//         }
//     }

//     markMessagesAsRead(messageIds: number[]) {
//         if (this.ws?.readyState === WebSocket.OPEN) {
//             this.ws.send(JSON.stringify({
//                 action: 'mark_as_read',
//                 message_ids: messageIds
//             }));
//         }
//     }

//     loadMoreMessages(beforeMessageId: number, limit: number = 20) {
//         if (this.ws?.readyState === WebSocket.OPEN) {
//             this.ws.send(JSON.stringify({
//                 action: 'load_more_messages',
//                 before_message_id: beforeMessageId,
//                 limit: limit
//             }));
//         }
//     }

//     disconnect() {
//         if (this.reconnectTimeout) {
//             window.clearTimeout(this.reconnectTimeout);
//             this.reconnectTimeout = null;
//         }
        
//         this.stopHeartbeat();
        
//         if (this.ws) {
//             this.ws.close();
//             this.ws = null;
//         }
        
//         this.customerId = null;
//         this.platformId = null;
//         this.reconnectAttempts = 0;
//     }
// }

// export const platformWebSocket = new PlatformWebSocket();


































// import { writable } from 'svelte/store';
// import { getBackendUrl } from '$lib/config';

// export interface WebSocketMessage {
//     type: string;
//     data: any;
// }

// class PlatformWebSocket {
//     private ws: WebSocket | null = null;
//     private reconnectTimeout: number | null = null;
//     private subscribedPlatforms: Set<number> = new Set();
    
//     public messages = writable<WebSocketMessage[]>([]);
//     public connectionStatus = writable<'connecting' | 'connected' | 'disconnected'>('disconnected');
    
//     connect() {
//         if (this.ws?.readyState === WebSocket.OPEN) return;
        
//         this.connectionStatus.set('connecting');
        
// //         // Determine WebSocket protocol (ws: or wss:) based on backend URL
// //         const backendUrl = getBackendUrl() ?? (typeof window !== 'undefined' ? window.location.origin : '');
// // 		const wsProtocol = backendUrl.startsWith('https') ? 'wss:' : 'ws:';
// // 		const wsUrl = backendUrl.replace(/^https?:/, wsProtocol);
        
// //         // TODO - Delete this
// //         console.log('Connecting to WebSocket:', wsUrl);

// //         try {
// //             // this.ws = new WebSocket(wsUrl);
// //             this.ws = new WebSocket(`${wsUrl}/ws/customer/${this.customerId}/platform/${this.platformId}/`);


//         // Determine WebSocket protocol (ws: or wss:) based on backend URL
//         const backendUrl = getBackendUrl() ?? (typeof window !== 'undefined' ? window.location.origin : '');
// 		const wsProtocol = backendUrl.startsWith('https') ? 'wss:' : 'ws:';
// 		const wsUrl = backendUrl.replace(/^https?:/, wsProtocol);

//         // const wsUrl = getBackendUrl().replace('http', 'ws');
//         // TODO - Delete this
//         console.log('Connecting to WebSocket:', wsUrl);

//         this.ws = new WebSocket(`${wsUrl}/ws/platforms/`);
        
//         this.ws.onopen = () => {
//             console.log('Platform WebSocket connected');
//             this.connectionStatus.set('connected');
            
//             // Re-subscribe to platforms
//             this.subscribedPlatforms.forEach(platformId => {
//                 this.sendMessage({
//                     type: 'subscribe_platform',
//                     platform_id: platformId
//                 });
//             });
//         };
        
//         this.ws.onmessage = (event) => {
//             const message = JSON.parse(event.data);
//             this.messages.update(msgs => [...msgs, message]);
            
//             // Handle specific message types
//             if (message.type === 'new_message') {
//                 this.handleNewMessage(message.data);
//             } else if (message.type === 'message_status_update') {
//                 this.handleStatusUpdate(message.data);
//             }
//         };
        
//         this.ws.onclose = () => {
//             console.log('Platform WebSocket disconnected');
//             this.connectionStatus.set('disconnected');
//             this.scheduleReconnect();
//         };
        
//         this.ws.onerror = (error) => {
//             console.error('Platform WebSocket error:', error);
//         };
//     }
    
//     subscribeToPlatform(platformId: number) {
//         this.subscribedPlatforms.add(platformId);
        
//         if (this.ws?.readyState === WebSocket.OPEN) {
//             this.sendMessage({
//                 type: 'subscribe_platform',
//                 platform_id: platformId
//             });
//         }
//     }
    
//     unsubscribeFromPlatform(platformId: number) {
//         this.subscribedPlatforms.delete(platformId);
        
//         if (this.ws?.readyState === WebSocket.OPEN) {
//             this.sendMessage({
//                 type: 'unsubscribe_platform',
//                 platform_id: platformId
//             });
//         }
//     }
    
//     private sendMessage(message: any) {
//         if (this.ws?.readyState === WebSocket.OPEN) {
//             this.ws.send(JSON.stringify(message));
//         }
//     }
    
//     private handleNewMessage(data: any) {
//         // Dispatch event for components to handle
//         window.dispatchEvent(new CustomEvent('platform-new-message', { detail: data }));
//     }
    
//     private handleStatusUpdate(data: any) {
//         // Dispatch event for components to handle
//         window.dispatchEvent(new CustomEvent('platform-message-status', { detail: data }));
//     }
    
//     private scheduleReconnect() {
//         if (this.reconnectTimeout) return;
        
//         this.reconnectTimeout = window.setTimeout(() => {
//             this.reconnectTimeout = null;
//             this.connect();
//         }, 5000);
//     }
    
//     disconnect() {
//         if (this.reconnectTimeout) {
//             clearTimeout(this.reconnectTimeout);
//             this.reconnectTimeout = null;
//         }
        
//         if (this.ws) {
//             this.ws.close();
//             this.ws = null;
//         }
        
//         this.subscribedPlatforms.clear();
//     }
// }

// export const platformWebSocket = new PlatformWebSocket();




















































// import { writable } from 'svelte/store';
// import { getBackendUrl } from '$lib/config';

// export interface WebSocketMessage {
//     type: string;
//     data: any;
// }

// class PlatformWebSocket {
//     private ws: WebSocket | null = null;
//     private reconnectTimeout: number | null = null;
//     private subscribedPlatforms: Set<number> = new Set();
    
//     public messages = writable<WebSocketMessage[]>([]);
//     public connectionStatus = writable<'connecting' | 'connected' | 'disconnected'>('disconnected');
    
//     connect() {
//         if (this.ws?.readyState === WebSocket.OPEN) return;
        
//         this.connectionStatus.set('connecting');
        
//         const wsUrl = getBackendUrl().replace('http', 'ws');
//         this.ws = new WebSocket(`${wsUrl}/ws/platforms/`);
        
//         this.ws.onopen = () => {
//             console.log('Platform WebSocket connected');
//             this.connectionStatus.set('connected');
            
//             // Re-subscribe to platforms
//             this.subscribedPlatforms.forEach(platformId => {
//                 this.sendMessage({
//                     type: 'subscribe_platform',
//                     platform_id: platformId
//                 });
//             });
//         };
        
//         this.ws.onmessage = (event) => {
//             const message = JSON.parse(event.data);
//             this.messages.update(msgs => [...msgs, message]);
            
//             // Handle specific message types
//             if (message.type === 'new_message') {
//                 this.handleNewMessage(message.data);
//             } else if (message.type === 'message_status_update') {
//                 this.handleStatusUpdate(message.data);
//             }
//         };
        
//         this.ws.onclose = () => {
//             console.log('Platform WebSocket disconnected');
//             this.connectionStatus.set('disconnected');
//             this.scheduleReconnect();
//         };
        
//         this.ws.onerror = (error) => {
//             console.error('Platform WebSocket error:', error);
//         };
//     }
    
//     subscribeToPlatform(platformId: number) {
//         this.subscribedPlatforms.add(platformId);
        
//         if (this.ws?.readyState === WebSocket.OPEN) {
//             this.sendMessage({
//                 type: 'subscribe_platform',
//                 platform_id: platformId
//             });
//         }
//     }
    
//     unsubscribeFromPlatform(platformId: number) {
//         this.subscribedPlatforms.delete(platformId);
        
//         if (this.ws?.readyState === WebSocket.OPEN) {
//             this.sendMessage({
//                 type: 'unsubscribe_platform',
//                 platform_id: platformId
//             });
//         }
//     }
    
//     private sendMessage(message: any) {
//         if (this.ws?.readyState === WebSocket.OPEN) {
//             this.ws.send(JSON.stringify(message));
//         }
//     }
    
//     private handleNewMessage(data: any) {
//         // Dispatch event for components to handle
//         window.dispatchEvent(new CustomEvent('platform-new-message', { detail: data }));
//     }
    
//     private handleStatusUpdate(data: any) {
//         // Dispatch event for components to handle
//         window.dispatchEvent(new CustomEvent('platform-message-status', { detail: data }));
//     }
    
//     private scheduleReconnect() {
//         if (this.reconnectTimeout) return;
        
//         this.reconnectTimeout = window.setTimeout(() => {
//             this.reconnectTimeout = null;
//             this.connect();
//         }, 5000);
//     }
    
//     disconnect() {
//         if (this.reconnectTimeout) {
//             clearTimeout(this.reconnectTimeout);
//             this.reconnectTimeout = null;
//         }
        
//         if (this.ws) {
//             this.ws.close();
//             this.ws = null;
//         }
        
//         this.subscribedPlatforms.clear();
//     }
// }

// export const platformWebSocket = new PlatformWebSocket();






































import { writable } from 'svelte/store';
import { getBackendUrl } from '$lib/config';

export interface WebSocketMessage {
    type: string;
    data: any;
}

class PlatformWebSocket {
    private ws: WebSocket | null = null;
    private reconnectTimeout: number | null = null;
    private subscribedPlatforms: Set = new Set();
    private reconnectAttempts = 0;
    private maxReconnectAttempts = 5;
    private reconnectDelay = 1000;
    private pingInterval: number | null = null;
    
    public messages = writable([]);
    public connectionStatus = writable<'connecting' | 'connected' | 'disconnected'>('disconnected');
    
    connect() {
        if (this.ws?.readyState === WebSocket.OPEN) return;
        
        this.connectionStatus.set('connecting');
        
        const backendUrl = getBackendUrl();
        const wsProtocol = backendUrl.startsWith('https') ? 'wss:' : 'ws:';
        const wsUrl = backendUrl.replace(/^https?:/, wsProtocol);
        
        // Use the global platforms endpoint
        this.ws = new WebSocket(`${wsUrl}/ws/platforms/`);
        
        // TODO - Delete this or Log this
        console.log('Connecting to WebSocket:', this.ws.url);
        
        this.ws.onopen = () => {
            console.log('Platform WebSocket connected');
            this.connectionStatus.set('connected');
            this.reconnectAttempts = 0;
            this.reconnectDelay = 1000;
            
            // Re-subscribe to all platforms
            if (this.subscribedPlatforms.size > 0) {
                this.sendMessage({
                    action: 'subscribe_multiple',
                    platform_ids: Array.from(this.subscribedPlatforms)
                });
            }

            // show which platforms are subscribed
            // console.log('Subscribed platforms:', Array.from(this.subscribedPlatforms));
            
            // Start ping interval to keep connection alive
            this.startPingInterval();
        };
        
        this.ws.onmessage = (event) => {
            try {
                const message = JSON.parse(event.data);
                this.messages.update(msgs => [...msgs, message]);
                this.handleMessage(message);
            } catch (error) {
                console.error('Error parsing WebSocket message:', error);
            }
        };
        
        this.ws.onclose = () => {
            console.log('Platform WebSocket disconnected');
            this.connectionStatus.set('disconnected');
            this.stopPingInterval();
            this.scheduleReconnect();
        };
        
        this.ws.onerror = (error) => {
            console.error('Platform WebSocket error:', error);
        };
    }
    
    subscribeToPlatform(platformId: number) {
        this.subscribedPlatforms.add(platformId);
        
        if (this.ws?.readyState === WebSocket.OPEN) {
            this.sendMessage({
                action: 'subscribe_platform',
                platform_id: platformId
            });
        }
    }
    
    unsubscribeFromPlatform(platformId: number) {
        this.subscribedPlatforms.delete(platformId);
        
        if (this.ws?.readyState === WebSocket.OPEN) {
            this.sendMessage({
                action: 'unsubscribe_platform',
                platform_id: platformId
            });
        }
    }
    
    subscribeToMultiplePlatforms(platformIds: number[]) {
        platformIds.forEach(id => this.subscribedPlatforms.add(id));
        
        if (this.ws?.readyState === WebSocket.OPEN) {
            this.sendMessage({
                action: 'subscribe_multiple',
                platform_ids: platformIds
            });
        }
    }

    unsubscribeFromMultiplePlatforms(platformIds: number[]) {
        platformIds.forEach(id => this.subscribedPlatforms.delete(id));
        
        if (this.ws?.readyState === WebSocket.OPEN) {
            this.sendMessage({
                action: 'unsubscribe_multiple',
                platform_ids: platformIds
            });
        }
    }
    
    private sendMessage(message: any) {
        if (this.ws?.readyState === WebSocket.OPEN) {
            this.ws.send(JSON.stringify(message));
        }
    }
    
    private handleMessage(message: WebSocketMessage) {
        console.log('WebSocket received:', message);
        // console.log('WebSocket received:', message);
        // console.log(`handleMessage's WebSocket received: ${message}`);
        switch (message.type) {
            case 'new_message':
                this.handleNewMessage(message);
                break;
            case 'platform_message_update':
                this.handlePlatformMessageUpdate(message);
                break;
            case 'platform_status':
                this.handleStatusUpdate(message);
                break;
            case 'platform_status_update':
                this.handleStatusUpdate(message);
                break;
            case 'typing_indicator':
                this.handleTypingIndicator(message);
                break;
            case 'platform_updates':
                this.handlePlatformUpdates(message);
                break;
            case 'batch_complete':
                this.handleBatchComplete(message);
                break;
        }
    }
    
    private handleNewMessage(message: any) {
        // Dispatch event for components to handle
        window.dispatchEvent(new CustomEvent('platform-new-message', {
            detail: {
                platformId: message.platform_id,
                message: message.message,
                unreadCount: message.unread_count
            }
        }));
    }

    private handlePlatformMessageUpdate(message: any) {
        // Handle the new batch broadcasting events
        // console.log('Platform message update received:', message);

        // Dispatch event for components to handle - using the same event name for compatibility
        window.dispatchEvent(new CustomEvent('platform-new-message', {
            detail: {
                platformId: message.platform_id,
                message: message.message,
                unreadCount: message.unread_count,
                updateType: message.update_type,
                batchId: message.batch_id
            }
        }));
    }
    
    private handleStatusUpdate(message: any) {
        window.dispatchEvent(new CustomEvent('platform-status-update', { 
            detail: {
                platformId: message.platform_id,
                status: message.status
            }
        }));
    }
    
    private handleTypingIndicator(message: any) {
        window.dispatchEvent(new CustomEvent('platform-typing', { 
            detail: {
                platformId: message.platform_id,
                isTyping: message.is_typing,
                userName: message.user_name
            }
        }));
    }
    
    private handlePlatformUpdates(message: any) {
        window.dispatchEvent(new CustomEvent('platform-bulk-update', {
            detail: message.updates
        }));
    }

    private handleBatchComplete(message: any) {
        // Handle batch completion events
        // console.log('Batch complete received:', message);

        window.dispatchEvent(new CustomEvent('platform-batch-complete', {
            detail: {
                batchId: message.batch_id,
                platformId: message.platform_id,
                summary: message.summary
            }
        }));
    }
    
    private startPingInterval() {
        this.pingInterval = window.setInterval(() => {
            if (this.ws?.readyState === WebSocket.OPEN) {
                this.sendMessage({
                    action: 'ping',
                    timestamp: Date.now()
                });
            }
        }, 30000); // Ping every 30 seconds
    }
    
    private stopPingInterval() {
        if (this.pingInterval) {
            clearInterval(this.pingInterval);
            this.pingInterval = null;
        }
    }
    
    private scheduleReconnect() {
        if (this.reconnectTimeout || this.reconnectAttempts >= this.maxReconnectAttempts) return;
        
        this.reconnectTimeout = window.setTimeout(() => {
            this.reconnectTimeout = null;
            this.reconnectAttempts++;
            this.reconnectDelay = Math.min(this.reconnectDelay * 2, 30000); // Max 30 seconds
            this.connect();
        }, this.reconnectDelay);
    }
    
    disconnect() {
        this.stopPingInterval();
        
        if (this.reconnectTimeout) {
            clearTimeout(this.reconnectTimeout);
            this.reconnectTimeout = null;
        }
        
        if (this.ws) {
            this.ws.close();
            this.ws = null;
        }
        
        this.subscribedPlatforms.clear();
        this.connectionStatus.set('disconnected');
    }
}

export const platformWebSocket = new PlatformWebSocket();












