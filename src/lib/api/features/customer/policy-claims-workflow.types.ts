/**
 * TypeScript interfaces for Simplified Policy Claims API Workflow Configuration
 * These types define the structure of the policy-claims-workflow.json file
 */

export interface FixedValues {
  social_id: string;
  channel_id: string;
  citizen_id: string;
}

export interface DatabaseConfig {
  table: string;
  fields: {
    social_id: string;
    channel_id: string;
  };
  where: string;
}

export interface WorkflowConfig {
  mode: 'database' | 'fixed' | 'hybrid';
  fixed_values: FixedValues;
  database: DatabaseConfig;
}

export interface WorkflowStep {
  id: number;
  name: string;
  endpoint: string;
  method: 'GET' | 'POST' | 'PUT' | 'DELETE';
  headers?: Record<string, string>;
  request: Record<string, any>;
  extract: Record<string, string>;
  validate?: string;
  iterate_over?: string;
  retry: number;
}

export interface StorageConfig {
  table: string;
  key: string[];
  data: Record<string, string>;
}

export interface WorkflowOptions {
  timeout_minutes: number;
  retry_delay_seconds: number;
}

export interface SimplifiedPolicyClaimsWorkflowConfig {
  name: string;
  version: string;
  config: WorkflowConfig;
  steps: WorkflowStep[];
  storage: StorageConfig;
  options: WorkflowOptions;
}

/**
 * Runtime data interfaces for workflow execution
 */
export interface WorkflowExecutionContext {
  customer_id: string;
  execution_id: string;
  started_at: Date;
  current_step: number;
  step_data: Record<string, any>;
  database_data: Record<string, any>;
}

export interface StepExecutionResult {
  step_id: number;
  step_name: string;
  success: boolean;
  execution_time_ms: number;
  extracted_data: Record<string, any>;
  error_message?: string;
  retry_count: number;
}

export interface WorkflowExecutionResult {
  workflow_name: string;
  execution_id: string;
  customer_id: string;
  success: boolean;
  total_execution_time_ms: number;
  started_at: Date;
  completed_at: Date;
  step_results: StepExecutionResult[];
  final_data: Record<string, any>;
  error_message?: string;
}

/**
 * API Response interfaces based on the TPA API documentation
 */
export interface GetTokenResponse {
  token: string;
}

export interface SearchCitizenIDResponse {
  ListOfSearchCitizenID: Array<{
    Status: string;
    CitizenID: string;
  }>;
  ErrorMessage: string;
}

export interface PolicyListSocialResponse {
  ListOfPolicyListSocial: Array<{
    Name: string;
    CitizenID: string;
    PolNo: string;
    MemberCode: string;
    EffFrom: string;
    EffTo: string;
  }>;
}

export interface PolicyDetailSocialResponse {
  ListOfPolDet: any; // Policy details structure may vary
  ListOfPolClaim: any; // Claims data structure may vary
}

/**
 * Utility type for template variable resolution
 */
export type TemplateVariable = 
  | `{{database.${string}}}`
  | `{{step_${number}.${string}}}`
  | `{{iteration.${string}}}`
  | `{{input.${string}}}`
  | `{{current_timestamp}}`;

/**
 * Configuration validation interface
 */
export interface WorkflowValidationResult {
  valid: boolean;
  errors: string[];
  warnings: string[];
}
