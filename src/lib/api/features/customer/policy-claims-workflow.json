{"name": "Policy Claims API Integration", "version": "1.0.0", "config": {"mode": "fixed", "fixed_values": {"social_id": "U3ef2199803607a9ec643f2461fd2f039", "channel_id": "2006769099", "citizen_id": "2019086318637"}, "database": {"table": "customer_customerplatformidentity", "fields": {"social_id": "platform_user_id", "channel_id": "channel_id"}, "where": "customer_id = :customer_id"}}, "steps": [{"id": 1, "name": "get_bearer_token", "endpoint": "/api/tpa/api/GetToken", "method": "POST", "request": {"USERNAME": "BVTPA", "PASSWORD": "*d!n^+Cb@1", "SOCIAL_ID": "{{social_id}}", "CHANNEL_ID": "{{channel_id}}", "CHANNEL": "LINE"}, "extract": {"bearer_token": "$"}, "retry": 3}, {"id": 2, "name": "verify_citizen_id", "endpoint": "/api/tpa/api/SearchCitizenID", "method": "POST", "headers": {"Authorization": "Bearer {{bearer_token}}"}, "request": {"SOCIAL_ID": "{{social_id}}", "CHANNEL_ID": "{{channel_id}}", "CHANNEL": "LINE"}, "extract": {"citizen_id": "$.ListOfSearchCitizenID[0].CitizenID"}, "validate": "$.ListOfSearchCitizenID[0].Status == '1'", "retry": 2}, {"id": 3, "name": "fetch_policy_list", "endpoint": "/api/tpa/api/PolicyListSocial", "method": "POST", "headers": {"Authorization": "Bearer {{bearer_token}}"}, "request": {"SOCIAL_ID": "{{social_id}}", "CHANNEL_ID": "{{channel_id}}", "CHANNEL": "LINE", "CITIZEN_ID": "{{citizen_id}}"}, "extract": {"policies": "$.ListOfPolicyListSocial", "member_codes": "$.ListOfPolicyListSocial[*].MemberCode"}, "retry": 2}, {"id": 4, "name": "fetch_policy_details", "endpoint": "/api/tpa/api/PolicyDetailSocial", "method": "POST", "headers": {"Authorization": "Bearer {{bearer_token}}"}, "request": {"SOCIAL_ID": "{{social_id}}", "CHANNEL_ID": "{{channel_id}}", "CHANNEL": "LINE", "MEMBER_CODE": "{{member_code}}"}, "extract": {"policy_details": "$.ListOfPolDet", "claims_data": "$.ListOfPolClaim"}, "iterate_over": "member_codes", "retry": 2}], "storage": {"table": "CustomerPolicies", "key": ["customer_id", "PolNo"], "data": {"customer_id": "{{input.customer_id}}", "citizen_id": "{{citizen_id}}", "policies_data": "{{policies}}", "policy_details": "{{policy_details}}", "claims_data": "{{claims_data}}", "last_updated": "{{timestamp}}"}}, "options": {"timeout_minutes": 10, "retry_delay_seconds": 5}}