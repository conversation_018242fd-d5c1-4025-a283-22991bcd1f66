/**
 * Policy Workflow Executor Service
 *
 * Executes the 4-step TPA API workflow for fetching policy and claims data:
 * 1. Get Bearer Token
 * 2. Verify Citizen ID
 * 3. Fetch Policy List
 * 4. Fetch Policy Details
 *
 * Returns raw API response data without transformation.
 */

import type {
  SimplifiedPolicyClaimsWorkflowConfig,
  WorkflowStep,
  WorkflowExecutionContext,
  StepExecutionResult
} from './policy-claims-workflow.types';

import {
  loadWorkflowConfig,
  createExecutionContext,
  resolveTemplateVariables,
  getStepsInOrder,
  resolveDataSource
} from './policy-claims-workflow.utils';

/**
 * Raw API response data structure combining results from steps 3 and 4
 */
export interface RawPolicyWorkflowData {
  customer_id: string;
  execution_id: string;
  step3_data: {
    ListOfPolicyListSocial: any[];
  };
  step4_data: {
    ListOfPolDet: any[];
    ListOfPolClaim: any[];
  };
  execution_metadata: {
    started_at: Date;
    completed_at: Date;
    total_execution_time_ms: number;
    step_results: StepExecutionResult[];
  };
}

export class PolicyWorkflowExecutor {
  private config: SimplifiedPolicyClaimsWorkflowConfig;
  private baseTimeout: number = 10000; // 10 seconds base timeout

  constructor() {
    this.config = loadWorkflowConfig();
  }

  /**
   * Execute the complete 4-step workflow for a customer
   * Returns raw API response data without transformation
   */
  async executeWorkflow(customerId: string): Promise<RawPolicyWorkflowData> {
    const context = createExecutionContext(customerId);
    const steps = getStepsInOrder(this.config);
    const stepResults: StepExecutionResult[] = [];

    console.log(`Starting workflow execution for customer ${customerId}`, {
      execution_id: context.execution_id,
      total_steps: steps.length
    });

    try {
      // Resolve data source based on configuration mode
      const dataSource = resolveDataSource(this.config);
      context.database_data = {
        social_id: dataSource.social_id,
        channel_id: dataSource.channel_id,
        citizen_id: dataSource.citizen_id
      };

      // Execute each step in sequence
      for (const step of steps) {
        context.current_step = step.id;
        console.log(`Executing step ${step.id}: ${step.name}`);

        const stepResult = await this.executeStep(step, context);
        stepResults.push(stepResult);

        if (!stepResult.success) {
          throw new Error(`Step ${step.id} (${step.name}) failed: ${stepResult.error_message}`);
        }

        // Store extracted data for use in subsequent steps
        Object.assign(context.step_data, stepResult.extracted_data);
      }

      // Combine raw results from steps 3 and 4 without transformation
      const completedAt = new Date();
      const rawData: RawPolicyWorkflowData = {
        customer_id: customerId,
        execution_id: context.execution_id,
        step3_data: {
          ListOfPolicyListSocial: context.step_data.policies || []
        },
        step4_data: {
          ListOfPolDet: context.step_data.policy_details || [],
          ListOfPolClaim: context.step_data.claims_data || []
        },
        execution_metadata: {
          started_at: context.started_at,
          completed_at: completedAt,
          total_execution_time_ms: completedAt.getTime() - context.started_at.getTime(),
          step_results: stepResults
        }
      };

      console.log(`Workflow execution completed successfully for customer ${customerId}`, {
        execution_id: context.execution_id,
        total_steps: stepResults.length,
        total_time_ms: rawData.execution_metadata.total_execution_time_ms
      });

      return rawData;

    } catch (error) {
      console.error(`Workflow execution failed for customer ${customerId}:`, error);
      throw error;
    }
  }

  /**
   * Execute a single workflow step with retry logic
   */
  private async executeStep(step: WorkflowStep, context: WorkflowExecutionContext): Promise<StepExecutionResult> {
    const startTime = Date.now();
    let lastError: Error | null = null;
    let retryCount = 0;

    while (retryCount <= step.retry) {
      try {
        console.log(`Attempting step ${step.id} (${step.name}), attempt ${retryCount + 1}/${step.retry + 1}`);

        let result: any;
        
        switch (step.name) {
          case 'get_bearer_token':
            result = await this.getBearerToken(step, context);
            break;
          case 'verify_citizen_id':
            result = await this.verifyCitizenId(step, context);
            break;
          case 'fetch_policy_list':
            result = await this.fetchPolicyList(step, context);
            break;
          case 'fetch_policy_details':
            result = await this.fetchPolicyDetails(step, context);
            break;
          default:
            throw new Error(`Unknown step: ${step.name}`);
        }

        // Extract data according to step configuration
        const extractedData = this.extractDataFromResponse(result, step.extract);

        // Validate extracted data if validation is specified
        if (step.validate) {
          this.validateStepResult(result, step.validate);
        }

        return {
          step_id: step.id,
          step_name: step.name,
          success: true,
          execution_time_ms: Date.now() - startTime,
          extracted_data: extractedData,
          retry_count: retryCount
        };

      } catch (error) {
        lastError = error as Error;
        retryCount++;
        
        console.warn(`Step ${step.id} (${step.name}) attempt ${retryCount} failed:`, error);

        if (retryCount <= step.retry) {
          const delayMs = this.config.options.retry_delay_seconds * 1000 * Math.pow(2, retryCount - 1);
          console.log(`Retrying step ${step.id} in ${delayMs}ms...`);
          await this.delay(delayMs);
        }
      }
    }

    return {
      step_id: step.id,
      step_name: step.name,
      success: false,
      execution_time_ms: Date.now() - startTime,
      extracted_data: {},
      error_message: lastError?.message || 'Unknown error',
      retry_count: retryCount - 1
    };
  }

  /**
   * Step 1: Get Bearer Token
   *
   * Note: In development, this uses a proxy endpoint (/api/tpa) configured in vite.config.ts
   * to handle certificate validation issues with the UAT environment.
   * The proxy forwards requests to the actual TPA API with secure: false.
   */
  private async getBearerToken(step: WorkflowStep, context: WorkflowExecutionContext): Promise<any> {
    const requestBody = this.resolveStepRequest(step.request, context);

    console.log(`Making request to: ${step.endpoint}`, {
      method: step.method,
      body: requestBody
    });

    const response = await fetch(step.endpoint, {
      method: step.method,
      headers: {
        'Content-Type': 'application/json',
        ...step.headers
      },
      body: JSON.stringify(requestBody),
      signal: AbortSignal.timeout(this.baseTimeout)
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`API request failed:`, {
        status: response.status,
        statusText: response.statusText,
        endpoint: step.endpoint,
        error: errorText
      });
      throw new Error(`HTTP ${response.status}: ${response.statusText} - ${errorText}`);
    }

    const responseText = await response.text();
    console.log(`API response received:`, responseText);

    // The GetToken API returns a plain string token
    return responseText.replace(/"/g, ''); // Remove quotes if present
  }

  /**
   * Step 2: Verify Citizen ID
   */
  private async verifyCitizenId(step: WorkflowStep, context: WorkflowExecutionContext): Promise<any> {
    const requestBody = this.resolveStepRequest(step.request, context);
    const headers = this.resolveStepHeaders(step.headers || {}, context);
    
    const response = await fetch(step.endpoint, {
      method: step.method,
      headers: {
        'Content-Type': 'application/json',
        ...headers
      },
      body: JSON.stringify(requestBody),
      signal: AbortSignal.timeout(this.baseTimeout)
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    return await response.json();
  }

  /**
   * Step 3: Fetch Policy List
   */
  private async fetchPolicyList(step: WorkflowStep, context: WorkflowExecutionContext): Promise<any> {
    const requestBody = this.resolveStepRequest(step.request, context);
    const headers = this.resolveStepHeaders(step.headers || {}, context);
    
    const response = await fetch(step.endpoint, {
      method: step.method,
      headers: {
        'Content-Type': 'application/json',
        ...headers
      },
      body: JSON.stringify(requestBody),
      signal: AbortSignal.timeout(this.baseTimeout)
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    return await response.json();
  }

  /**
   * Step 4: Fetch Policy Details (iterates over member codes)
   */
  private async fetchPolicyDetails(step: WorkflowStep, context: WorkflowExecutionContext): Promise<any> {
    const memberCodes = context.step_data.member_codes || [];
    const allPolicyDetails: any[] = [];
    const allClaimsData: any[] = [];

    console.log(`Fetching policy details for ${memberCodes.length} member codes`);

    for (const memberCode of memberCodes) {
      const requestBody = this.resolveStepRequest(step.request, {
        ...context,
        step_data: { ...context.step_data, member_code: memberCode }
      });
      const headers = this.resolveStepHeaders(step.headers || {}, context);
      
      const response = await fetch(step.endpoint, {
        method: step.method,
        headers: {
          'Content-Type': 'application/json',
          ...headers
        },
        body: JSON.stringify(requestBody),
        signal: AbortSignal.timeout(this.baseTimeout)
      });

      if (!response.ok) {
        console.warn(`Failed to fetch details for member code ${memberCode}: HTTP ${response.status}`);
        continue;
      }

      const result = await response.json();
      
      if (result.ListOfPolDet) {
        allPolicyDetails.push(...(Array.isArray(result.ListOfPolDet) ? result.ListOfPolDet : [result.ListOfPolDet]));
      }
      
      if (result.ListOfPolClaim) {
        allClaimsData.push(...(Array.isArray(result.ListOfPolClaim) ? result.ListOfPolClaim : [result.ListOfPolClaim]));
      }
    }

    return {
      ListOfPolDet: allPolicyDetails,
      ListOfPolClaim: allClaimsData
    };
  }

  /**
   * Resolve template variables in step request body
   */
  private resolveStepRequest(request: Record<string, any>, context: WorkflowExecutionContext): Record<string, any> {
    const resolved: Record<string, any> = {};
    
    for (const [key, value] of Object.entries(request)) {
      if (typeof value === 'string') {
        resolved[key] = resolveTemplateVariables(value, context);
      } else {
        resolved[key] = value;
      }
    }
    
    return resolved;
  }

  /**
   * Resolve template variables in step headers
   */
  private resolveStepHeaders(headers: Record<string, string>, context: WorkflowExecutionContext): Record<string, string> {
    const resolved: Record<string, string> = {};
    
    for (const [key, value] of Object.entries(headers)) {
      resolved[key] = resolveTemplateVariables(value, context);
    }
    
    return resolved;
  }

  /**
   * Extract data from API response using JSONPath-like expressions
   */
  private extractDataFromResponse(response: any, extractConfig: Record<string, string>): Record<string, any> {
    const extracted: Record<string, any> = {};
    
    for (const [key, path] of Object.entries(extractConfig)) {
      if (path === '$') {
        // Root response
        extracted[key] = response;
      } else if (path.startsWith('$.')) {
        // Simple JSONPath extraction
        const pathParts = path.substring(2).split('.');
        let value = response;
        
        for (const part of pathParts) {
          if (part.includes('[*]')) {
            // Array extraction
            const arrayKey = part.replace('[*]', '');
            if (value[arrayKey] && Array.isArray(value[arrayKey])) {
              const nextPart = pathParts[pathParts.indexOf(part) + 1];
              if (nextPart) {
                value = value[arrayKey].map((item: any) => item[nextPart]).filter(Boolean);
              } else {
                value = value[arrayKey];
              }
              break;
            }
          } else if (value && typeof value === 'object') {
            value = value[part];
          } else {
            value = undefined;
            break;
          }
        }
        
        extracted[key] = value;
      }
    }
    
    return extracted;
  }

  /**
   * Validate step result according to validation rules
   */
  private validateStepResult(response: any, validationRule: string): void {
    // Simple validation for citizen ID verification
    if (validationRule.includes("Status == '1'")) {
      const listOfSearchCitizenID = response.ListOfSearchCitizenID;
      if (!listOfSearchCitizenID || !Array.isArray(listOfSearchCitizenID) || listOfSearchCitizenID.length === 0) {
        throw new Error('No citizen ID search results found');
      }
      
      const firstResult = listOfSearchCitizenID[0];
      if (firstResult.Status !== '1') {
        throw new Error(`Citizen ID verification failed: Status = ${firstResult.Status}`);
      }
    }
  }



  /**
   * Utility method to add delay for retry logic
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}
