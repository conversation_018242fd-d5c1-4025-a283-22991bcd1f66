# Policy Claims API Workflow Configuration (Simplified)

## Overview

This document describes the simplified JSON configuration file `policy-claims-workflow.json` that defines the essential API integration workflow for fetching insurance policies and claims data from the TPA (Third Party Administrator) API.

## Purpose

The configuration file was extracted from the `PolicyClaimsApiStepsSection.svelte` component and simplified to focus on core functionality while providing flexible deployment options for development, testing, and production environments.

## File Location

```
src/lib/api/features/customer/policy-claims-workflow.json
```

## Configuration Structure

### 1. Basic Metadata
- **name**: Workflow name
- **version**: Configuration version

### 2. Configuration Modes
The workflow supports three operational modes:

#### Database Mode (`"mode": "database"`)
- Reads `SOCIAL_ID` and `CHANNEL_ID` from `customer_customerplatformidentity` table
- Uses database lookup for customer identification
- Production-ready mode

#### Fixed Values Mode (`"mode": "fixed"`)
- Uses hardcoded values for testing/development:
  - `SOCIAL_ID`: "U3ef2199803607a9ec643f2461fd2f039"
  - `CHANNEL_ID`: "2006769099"
  - `CITIZEN_ID`: "2019086318637"
- Bypasses database lookups for faster testing

#### Hybrid Mode (`"mode": "hybrid"`)
- Reads `SOCIAL_ID` and `CHANNEL_ID` from database
- Uses fixed `CITIZEN_ID` for testing
- Useful for partial testing scenarios

### 3. Simplified Workflow Steps
Four sequential steps with streamlined configuration:

#### Step 1: Get Bearer Token
- **Endpoint**: `/api/GetToken`
- **Input**: Username, password, social_id, channel_id
- **Extract**: `bearer_token` from response
- **Retry**: 3 attempts

#### Step 2: Verify Citizen ID
- **Endpoint**: `/api/SearchCitizenID`
- **Headers**: Bearer token from Step 1
- **Input**: social_id, channel_id
- **Extract**: `citizen_id` from response
- **Validate**: Status must equal '1'
- **Retry**: 2 attempts

#### Step 3: Fetch Policy List
- **Endpoint**: `/api/PolicyListSocial`
- **Headers**: Bearer token from Step 1
- **Input**: social_id, channel_id, citizen_id from Step 2
- **Extract**: `policies` array and `member_codes` array
- **Retry**: 2 attempts

#### Step 4: Fetch Policy Details (Iterative)
- **Endpoint**: `/api/PolicyDetailSocial`
- **Headers**: Bearer token from Step 1
- **Input**: social_id, channel_id, individual member_code
- **Extract**: `policy_details` and `claims_data`
- **Iteration**: Loops over each member_code from Step 3
- **Retry**: 2 attempts per iteration

### 4. Simplified Data Flow

```
Config Mode → Data Source → Step 1 → Bearer Token
                ↓              ↓
            Step 2 → CitizenID (or fixed value)
                ↓         ↓
            Step 3 → MemberCodes[]
                ↓         ↓
            Step 4 → Policy Details (iterate over MemberCodes)
```

### 5. Storage Configuration
- **Table**: `CustomerPolicies`
- **Primary Key**: `customer_id` + `PolNo`
- **Data Mapping**: Direct mapping from extracted values
- **Timestamp**: Automatic last_updated field

### 6. Essential Options
- **Global timeout**: 10 minutes maximum
- **Retry delay**: 5 seconds between retries
- **Per-step retries**: Configurable (1-3 attempts)
- **Iteration support**: Step 4 automatically iterates over member_codes

## Simplified Template Variables

The configuration uses a streamlined template variable system:

- `{{social_id}}` - SOCIAL_ID (from database or fixed values)
- `{{channel_id}}` - CHANNEL_ID (from database or fixed values)
- `{{citizen_id}}` - CitizenID (from Step 2 or fixed values)
- `{{bearer_token}}` - Bearer token from Step 1
- `{{member_code}}` - Current MemberCode during Step 4 iteration
- `{{input.customer_id}}` - Customer ID passed to workflow
- `{{timestamp}}` - Current timestamp for database operations

## Simplified Backend Implementation

To implement this streamlined workflow, the backend should:

1. **Load configuration** and determine operational mode
2. **Resolve data sources** based on mode (database vs fixed values)
3. **Execute steps sequentially** with template variable substitution
4. **Handle retries** as specified per step
5. **Support iteration** for Step 4's member_code processing
6. **Store results** in CustomerPolicies table
7. **Provide basic logging** for debugging and monitoring

## Configuration Mode Examples

### Database Mode
```json
{
  "config": {
    "mode": "database"
  }
}
```

### Fixed Values Mode (Development/Testing)
```json
{
  "config": {
    "mode": "fixed"
  }
}
```

### Hybrid Mode (Partial Testing)
```json
{
  "config": {
    "mode": "hybrid"
  }
}
```

## Security Considerations

- **Credentials**: The USERNAME and PASSWORD are hardcoded in the configuration
- **Token Management**: Bearer tokens should be securely handled and not logged
- **Database Access**: Ensure proper access controls for customer data
- **API Endpoints**: All endpoints use HTTPS for secure communication

## Maintenance

- **Version Control**: Update the version number when making changes
- **Documentation**: Keep this documentation in sync with configuration changes
- **Testing**: Test workflow changes in UAT environment before production
- **Monitoring**: Review performance metrics regularly for optimization opportunities

## Related Files

- **Source Component**: `src/lib/components/settings/business/PolicyClaimsApiStepsSection.svelte`
- **Customer Services**: `src/lib/api/features/customer/customers.service.ts`
- **API Types**: `src/lib/api/types/customer.ts`
