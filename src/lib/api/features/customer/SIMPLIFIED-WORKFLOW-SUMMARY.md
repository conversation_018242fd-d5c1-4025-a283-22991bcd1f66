# Simplified Policy Claims API Workflow - Summary

## What Was Simplified

The original `policy-claims-workflow.json` configuration has been streamlined from **279 lines** to **95 lines** while maintaining all core functionality.

### Removed Complexity
- ❌ Verbose descriptions and documentation fields
- ❌ Detailed error handling configurations
- ❌ Complex monitoring and performance tracking
- ❌ Nested configuration structures
- ❌ Redundant validation rules

### Kept Essential Elements
- ✅ 4-step sequential API workflow
- ✅ Template variable system for data flow
- ✅ Retry logic per step
- ✅ Database storage configuration
- ✅ Core error handling (retry attempts)

## New Configuration Modes

### 1. Database Mode (Production)
```json
{
  "config": {
    "mode": "database"
  }
}
```
- Reads `SOCIAL_ID` and `CHANNEL_ID` from `customer_customerplatformidentity` table
- Uses Step 2 API response for `CITIZEN_ID`
- Full production workflow

### 2. Fixed Values Mode (Development/Testing)
```json
{
  "config": {
    "mode": "fixed"
  }
}
```
- Uses hardcoded values:
  - `SOCIAL_ID`: "U3ef2199803607a9ec643f2461fd2f039"
  - `CHANNEL_ID`: "2006769099"
  - `CITIZEN_ID`: "2019086318637"
- Bypasses database lookups and Step 2 API call
- Fastest testing mode

### 3. Hybrid Mode (Partial Testing)
```json
{
  "config": {
    "mode": "hybrid"
  }
}
```
- Reads `SOCIAL_ID` and `CHANNEL_ID` from database
- Uses fixed `CITIZEN_ID` for consistent testing
- Useful for testing Steps 3 and 4 with known data

## Simplified Step Structure

### Before (Complex)
```json
{
  "step_id": 1,
  "step_name": "get_bearer_token",
  "title": "Get Bearer Token",
  "description": "Retrieve authentication token from TPA API",
  "sequence_order": 1,
  "dependencies": [],
  "api_configuration": {
    "endpoint": "https://...",
    "method": "POST",
    "headers": {"Content-Type": "application/json"},
    "request_body": {...},
    "timeout_seconds": 30
  },
  "response_handling": {
    "expected_format": "string",
    "success_indicators": [...],
    "data_extraction": {
      "bearer_token": {
        "path": "$",
        "transform": "remove_quotes",
        "store_for_subsequent_steps": true,
        "description": "JWT token for API authentication"
      }
    }
  },
  "error_handling": {
    "retry_attempts": 3,
    "retry_delay_seconds": 5,
    "failure_action": "abort_workflow"
  }
}
```

### After (Simplified)
```json
{
  "id": 1,
  "name": "get_bearer_token",
  "endpoint": "https://...",
  "method": "POST",
  "request": {...},
  "extract": {
    "bearer_token": "$"
  },
  "retry": 3
}
```

## Template Variables Simplified

### Before (Complex)
- `{{database.social_id}}`
- `{{step_1.bearer_token}}`
- `{{step_2.citizen_id}}`
- `{{iteration.current_member_code}}`

### After (Simple)
- `{{social_id}}`
- `{{bearer_token}}`
- `{{citizen_id}}`
- `{{member_code}}`

## File Structure

```
src/lib/api/features/customer/
├── policy-claims-workflow.json          # Main configuration (95 lines)
├── policy-claims-workflow.types.ts      # TypeScript interfaces
├── policy-claims-workflow.utils.ts      # Utility functions
├── policy-claims-workflow.example.ts    # Usage examples
├── policy-claims-workflow.md           # Documentation
└── SIMPLIFIED-WORKFLOW-SUMMARY.md      # This summary
```

## Backend Implementation Requirements

### Minimal Implementation
1. **Load JSON configuration**
2. **Determine mode** (`database`, `fixed`, or `hybrid`)
3. **Resolve data sources** based on mode
4. **Execute 4 steps sequentially** with template substitution
5. **Handle retries** as specified
6. **Support iteration** for Step 4 (member_codes)
7. **Store results** in CustomerPolicies table

### Key Implementation Points

#### Mode Resolution
```typescript
if (config.mode === 'fixed') {
  // Use config.fixed_values directly
} else if (config.mode === 'hybrid') {
  // Read social_id/channel_id from DB, use fixed citizen_id
} else {
  // Database mode: read all from DB, get citizen_id from Step 2
}
```

#### Template Variable Substitution
```typescript
// Simple string replacement
request_body = JSON.stringify(step.request)
  .replace(/\{\{social_id\}\}/g, resolved_social_id)
  .replace(/\{\{bearer_token\}\}/g, step1_response)
  // etc.
```

#### Step 4 Iteration
```typescript
// Step 4 iterates over member_codes from Step 3
const member_codes = step3_response.member_codes;
const all_details = [];

for (const member_code of member_codes) {
  const request = step.request.replace('{{member_code}}', member_code);
  const response = await api_call(request);
  all_details.push(response);
}
```

## Benefits of Simplification

1. **Easier to Implement**: 70% less configuration complexity
2. **Flexible Testing**: Three deployment modes for different scenarios
3. **Cleaner Code**: Simplified template variables and structure
4. **Faster Development**: Fixed values mode bypasses database dependencies
5. **Maintainable**: Essential functionality preserved without bloat

## Migration from Original

The simplified version maintains 100% functional compatibility with the original Svelte component workflow while being much easier for backend developers to implement and understand.
