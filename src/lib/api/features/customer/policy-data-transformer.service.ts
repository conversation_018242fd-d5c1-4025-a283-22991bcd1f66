/**
 * Policy Data Transformer Service
 * 
 * Transforms TPA API responses to internal Policy and Claim types
 * Handles data validation, sanitization, and statistics calculation
 */

import type {
  CustomerPoliciesData,
  Policy,
  Claim,
  PolicyStatistics,
  PolicyProduct,
  PolicyStatus,
  PolicyType,
  ClaimStatus,
  ClaimType
} from '$lib/types/customer';

import type { WorkflowExecutionContext } from './policy-claims-workflow.types';

export class PolicyDataTransformer {
  
  /**
   * Transform workflow execution results to CustomerPoliciesData format
   */
  transformToCustomerPoliciesData(
    context: WorkflowExecutionContext,
    customerId: number,
    customerName?: string,
    customerEmail?: string
  ): CustomerPoliciesData {
    console.log('Transforming workflow results to CustomerPoliciesData', {
      execution_id: context.execution_id,
      customer_id: customerId
    });

    // Extract raw data from workflow context with correct API response structure
    const rawPolicies = context.step_data.policies || [];
    const rawPolicyDetails = context.step_data.policy_details || [];
    const rawClaimsData = context.step_data.claims_data || [];
    // const rawPolicies = context.step_data.policies?.ListOfPolicyListSocial || [];
    // const rawPolicyDetails = context.step_data.policy_details?.ListOfPolDet || [];
    // const rawClaimsData = context.step_data.claims_data?.ListOfPolClaim || [];

    console.log('Raw policies:', rawPolicies);
    console.log('Raw policy details:', rawPolicyDetails);
    console.log('Raw claims data:', rawClaimsData);

    // Transform policies and claims
    const policies = this.transformPolicies(rawPolicies, rawPolicyDetails);
    const claims = this.transformClaims(rawClaimsData);

    // Calculate statistics
    const statistics = this.calculateStatistics(policies, claims);

    return {
      customer_id: customerId,
      customer_name: customerName || `Customer ${customerId}`,
      customer_email: customerEmail || `customer${customerId}@example.com`,
      policies,
      claims,
      statistics,
      last_updated: new Date().toISOString()
    };
  }

  /**
   * Transform TPA policy data to internal Policy type
   */
  private transformPolicies(rawPolicies: any[], rawPolicyDetails: any[]): Policy[] {
    const policies: Policy[] = [];

    for (const rawPolicy of rawPolicies) {
      try {
        // Find corresponding policy details
        const policyDetails = rawPolicyDetails.find(
          detail => detail.PolNo === rawPolicy.PolNo || detail.MemberCode === rawPolicy.MemberCode
        );

        const policy = this.mapPolicyFields(rawPolicy, policyDetails);
        policies.push(policy);
      } catch (error) {
        console.warn('Failed to transform policy:', rawPolicy, error);
      }
    }

    return policies;
  }

  /**
   * Transform TPA claims data to internal Claim type
   */
  private transformClaims(rawClaimsData: any[]): Claim[] {
    const claims: Claim[] = [];

    for (const rawClaim of rawClaimsData) {
      try {
        const claim = this.mapClaimFields(rawClaim);
        claims.push(claim);
      } catch (error) {
        console.warn('Failed to transform claim:', rawClaim, error);
      }
    }

    return claims;
  }

  /**
   * Map TPA policy fields to internal Policy type
   */
  private mapPolicyFields(rawPolicy: any, policyDetails?: any): Policy {
    // Create policy product name from Name and Surname
    const fullName = `${rawPolicy.Name || ''} ${rawPolicy.Surname || ''}`.trim();
    const productName = rawPolicy.PlanName || fullName || 'Unknown Product';

    // Create policy product
    const product: PolicyProduct = {
      id: this.generateId(),
      name: productName,
      product_type: this.determinePolicyType(productName),
      description: policyDetails?.MainBenefit,
      category: rawPolicy.CardType || 'Insurance',
      provider: rawPolicy.InsurerName || rawPolicy.InsurerNameEN || 'TPA',
      coverage_details: this.extractCoverageDetails(policyDetails),
      exclusions: []
    };

    // Determine policy status based on dates and available data
    const policyStatus = this.determinePolicyStatus(rawPolicy, policyDetails);

    // Parse dates with DD/MM/YYYY format
    const startDate = this.parseDate(rawPolicy.EffFrom);
    const endDate = this.parseDate(rawPolicy.EffTo);
    const issueDate = startDate; // Use start date as issue date if not provided

    return {
      id: this.generateId(),
      policy_number: rawPolicy.PolNo || rawPolicy.CertificateNo || '',
      customer_id: 0, // Will be set by the calling service

      // Product Information
      product,

      // Policy Details
      policy_status: policyStatus,
      issue_date: issueDate,
      start_date: startDate,
      end_date: endDate,
      renewal_date: undefined, // Not provided in API response

      // Financial Information - not available in current API response
      premium_amount: 0, // Not provided in policy list API
      coverage_amount: 0, // Not provided in policy list API
      deductible: undefined,
      currency: 'THB',
      payment_frequency: 'ANNUAL' as const,
      next_payment_date: undefined,
      insurer: rawPolicy.InsurerName || rawPolicy.InsurerNameEN || 'TPA',
      plan_code: rawPolicy.PlanCode || '',
      plan_name: rawPolicy.PlanName || '',

      // Beneficiaries and Coverage
      beneficiaries: undefined,
      coverage_details: this.buildCoverageDetails(rawPolicy, policyDetails),

      // Documents
      documents: [],

      // Metadata
      notes: '',
      created_on: new Date().toISOString(),
      updated_on: new Date().toISOString()
    };
  }

  /**
   * Map TPA claim fields to internal Claim type
   */
  private mapClaimFields(rawClaim: any): Claim {
    const claimStatus = this.determineClaimStatus(rawClaim.ClmStatus);
    const claimType = this.determineClaimType(rawClaim.ClmType);

    return {
      id: this.generateId(),
      claim_number: rawClaim.ClmNo || '',
      policy_id: 0, // Will be linked by policy number
      policy_number: rawClaim.ClmPolNo || '',
      customer_id: 0, // Will be set by the calling service

      // Claim Details
      claim_type: claimType,
      claim_status: claimStatus,
      incident_date: this.parseDate(rawClaim.ClmVisitDate),
      reported_date: this.parseDate(rawClaim.ClmVisitDate), // Using visit date as reported date

      // Financial Information
      claimed_amount: this.parseAmount(rawClaim.ClmIncurredAmt) || 0,
      approved_amount: this.parseAmount(rawClaim.ClmPayable),
      paid_amount: rawClaim.ClmPaymentDate ? this.parseAmount(rawClaim.ClmPayable) : undefined,
      currency: 'THB',

      // Claim Information
      description: rawClaim.ClmDiagTH || rawClaim.ClmDiagEN || '',
      incident_location: rawClaim.ClmProviderTH || rawClaim.ClmProviderEN,
      cause_of_claim: rawClaim.ClmDiagTH || rawClaim.ClmDiagEN,

      // Processing Information
      assigned_adjuster: undefined, // Not provided in API response
      estimated_settlement_date: undefined,
      settlement_date: this.parseDate(rawClaim.ClmPaymentDate),

      // Documents and History
      documents: [],
      status_history: [],

      // Metadata
      notes: `Provider: ${rawClaim.ClmProviderTH || rawClaim.ClmProviderEN || ''}, Source: ${rawClaim.ClmSource || ''}`,
      created_on: new Date().toISOString(),
      updated_on: new Date().toISOString()
    };
  }

  /**
   * Calculate statistics from policies and claims data
   */
  private calculateStatistics(policies: Policy[], claims: Claim[]): PolicyStatistics {
    const now = new Date();
    const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);

    // Policy statistics
    const totalPolicies = policies.length;
    const activePolicies = policies.filter(p => p.policy_status === 'ACTIVE').length;
    const expiredPolicies = policies.filter(p => p.policy_status === 'EXPIRED').length;
    const pendingPolicies = policies.filter(p => p.policy_status === 'PENDING').length;
    const cancelledPolicies = policies.filter(p => p.policy_status === 'CANCELLED').length;
    const waitingPeriodPolicies = policies.filter(p => p.policy_status === 'WAITING_PERIOD').length;
    
    // Nearly expired policies (within 30 days)
    const nearlyExpiredPolicies = policies.filter(p => {
      if (!p.end_date) return false;
      const endDate = new Date(p.end_date);
      const daysUntilExpiry = (endDate.getTime() - now.getTime()) / (24 * 60 * 60 * 1000);
      return daysUntilExpiry > 0 && daysUntilExpiry <= 30;
    }).length;

    // Financial statistics
    const totalPremiumAmount = policies.reduce((sum, p) => sum + (p.premium_amount || 0), 0);
    const totalCoverageAmount = policies.reduce((sum, p) => sum + (p.coverage_amount || 0), 0);
    const averagePremium = totalPolicies > 0 ? totalPremiumAmount / totalPolicies : 0;

    // Claims statistics
    const totalClaims = claims.length;
    const activeClaims = claims.filter(c => ['SUBMITTED', 'UNDER_REVIEW', 'PENDING_DOCUMENTS'].includes(c.claim_status)).length;
    const approvedClaims = claims.filter(c => ['APPROVED', 'PAID'].includes(c.claim_status)).length;
    const rejectedClaims = claims.filter(c => c.claim_status === 'REJECTED').length;
    const totalClaimsAmount = claims.reduce((sum, c) => sum + (c.claimed_amount || 0), 0);
    const totalPaidAmount = claims.reduce((sum, c) => sum + (c.paid_amount || 0), 0);

    // Policy type breakdown
    const policyTypeBreakdown: Record<PolicyType, number> = {
      LIFE: 0,
      HEALTH: 0,
      AUTO: 0,
      PROPERTY: 0,
      TRAVEL: 0,
      DISABILITY: 0,
      CRITICAL_ILLNESS: 0
    };

    policies.forEach(policy => {
      policyTypeBreakdown[policy.product.product_type]++;
    });

    // Recent activity (last 30 days)
    const recentPolicies = policies.filter(p => {
      const issueDate = new Date(p.issue_date);
      return issueDate >= thirtyDaysAgo;
    }).length;

    const recentClaims = claims.filter(c => {
      const claimDate = new Date(c.reported_date);
      return claimDate >= thirtyDaysAgo;
    }).length;

    return {
      total_policies: totalPolicies,
      active_policies: activePolicies,
      expired_policies: expiredPolicies,
      pending_policies: pendingPolicies,
      cancelled_policies: cancelledPolicies,
      waiting_period_policies: waitingPeriodPolicies,
      nearly_expired_policies: nearlyExpiredPolicies,
      total_premium_amount: totalPremiumAmount,
      total_coverage_amount: totalCoverageAmount,
      average_premium: averagePremium,
      total_claims: totalClaims,
      active_claims: activeClaims,
      approved_claims: approvedClaims,
      rejected_claims: rejectedClaims,
      total_claims_amount: totalClaimsAmount,
      total_paid_amount: totalPaidAmount,
      policy_type_breakdown: policyTypeBreakdown,
      recent_policies: recentPolicies,
      recent_claims: recentClaims
    };
  }

  // Utility methods for data transformation

  private generateId(): number {
    return Math.floor(Math.random() * 1000000) + Date.now();
  }

  private parseDate(dateString: string | undefined): string {
    if (!dateString) return new Date().toISOString();

    try {
      // Handle DD/MM/YYYY format from TPA API
      if (dateString.includes('/')) {
        const [day, month, year] = dateString.split('/');
        const date = new Date(parseInt(year), parseInt(month) - 1, parseInt(day));
        return isNaN(date.getTime()) ? new Date().toISOString() : date.toISOString();
      }

      // Handle other date formats
      const date = new Date(dateString);
      return isNaN(date.getTime()) ? new Date().toISOString() : date.toISOString();
    } catch {
      return new Date().toISOString();
    }
  }

  private parseAmount(amountString: string | number | undefined): number | undefined {
    if (typeof amountString === 'number') return amountString;
    if (!amountString) return undefined;
    
    const parsed = parseFloat(String(amountString).replace(/[^\d.-]/g, ''));
    return isNaN(parsed) ? undefined : parsed;
  }

  private determinePolicyType(productName: string | undefined): PolicyType {
    if (!productName) return 'HEALTH';
    
    const name = productName.toLowerCase();
    if (name.includes('life')) return 'LIFE';
    if (name.includes('health') || name.includes('medical')) return 'HEALTH';
    if (name.includes('auto') || name.includes('car') || name.includes('vehicle')) return 'AUTO';
    if (name.includes('property') || name.includes('home')) return 'PROPERTY';
    if (name.includes('travel')) return 'TRAVEL';
    if (name.includes('disability')) return 'DISABILITY';
    if (name.includes('critical') || name.includes('illness')) return 'CRITICAL_ILLNESS';
    
    return 'HEALTH'; // Default
  }

  private determinePolicyStatus(rawPolicy: any, policyDetails?: any): PolicyStatus {
    const now = new Date();
    const endDate = new Date(rawPolicy.EffTo);
    
    if (policyDetails?.Status) {
      const status = policyDetails.Status.toLowerCase();
      if (status.includes('active')) return 'ACTIVE';
      if (status.includes('expired')) return 'EXPIRED';
      if (status.includes('cancelled')) return 'CANCELLED';
      if (status.includes('pending')) return 'PENDING';
      if (status.includes('suspended')) return 'SUSPENDED';
    }
    
    // Determine status based on dates
    if (endDate < now) return 'EXPIRED';
    return 'ACTIVE';
  }



  private determineClaimStatus(status: string | undefined): ClaimStatus {
    if (!status) return 'SUBMITTED';

    const statusLower = status.toLowerCase();
    // Handle actual API status values
    if (statusLower === 'open') return 'UNDER_REVIEW';
    if (statusLower === 'closed') return 'CLOSED';
    if (statusLower === 'paid') return 'PAID';
    if (statusLower === 'approved') return 'APPROVED';
    if (statusLower === 'rejected' || statusLower === 'denied') return 'REJECTED';
    if (statusLower.includes('submit')) return 'SUBMITTED';
    if (statusLower.includes('review') || statusLower.includes('process')) return 'UNDER_REVIEW';
    if (statusLower.includes('pending') || statusLower.includes('document')) return 'PENDING_DOCUMENTS';

    return 'SUBMITTED';
  }

  private determineClaimType(type: string | undefined): ClaimType {
    if (!type) return 'MEDICAL';

    const typeLower = type.toLowerCase();
    // Handle actual API claim type values
    if (typeLower === 'accident') return 'ACCIDENT';
    if (typeLower === 'illness') return 'MEDICAL';
    if (typeLower.includes('death')) return 'DEATH';
    if (typeLower.includes('medical') || typeLower.includes('health')) return 'MEDICAL';
    if (typeLower.includes('property') || typeLower.includes('damage')) return 'PROPERTY_DAMAGE';
    if (typeLower.includes('theft')) return 'THEFT';
    if (typeLower.includes('disability')) return 'DISABILITY';
    if (typeLower.includes('critical') || typeLower.includes('illness')) return 'CRITICAL_ILLNESS';
    if (typeLower.includes('hospital')) return 'HOSPITALIZATION';

    return 'MEDICAL';
  }

  /**
   * Extract coverage details from policy details structure
   */
  private extractCoverageDetails(policyDetails?: any): string[] {
    if (!policyDetails?.Coverage || !Array.isArray(policyDetails.Coverage)) {
      return [];
    }

    return policyDetails.Coverage
      .filter((cov: any) => cov.CovDesc || cov.CovDescEN)
      .map((cov: any) => cov.CovDesc || cov.CovDescEN);
  }

  /**
   * Build coverage details object from policy data
   */
  private buildCoverageDetails(rawPolicy: any, policyDetails?: any): Record<string, any> {
    const details: Record<string, any> = {};

    // Add benefit information from policy
    if (rawPolicy.EvenMBAccident) {
      details.accident_benefits = rawPolicy.EvenMBAccident.split('|');
    }
    if (rawPolicy.EvenMBIllness) {
      details.illness_benefits = rawPolicy.EvenMBIllness.split('|');
    }

    // Add coverage details from policy details
    if (policyDetails?.Coverage && Array.isArray(policyDetails.Coverage)) {
      details.coverage_items = policyDetails.Coverage.map((cov: any) => ({
        number: cov.CovNo,
        description: cov.CovDesc || cov.CovDescEN,
        limit: cov.CovLimit,
        utilized: cov.CovUtilized || cov.CovUtilizedEN
      }));
    }

    return details;
  }
}
