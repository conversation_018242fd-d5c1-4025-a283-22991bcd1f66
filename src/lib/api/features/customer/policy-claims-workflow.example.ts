/**
 * Example usage of the simplified Policy Claims API Workflow Configuration
 */

import { 
  loadWorkflowConfig, 
  validateWorkflowConfig, 
  getWorkflowSummary,
  resolveDataSource,
  createExecutionContext
} from './policy-claims-workflow.utils';

/**
 * Example: Load and validate the workflow configuration
 */
export function exampleLoadAndValidate() {
  // Load the configuration
  const config = loadWorkflowConfig();
  
  // Validate the configuration
  const validation = validateWorkflowConfig(config);
  
  if (!validation.valid) {
    console.error('Configuration validation failed:', validation.errors);
    return null;
  }
  
  if (validation.warnings.length > 0) {
    console.warn('Configuration warnings:', validation.warnings);
  }
  
  console.log('Configuration loaded successfully:', getWorkflowSummary(config));
  return config;
}

/**
 * Example: Resolve data source based on configuration mode
 */
export function exampleResolveDataSource() {
  const config = loadWorkflowConfig();
  const dataSource = resolveDataSource(config);
  
  console.log(`Configuration mode: ${config.config.mode}`);
  console.log('Data source resolution:', dataSource);
  
  return dataSource;
}

/**
 * Example: Create execution context for a customer
 */
export function exampleCreateExecutionContext(customerId: string) {
  const context = createExecutionContext(customerId);
  
  console.log('Execution context created:', {
    customer_id: context.customer_id,
    execution_id: context.execution_id,
    started_at: context.started_at
  });
  
  return context;
}

/**
 * Example: Simulate workflow execution steps
 */
export function exampleWorkflowExecution(customerId: string) {
  const config = loadWorkflowConfig();
  const context = createExecutionContext(customerId);
  
  console.log('=== Policy Claims Workflow Execution Example ===');
  console.log(`Customer ID: ${customerId}`);
  console.log(`Execution ID: ${context.execution_id}`);
  console.log(`Configuration Mode: ${config.config.mode}`);
  
  // Simulate step execution
  config.steps.forEach((step, index) => {
    console.log(`\n--- Step ${step.id}: ${step.name} ---`);
    console.log(`Endpoint: ${step.endpoint}`);
    console.log(`Method: ${step.method}`);
    
    // Show request template
    console.log('Request template:', JSON.stringify(step.request, null, 2));
    
    // Show what data will be extracted
    console.log('Will extract:', Object.keys(step.extract));
    
    // Show retry configuration
    console.log(`Retry attempts: ${step.retry}`);
    
    // Show iteration info for step 4
    if (step.iterate_over) {
      console.log(`Will iterate over: ${step.iterate_over}`);
    }
    
    // Show validation for step 2
    if (step.validate) {
      console.log(`Validation: ${step.validate}`);
    }
  });
  
  console.log('\n--- Storage Configuration ---');
  console.log(`Table: ${config.storage.table}`);
  console.log(`Primary Key: ${config.storage.key.join(', ')}`);
  console.log('Data mapping:', Object.keys(config.storage.data));
  
  console.log('\n--- Options ---');
  console.log(`Timeout: ${config.options.timeout_minutes} minutes`);
  console.log(`Retry delay: ${config.options.retry_delay_seconds} seconds`);
}

/**
 * Example: Show different configuration modes
 */
export function exampleConfigurationModes() {
  const config = loadWorkflowConfig();
  
  console.log('=== Configuration Mode Examples ===');
  
  // Show current mode
  console.log(`\nCurrent mode: ${config.config.mode}`);
  
  // Show fixed values available
  console.log('\nFixed values available:');
  console.log(`- SOCIAL_ID: ${config.config.fixed_values.social_id}`);
  console.log(`- CHANNEL_ID: ${config.config.fixed_values.channel_id}`);
  console.log(`- CITIZEN_ID: ${config.config.fixed_values.citizen_id}`);
  
  // Show database configuration
  console.log('\nDatabase configuration:');
  console.log(`- Table: ${config.config.database.table}`);
  console.log(`- Fields: ${JSON.stringify(config.config.database.fields)}`);
  console.log(`- Where clause: ${config.config.database.where}`);
  
  // Show how to change modes (for documentation purposes)
  console.log('\nTo change modes, update the "mode" field in the configuration:');
  console.log('- "database": Read from customer_customerplatformidentity table');
  console.log('- "fixed": Use hardcoded values for testing');
  console.log('- "hybrid": Mix of database and fixed values');
}

/**
 * Example: Template variable resolution simulation
 */
export function exampleTemplateVariables() {
  const config = loadWorkflowConfig();
  
  console.log('=== Template Variable Examples ===');
  
  // Show template variables used in each step
  config.steps.forEach(step => {
    console.log(`\nStep ${step.id} (${step.name}) uses these template variables:`);
    
    // Extract variables from request
    const requestStr = JSON.stringify(step.request);
    const variables = requestStr.match(/\{\{([^}]+)\}\}/g) || [];
    
    variables.forEach(variable => {
      console.log(`- ${variable}`);
    });
    
    // Show headers if they contain variables
    if (step.headers) {
      const headersStr = JSON.stringify(step.headers);
      const headerVariables = headersStr.match(/\{\{([^}]+)\}\}/g) || [];
      if (headerVariables.length > 0) {
        console.log('Header variables:');
        headerVariables.forEach(variable => {
          console.log(`- ${variable}`);
        });
      }
    }
  });
  
  console.log('\nStorage template variables:');
  const storageStr = JSON.stringify(config.storage.data);
  const storageVariables = storageStr.match(/\{\{([^}]+)\}\}/g) || [];
  storageVariables.forEach(variable => {
    console.log(`- ${variable}`);
  });
}

// Export all examples for easy testing
export const examples = {
  loadAndValidate: exampleLoadAndValidate,
  resolveDataSource: exampleResolveDataSource,
  createExecutionContext: exampleCreateExecutionContext,
  workflowExecution: exampleWorkflowExecution,
  configurationModes: exampleConfigurationModes,
  templateVariables: exampleTemplateVariables
};
