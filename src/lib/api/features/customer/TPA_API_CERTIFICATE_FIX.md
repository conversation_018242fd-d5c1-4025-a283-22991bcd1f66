# TPA API Certificate Issue Fix

## Problem
The TPA API endpoints at `https://uat.thirdpartyadmin.co.th:4443/TPA.TMS.Web.API_PREPROV2/` were returning certificate validation errors:

```
POST https://uat.thirdpartyadmin.co.th:4443/TPA.TMS.Web.API_PREPROV2/api/GetToken net::ERR_CERT_DATE_INVALID
```

This error indicates that the SSL certificate for the UAT environment has an invalid date (likely expired or not yet valid).

## Solution
Since this is a browser-based SvelteKit application, we cannot directly disable SSL certificate validation like in Node.js applications. Instead, we implemented a proxy-based solution:

### 1. Vite Development Proxy
Added a proxy configuration in `vite.config.ts` to handle TPA API requests:

```typescript
'/api/tpa': {
    target: 'https://uat.thirdpartyadmin.co.th:4443/TPA.TMS.Web.API_PREPROV2',
    changeOrigin: true,
    secure: false,  // This bypasses certificate validation
    rewrite: (path) => path.replace(/^\/api\/tpa/, '')
}
```

### 2. Updated Configuration
Modified `policy-claims-workflow.json` to use proxy endpoints in development:

- **Before**: `https://uat.thirdpartyadmin.co.th:4443/TPA.TMS.Web.API_PREPROV2/api/GetToken`
- **After**: `/api/tpa/api/GetToken`

### 3. Environment-Aware Endpoint Resolution
Enhanced `policy-claims-workflow.utils.ts` with `resolveEndpointForEnvironment()` function that:

- In **development**: Converts TPA endpoints to use the local proxy (`/api/tpa`)
- In **production**: Uses direct endpoints (assuming valid certificates)

## How It Works

1. **Development Environment**:
   - Application makes request to `/api/tpa/api/GetToken`
   - Vite dev server proxy intercepts the request
   - Proxy forwards to `https://uat.thirdpartyadmin.co.th:4443/TPA.TMS.Web.API_PREPROV2/api/GetToken`
   - Proxy uses `secure: false` to bypass certificate validation
   - Response is returned to the application

2. **Production Environment**:
   - Application uses direct endpoints
   - Assumes production endpoints have valid certificates

## Files Modified

1. **vite.config.ts**: Added TPA API proxy configuration
2. **policy-claims-workflow.json**: Updated endpoints to use proxy paths
3. **policy-claims-workflow.utils.ts**: Added environment-aware endpoint resolution
4. **policy-workflow-executor.service.ts**: Enhanced logging for debugging

## Testing
To test the fix:

1. Start the development server: `npm run dev`
2. Trigger a policy workflow execution
3. Check browser network tab - requests should go to `/api/tpa/api/*` endpoints
4. Check console logs for successful API responses

## Production Deployment
For production deployment, ensure:

1. The production TPA API endpoints have valid SSL certificates
2. Update the configuration to use production endpoints if different from UAT
3. Consider implementing proper certificate validation in production

## Environment Variables (Optional)
For additional flexibility, you can add environment variables:

```env
# Development
TPA_API_BASE_URL=/api/tpa

# Production  
TPA_API_BASE_URL=https://prod.thirdpartyadmin.co.th:4443/TPA.TMS.Web.API_PREPROV2
```

## Security Considerations
- The `secure: false` setting is only used in development
- Production should always use valid certificates
- Monitor certificate expiration dates to prevent future issues
- Consider implementing certificate validation alerts
