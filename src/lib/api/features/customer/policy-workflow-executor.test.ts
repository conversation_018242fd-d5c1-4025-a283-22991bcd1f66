/**
 * Unit tests for PolicyWorkflowExecutor
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { PolicyWorkflowExecutor } from './policy-workflow-executor.service';
import type { WorkflowExecutionContext } from './policy-claims-workflow.types';

// Mock the workflow configuration
vi.mock('./policy-claims-workflow.utils', () => ({
  loadWorkflowConfig: vi.fn(() => ({
    name: 'Policy Claims API Integration',
    version: '1.0.0',
    config: {
      mode: 'fixed_values',
      fixed_values: {
        social_id: 'test_social_id',
        channel_id: 'test_channel_id',
        citizen_id: 'test_citizen_id'
      }
    },
    steps: [
      {
        id: 1,
        name: 'get_bearer_token',
        endpoint: 'https://test.api.com/GetToken',
        method: 'POST',
        request: {
          USERNAME: 'BVTPA',
          PASSWORD: '*d!n^+Cb@1',
          SOCIAL_ID: '{{social_id}}',
          CHANNEL_ID: '{{channel_id}}',
          CHANNEL: 'LINE'
        },
        extract: {
          bearer_token: '$'
        },
        retry: 3
      },
      {
        id: 2,
        name: 'verify_citizen_id',
        endpoint: 'https://test.api.com/SearchCitizenID',
        method: 'POST',
        headers: {
          Authorization: 'Bearer {{bearer_token}}'
        },
        request: {
          SOCIAL_ID: '{{social_id}}',
          CHANNEL_ID: '{{channel_id}}',
          CHANNEL: 'LINE'
        },
        extract: {
          citizen_id: '$.ListOfSearchCitizenID[0].CitizenID'
        },
        validate: "$.ListOfSearchCitizenID[0].Status == '1'",
        retry: 2
      }
    ],
    options: {
      timeout_minutes: 10,
      retry_delay_seconds: 5
    }
  })),
  createExecutionContext: vi.fn((customerId: string) => ({
    customer_id: customerId,
    execution_id: `exec_test_${customerId}`,
    started_at: new Date(),
    current_step: 0,
    step_data: {},
    database_data: {}
  })),
  resolveTemplateVariables: vi.fn((text: string, context: WorkflowExecutionContext) => {
    return text
      .replace('{{social_id}}', context.database_data.social_id || 'test_social_id')
      .replace('{{channel_id}}', context.database_data.channel_id || 'test_channel_id')
      .replace('{{bearer_token}}', context.step_data.bearer_token || 'test_token');
  }),
  getStepsInOrder: vi.fn((config) => config.steps),
  resolveDataSource: vi.fn(() => ({
    social_id: 'test_social_id',
    channel_id: 'test_channel_id',
    citizen_id: 'test_citizen_id'
  }))
}));

// Mock the data transformer
vi.mock('./policy-data-transformer.service', () => ({
  PolicyDataTransformer: vi.fn().mockImplementation(() => ({
    transformToCustomerPoliciesData: vi.fn(() => ({
      customer_id: 123,
      customer_name: 'Test Customer',
      customer_email: '<EMAIL>',
      policies: [],
      claims: [],
      statistics: {
        total_policies: 0,
        active_policies: 0,
        expired_policies: 0,
        pending_policies: 0,
        cancelled_policies: 0,
        waiting_period_policies: 0,
        nearly_expired_policies: 0,
        total_premium_amount: 0,
        total_coverage_amount: 0,
        average_premium: 0,
        total_claims: 0,
        active_claims: 0,
        approved_claims: 0,
        rejected_claims: 0,
        total_claims_amount: 0,
        total_paid_amount: 0,
        policy_type_breakdown: {
          LIFE: 0,
          HEALTH: 0,
          AUTO: 0,
          PROPERTY: 0,
          TRAVEL: 0,
          DISABILITY: 0,
          CRITICAL_ILLNESS: 0
        },
        recent_policies: 0,
        recent_claims: 0
      },
      last_updated: new Date().toISOString()
    }))
  }))
}));

describe('PolicyWorkflowExecutor', () => {
  let executor: PolicyWorkflowExecutor;
  let fetchMock: any;

  beforeEach(() => {
    executor = new PolicyWorkflowExecutor();
    
    // Mock fetch globally
    fetchMock = vi.fn();
    global.fetch = fetchMock;
    
    // Mock AbortSignal.timeout
    global.AbortSignal = {
      timeout: vi.fn(() => ({
        aborted: false,
        addEventListener: vi.fn(),
        removeEventListener: vi.fn()
      }))
    } as any;
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('executeWorkflow', () => {
    it('should execute workflow successfully with valid responses', async () => {
      // Mock successful API responses
      fetchMock
        .mockResolvedValueOnce({
          ok: true,
          text: () => Promise.resolve('"test_bearer_token"')
        })
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({
            ListOfSearchCitizenID: [
              { Status: '1', CitizenID: 'test_citizen_id' }
            ]
          })
        });

      const result = await executor.executeWorkflow('123');

      expect(result).toBeDefined();
      expect(result.customer_id).toBe(123);
      expect(result.customer_name).toBe('Test Customer');
      expect(fetchMock).toHaveBeenCalledTimes(2);
    });

    it('should handle API errors and throw appropriate error', async () => {
      // Mock failed API response
      fetchMock.mockResolvedValueOnce({
        ok: false,
        status: 500,
        statusText: 'Internal Server Error'
      });

      await expect(executor.executeWorkflow('123')).rejects.toThrow();
      expect(fetchMock).toHaveBeenCalledTimes(1);
    });

    it('should retry failed requests according to configuration', async () => {
      // Mock failed requests followed by success
      fetchMock
        .mockResolvedValueOnce({
          ok: false,
          status: 500,
          statusText: 'Internal Server Error'
        })
        .mockResolvedValueOnce({
          ok: false,
          status: 500,
          statusText: 'Internal Server Error'
        })
        .mockResolvedValueOnce({
          ok: true,
          text: () => Promise.resolve('"test_bearer_token"')
        })
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({
            ListOfSearchCitizenID: [
              { Status: '1', CitizenID: 'test_citizen_id' }
            ]
          })
        });

      const result = await executor.executeWorkflow('123');

      expect(result).toBeDefined();
      expect(fetchMock).toHaveBeenCalledTimes(4); // 3 attempts for first step + 1 for second step
    });

    it('should validate citizen ID verification response', async () => {
      fetchMock
        .mockResolvedValueOnce({
          ok: true,
          text: () => Promise.resolve('"test_bearer_token"')
        })
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({
            ListOfSearchCitizenID: [
              { Status: '0', CitizenID: 'test_citizen_id' } // Invalid status
            ]
          })
        });

      await expect(executor.executeWorkflow('123')).rejects.toThrow('Citizen ID verification failed');
    });

    it('should handle missing citizen ID search results', async () => {
      fetchMock
        .mockResolvedValueOnce({
          ok: true,
          text: () => Promise.resolve('"test_bearer_token"')
        })
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({
            ListOfSearchCitizenID: [] // Empty results
          })
        });

      await expect(executor.executeWorkflow('123')).rejects.toThrow('No citizen ID search results found');
    });
  });

  describe('template variable resolution', () => {
    it('should resolve template variables in request body', async () => {
      fetchMock.mockResolvedValueOnce({
        ok: true,
        text: () => Promise.resolve('"test_bearer_token"')
      });

      try {
        await executor.executeWorkflow('123');
      } catch (error) {
        // Expected to fail at step 2, but we're testing step 1 request formation
      }

      // Verify that the request was made with resolved template variables
      const firstCall = fetchMock.mock.calls[0];
      const requestBody = JSON.parse(firstCall[1].body);
      
      expect(requestBody.SOCIAL_ID).toBe('test_social_id');
      expect(requestBody.CHANNEL_ID).toBe('test_channel_id');
    });
  });

  describe('data extraction', () => {
    it('should extract bearer token from string response', async () => {
      fetchMock
        .mockResolvedValueOnce({
          ok: true,
          text: () => Promise.resolve('"extracted_token"')
        })
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({
            ListOfSearchCitizenID: [
              { Status: '1', CitizenID: 'extracted_citizen_id' }
            ]
          })
        });

      await executor.executeWorkflow('123');

      // Verify that the second request uses the extracted token
      const secondCall = fetchMock.mock.calls[1];
      expect(secondCall[1].headers.Authorization).toBe('Bearer extracted_token');
    });
  });

  describe('error handling', () => {
    it('should handle network timeouts gracefully', async () => {
      fetchMock.mockRejectedValueOnce(new Error('Network timeout'));

      await expect(executor.executeWorkflow('123')).rejects.toThrow();
    });

    it('should handle malformed JSON responses', async () => {
      fetchMock
        .mockResolvedValueOnce({
          ok: true,
          text: () => Promise.resolve('"test_bearer_token"')
        })
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.reject(new Error('Invalid JSON'))
        });

      await expect(executor.executeWorkflow('123')).rejects.toThrow();
    });
  });
});
