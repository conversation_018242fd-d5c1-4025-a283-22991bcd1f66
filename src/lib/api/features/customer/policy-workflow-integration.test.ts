/**
 * Integration tests for Policy Workflow API Integration
 * Tests the complete workflow from service call to component integration
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { PolicyWorkflowExecutor } from './policy-workflow-executor.service';
import { PolicyDataTransformer } from './policy-data-transformer.service';
import { CustomersService } from './customers.service';

// Mock fetch globally
const mockFetch = vi.fn();
global.fetch = mockFetch;

// Mock AbortSignal.timeout
global.AbortSignal = {
  timeout: vi.fn(() => ({
    aborted: false,
    addEventListener: vi.fn(),
    removeEventListener: vi.fn()
  }))
} as any;

// Mock workflow configuration
vi.mock('./policy-claims-workflow.utils', () => ({
  loadWorkflowConfig: vi.fn(() => ({
    name: 'Policy Claims API Integration',
    version: '1.0.0',
    config: {
      mode: 'fixed_values',
      fixed_values: {
        social_id: 'test_social_id',
        channel_id: 'test_channel_id',
        citizen_id: 'test_citizen_id'
      }
    },
    steps: [
      {
        id: 1,
        name: 'get_bearer_token',
        endpoint: 'https://test.api.com/GetToken',
        method: 'POST',
        request: {
          USERNAME: 'BVTPA',
          PASSWORD: '*d!n^+Cb@1',
          SOCIAL_ID: '{{social_id}}',
          CHANNEL_ID: '{{channel_id}}',
          CHANNEL: 'LINE'
        },
        extract: { bearer_token: '$' },
        retry: 3
      },
      {
        id: 2,
        name: 'verify_citizen_id',
        endpoint: 'https://test.api.com/SearchCitizenID',
        method: 'POST',
        headers: { Authorization: 'Bearer {{bearer_token}}' },
        request: {
          SOCIAL_ID: '{{social_id}}',
          CHANNEL_ID: '{{channel_id}}',
          CHANNEL: 'LINE'
        },
        extract: { citizen_id: '$.ListOfSearchCitizenID[0].CitizenID' },
        validate: "$.ListOfSearchCitizenID[0].Status == '1'",
        retry: 2
      },
      {
        id: 3,
        name: 'fetch_policy_list',
        endpoint: 'https://test.api.com/PolicyListSocial',
        method: 'POST',
        headers: { Authorization: 'Bearer {{bearer_token}}' },
        request: {
          SOCIAL_ID: '{{social_id}}',
          CHANNEL_ID: '{{channel_id}}',
          CHANNEL: 'LINE',
          CITIZEN_ID: '{{citizen_id}}'
        },
        extract: {
          policies: '$.ListOfPolicyListSocial',
          member_codes: '$.ListOfPolicyListSocial[*].MemberCode'
        },
        retry: 2
      },
      {
        id: 4,
        name: 'fetch_policy_details',
        endpoint: 'https://test.api.com/PolicyDetailSocial',
        method: 'POST',
        headers: { Authorization: 'Bearer {{bearer_token}}' },
        request: {
          SOCIAL_ID: '{{social_id}}',
          CHANNEL_ID: '{{channel_id}}',
          CHANNEL: 'LINE',
          MEMBER_CODE: '{{member_code}}'
        },
        extract: {
          policy_details: '$.ListOfPolDet',
          claims_data: '$.ListOfPolClaim'
        },
        iterate_over: 'member_codes',
        retry: 2
      }
    ],
    options: {
      timeout_minutes: 10,
      retry_delay_seconds: 5
    }
  })),
  createExecutionContext: vi.fn((customerId: string) => ({
    customer_id: customerId,
    execution_id: `exec_test_${customerId}`,
    started_at: new Date(),
    current_step: 0,
    step_data: {},
    database_data: {
      social_id: 'test_social_id',
      channel_id: 'test_channel_id',
      citizen_id: 'test_citizen_id'
    }
  })),
  resolveTemplateVariables: vi.fn((text: string, context: any) => {
    return text
      .replace('{{social_id}}', context.database_data?.social_id || context.step_data?.social_id || 'test_social_id')
      .replace('{{channel_id}}', context.database_data?.channel_id || context.step_data?.channel_id || 'test_channel_id')
      .replace('{{citizen_id}}', context.step_data?.citizen_id || 'test_citizen_id')
      .replace('{{bearer_token}}', context.step_data?.bearer_token || 'test_token')
      .replace('{{member_code}}', context.step_data?.member_code || 'test_member_code');
  }),
  getStepsInOrder: vi.fn((config) => config.steps),
  resolveDataSource: vi.fn(() => ({
    social_id: 'test_social_id',
    channel_id: 'test_channel_id',
    citizen_id: 'test_citizen_id'
  }))
}));

describe('Policy Workflow Integration Tests', () => {
  let workflowExecutor: PolicyWorkflowExecutor;
  let dataTransformer: PolicyDataTransformer;
  let customersService: CustomersService;

  beforeEach(() => {
    workflowExecutor = new PolicyWorkflowExecutor();
    dataTransformer = new PolicyDataTransformer();
    customersService = new CustomersService();
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('Complete Workflow Integration', () => {
    it('should execute complete workflow and return transformed data', async () => {
      // Mock successful API responses for all 4 steps
      mockFetch
        // Step 1: Get Bearer Token
        .mockResolvedValueOnce({
          ok: true,
          text: () => Promise.resolve('"test_bearer_token_123"')
        })
        // Step 2: Verify Citizen ID
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({
            ListOfSearchCitizenID: [
              { Status: '1', CitizenID: 'verified_citizen_id' }
            ]
          })
        })
        // Step 3: Fetch Policy List
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({
            ListOfPolicyListSocial: [
              {
                Name: 'Health Insurance Plan A',
                PolNo: 'POL001',
                MemberCode: 'MEM001',
                EffFrom: '2024-01-01',
                EffTo: '2024-12-31'
              },
              {
                Name: 'Life Insurance Plan B',
                PolNo: 'POL002',
                MemberCode: 'MEM002',
                EffFrom: '2023-06-01',
                EffTo: '2025-05-31'
              }
            ]
          })
        })
        // Step 4: Fetch Policy Details (called twice for two member codes)
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({
            ListOfPolDet: [{
              PolNo: 'POL001',
              ProductName: 'Health Insurance Plan A',
              PremiumAmount: '5000',
              CoverageAmount: '100000',
              Currency: 'THB'
            }],
            ListOfPolClaim: [{
              ClaimNo: 'CLM001',
              Amount: '15000',
              Status: 'Approved',
              ClaimType: 'Medical'
            }]
          })
        })
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({
            ListOfPolDet: [{
              PolNo: 'POL002',
              ProductName: 'Life Insurance Plan B',
              PremiumAmount: '8000',
              CoverageAmount: '500000',
              Currency: 'THB'
            }],
            ListOfPolClaim: [{
              ClaimNo: 'CLM002',
              Amount: '25000',
              Status: 'Under Review',
              ClaimType: 'Death'
            }]
          })
        });

      const result = await workflowExecutor.executeWorkflow('123');

      expect(result).toBeDefined();
      expect(result.customer_id).toBe(123);
      expect(result.policies).toHaveLength(2);
      expect(result.claims).toHaveLength(2);
      expect(result.statistics.total_policies).toBe(2);
      expect(result.statistics.total_claims).toBe(2);

      // Verify all API calls were made
      expect(mockFetch).toHaveBeenCalledTimes(5);
    });

    it('should handle workflow failure and provide meaningful error', async () => {
      // Mock failed API response at step 1
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 401,
        statusText: 'Unauthorized'
      });

      await expect(workflowExecutor.executeWorkflow('123')).rejects.toThrow();
    });

    it('should handle partial workflow failure with retry', async () => {
      // Mock failed first attempt, then successful
      mockFetch
        .mockResolvedValueOnce({
          ok: false,
          status: 500,
          statusText: 'Internal Server Error'
        })
        .mockResolvedValueOnce({
          ok: true,
          text: () => Promise.resolve('"retry_token"')
        })
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({
            ListOfSearchCitizenID: [
              { Status: '1', CitizenID: 'retry_citizen_id' }
            ]
          })
        })
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({
            ListOfPolicyListSocial: []
          })
        });

      const result = await workflowExecutor.executeWorkflow('123');

      expect(result).toBeDefined();
      expect(result.policies).toHaveLength(0); // No policies found
      expect(mockFetch).toHaveBeenCalledTimes(4); // 2 attempts for step 1 + steps 2,3
    });
  });

  describe('Customer Service Integration', () => {
    it('should integrate workflow executor with customer service', async () => {
      // Mock successful workflow execution
      mockFetch
        .mockResolvedValueOnce({
          ok: true,
          text: () => Promise.resolve('"service_token"')
        })
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({
            ListOfSearchCitizenID: [
              { Status: '1', CitizenID: 'service_citizen_id' }
            ]
          })
        })
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({
            ListOfPolicyListSocial: [{
              Name: 'Service Test Policy',
              PolNo: 'SVC001',
              MemberCode: 'SVC_MEM001',
              EffFrom: '2024-01-01',
              EffTo: '2024-12-31'
            }]
          })
        })
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({
            ListOfPolDet: [{
              PolNo: 'SVC001',
              ProductName: 'Service Test Policy',
              PremiumAmount: '3000',
              CoverageAmount: '50000'
            }],
            ListOfPolClaim: []
          })
        });

      const result = await customersService.getCustomerPoliciesAndClaims('123', 'test_token');

      expect(result.res_status).toBe(200);
      expect(result.customer_policies).toHaveLength(1);
      expect(result.customer_policies[0].policies).toHaveLength(1);
      expect(result.customer_policies[0].policies[0].policy_number).toBe('SVC001');
    });

    it('should fallback to API call when workflow fails', async () => {
      // Mock workflow failure
      mockFetch.mockRejectedValueOnce(new Error('Network error'));

      // Mock fallback API call
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve([{
          customer_id: 123,
          customer_name: 'Fallback Customer',
          policies: [],
          claims: [],
          statistics: {
            total_policies: 0,
            total_claims: 0
          }
        }])
      });

      const result = await customersService.getCustomerPoliciesAndClaims('123', 'test_token');

      expect(result.res_status).toBe(200);
      expect(mockFetch).toHaveBeenCalledTimes(2); // 1 failed workflow + 1 fallback API
    });
  });

  describe('Error Handling Integration', () => {
    it('should handle citizen ID validation failure', async () => {
      mockFetch
        .mockResolvedValueOnce({
          ok: true,
          text: () => Promise.resolve('"validation_token"')
        })
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({
            ListOfSearchCitizenID: [
              { Status: '0', CitizenID: 'invalid_citizen_id' } // Invalid status
            ]
          })
        });

      await expect(workflowExecutor.executeWorkflow('123')).rejects.toThrow('Citizen ID verification failed');
    });

    it('should handle empty citizen ID search results', async () => {
      mockFetch
        .mockResolvedValueOnce({
          ok: true,
          text: () => Promise.resolve('"empty_token"')
        })
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({
            ListOfSearchCitizenID: [] // Empty results
          })
        });

      await expect(workflowExecutor.executeWorkflow('123')).rejects.toThrow('No citizen ID search results found');
    });

    it('should handle malformed JSON responses', async () => {
      mockFetch
        .mockResolvedValueOnce({
          ok: true,
          text: () => Promise.resolve('"malformed_token"')
        })
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.reject(new Error('Invalid JSON'))
        });

      await expect(workflowExecutor.executeWorkflow('123')).rejects.toThrow();
    });
  });

  describe('Data Transformation Integration', () => {
    it('should correctly transform complex policy and claims data', async () => {
      const mockContext = {
        customer_id: '123',
        execution_id: 'exec_integration_test',
        started_at: new Date(),
        current_step: 4,
        step_data: {
          policies: [
            {
              Name: 'Complex Health Plan',
              PolNo: 'COMPLEX001',
              MemberCode: 'COMPLEX_MEM001',
              EffFrom: '2024-01-01',
              EffTo: '2024-12-31'
            }
          ],
          policy_details: [
            {
              PolNo: 'COMPLEX001',
              ProductName: 'Complex Health Plan',
              PremiumAmount: '12000',
              CoverageAmount: '1000000',
              Currency: 'THB',
              Status: 'Active',
              PaymentFrequency: 'Monthly'
            }
          ],
          claims_data: [
            {
              ClaimNo: 'COMPLEX_CLM001',
              Amount: '50000',
              Status: 'Paid',
              ClaimType: 'Hospitalization',
              ClaimDate: '2024-03-15',
              IncidentDate: '2024-03-10',
              HospitalName: 'Bangkok Hospital'
            }
          ]
        },
        database_data: {}
      };

      const result = dataTransformer.transformToCustomerPoliciesData(
        mockContext,
        123,
        'Integration Test Customer',
        '<EMAIL>'
      );

      expect(result.customer_name).toBe('Integration Test Customer');
      expect(result.policies).toHaveLength(1);
      expect(result.claims).toHaveLength(1);
      
      const policy = result.policies[0];
      expect(policy.policy_number).toBe('COMPLEX001');
      expect(policy.premium_amount).toBe(12000);
      expect(policy.coverage_amount).toBe(1000000);
      expect(policy.payment_frequency).toBe('MONTHLY');
      
      const claim = result.claims[0];
      expect(claim.claim_number).toBe('COMPLEX_CLM001');
      expect(claim.claimed_amount).toBe(50000);
      expect(claim.claim_status).toBe('PAID');
      expect(claim.claim_type).toBe('HOSPITALIZATION');
      expect(claim.hospital_name).toBe('Bangkok Hospital');
      
      expect(result.statistics.total_policies).toBe(1);
      expect(result.statistics.total_claims).toBe(1);
      expect(result.statistics.total_premium_amount).toBe(12000);
      expect(result.statistics.total_coverage_amount).toBe(1000000);
    });
  });
});
