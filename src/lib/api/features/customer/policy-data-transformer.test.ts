/**
 * Unit tests for PolicyDataTransformer
 */

import { describe, it, expect, beforeEach } from 'vitest';
import { PolicyDataTransformer } from './policy-data-transformer.service';
import type { WorkflowExecutionContext } from './policy-claims-workflow.types';

describe('PolicyDataTransformer', () => {
  let transformer: PolicyDataTransformer;
  let mockContext: WorkflowExecutionContext;

  beforeEach(() => {
    transformer = new PolicyDataTransformer();
    
    mockContext = {
      customer_id: '123',
      execution_id: 'exec_test_123',
      started_at: new Date(),
      current_step: 4,
      step_data: {
        policies: [
          {
            Name: 'Health Insurance Plan A',
            PolNo: 'POL001',
            MemberCode: 'MEM001',
            EffFrom: '2024-01-01',
            EffTo: '2024-12-31'
          },
          {
            Name: 'Life Insurance Plan B',
            PolNo: 'POL002',
            MemberCode: 'MEM002',
            EffFrom: '2023-06-01',
            EffTo: '2025-05-31'
          }
        ],
        policy_details: [
          {
            PolNo: 'POL001',
            ProductName: 'Health Insurance Plan A',
            PremiumAmount: '5000',
            CoverageAmount: '100000',
            Currency: 'THB',
            Status: 'Active'
          },
          {
            PolNo: 'POL002',
            ProductName: 'Life Insurance Plan B',
            PremiumAmount: '8000',
            CoverageAmount: '500000',
            Currency: 'THB',
            Status: 'Active'
          }
        ],
        claims_data: [
          {
            ClaimNo: 'CLM001',
            Amount: '15000',
            Status: 'Approved',
            ClaimType: 'Medical',
            ClaimDate: '2024-03-15',
            IncidentDate: '2024-03-10'
          },
          {
            ClaimNo: 'CLM002',
            Amount: '25000',
            Status: 'Under Review',
            ClaimType: 'Hospitalization',
            ClaimDate: '2024-04-20',
            IncidentDate: '2024-04-18'
          }
        ]
      },
      database_data: {}
    };
  });

  describe('transformToCustomerPoliciesData', () => {
    it('should transform workflow context to CustomerPoliciesData format', () => {
      const result = transformer.transformToCustomerPoliciesData(
        mockContext,
        123,
        'John Doe',
        '<EMAIL>'
      );

      expect(result).toBeDefined();
      expect(result.customer_id).toBe(123);
      expect(result.customer_name).toBe('John Doe');
      expect(result.customer_email).toBe('<EMAIL>');
      expect(result.policies).toHaveLength(2);
      expect(result.claims).toHaveLength(2);
      expect(result.statistics).toBeDefined();
      expect(result.last_updated).toBeDefined();
    });

    it('should use default values when customer info is not provided', () => {
      const result = transformer.transformToCustomerPoliciesData(mockContext, 123);

      expect(result.customer_name).toBe('Customer 123');
      expect(result.customer_email).toBe('<EMAIL>');
    });

    it('should handle empty workflow data gracefully', () => {
      const emptyContext: WorkflowExecutionContext = {
        ...mockContext,
        step_data: {}
      };

      const result = transformer.transformToCustomerPoliciesData(emptyContext, 123);

      expect(result.policies).toHaveLength(0);
      expect(result.claims).toHaveLength(0);
      expect(result.statistics.total_policies).toBe(0);
      expect(result.statistics.total_claims).toBe(0);
    });
  });

  describe('policy transformation', () => {
    it('should correctly map TPA policy fields to internal Policy type', () => {
      const result = transformer.transformToCustomerPoliciesData(mockContext, 123);
      const policy = result.policies[0];

      expect(policy.policy_number).toBe('POL001');
      expect(policy.product.name).toBe('Health Insurance Plan A');
      expect(policy.product.product_type).toBe('HEALTH');
      expect(policy.policy_status).toBe('ACTIVE');
      expect(policy.premium_amount).toBe(5000);
      expect(policy.coverage_amount).toBe(100000);
      expect(policy.currency).toBe('THB');
      expect(policy.insurer).toBe('TPA');
    });

    it('should determine policy type correctly from product name', () => {
      const testCases = [
        { name: 'Life Insurance Plan', expected: 'LIFE' },
        { name: 'Health Coverage', expected: 'HEALTH' },
        { name: 'Auto Insurance', expected: 'AUTO' },
        { name: 'Property Protection', expected: 'PROPERTY' },
        { name: 'Travel Insurance', expected: 'TRAVEL' },
        { name: 'Disability Coverage', expected: 'DISABILITY' },
        { name: 'Critical Illness Plan', expected: 'CRITICAL_ILLNESS' },
        { name: 'Unknown Plan', expected: 'HEALTH' } // Default
      ];

      testCases.forEach(({ name, expected }) => {
        const contextWithTestPolicy = {
          ...mockContext,
          step_data: {
            policies: [{ Name: name, PolNo: 'TEST001', EffFrom: '2024-01-01', EffTo: '2024-12-31' }],
            policy_details: [{ PolNo: 'TEST001', ProductName: name }],
            claims_data: []
          }
        };

        const result = transformer.transformToCustomerPoliciesData(contextWithTestPolicy, 123);
        expect(result.policies[0].product.product_type).toBe(expected);
      });
    });

    it('should determine policy status correctly', () => {
      const activeContext = {
        ...mockContext,
        step_data: {
          policies: [{ Name: 'Test Policy', PolNo: 'TEST001', EffFrom: '2024-01-01', EffTo: '2025-12-31' }],
          policy_details: [{ PolNo: 'TEST001', Status: 'Active' }],
          claims_data: []
        }
      };

      const result = transformer.transformToCustomerPoliciesData(activeContext, 123);
      expect(result.policies[0].policy_status).toBe('ACTIVE');
    });

    it('should handle missing policy details gracefully', () => {
      const contextWithMissingDetails = {
        ...mockContext,
        step_data: {
          policies: [{ Name: 'Test Policy', PolNo: 'TEST001', EffFrom: '2024-01-01', EffTo: '2024-12-31' }],
          policy_details: [], // No matching details
          claims_data: []
        }
      };

      const result = transformer.transformToCustomerPoliciesData(contextWithMissingDetails, 123);
      expect(result.policies).toHaveLength(1);
      expect(result.policies[0].premium_amount).toBe(0); // Default value
    });
  });

  describe('claims transformation', () => {
    it('should correctly map TPA claims fields to internal Claim type', () => {
      const result = transformer.transformToCustomerPoliciesData(mockContext, 123);
      const claim = result.claims[0];

      expect(claim.claim_number).toBe('CLM001');
      expect(claim.claim_status).toBe('APPROVED');
      expect(claim.claim_type).toBe('MEDICAL');
      expect(claim.claimed_amount).toBe(15000);
      expect(claim.currency).toBe('THB');
    });

    it('should determine claim status correctly', () => {
      const testCases = [
        { status: 'Submitted', expected: 'SUBMITTED' },
        { status: 'Under Review', expected: 'UNDER_REVIEW' },
        { status: 'Approved', expected: 'APPROVED' },
        { status: 'Rejected', expected: 'REJECTED' },
        { status: 'Paid', expected: 'PAID' },
        { status: 'Closed', expected: 'CLOSED' },
        { status: 'Pending Documents', expected: 'PENDING_DOCUMENTS' },
        { status: 'Unknown Status', expected: 'SUBMITTED' } // Default
      ];

      testCases.forEach(({ status, expected }) => {
        const contextWithTestClaim = {
          ...mockContext,
          step_data: {
            policies: [],
            policy_details: [],
            claims_data: [{ ClaimNo: 'TEST001', Status: status, Amount: '1000' }]
          }
        };

        const result = transformer.transformToCustomerPoliciesData(contextWithTestClaim, 123);
        expect(result.claims[0].claim_status).toBe(expected);
      });
    });

    it('should determine claim type correctly', () => {
      const testCases = [
        { type: 'Death', expected: 'DEATH' },
        { type: 'Medical', expected: 'MEDICAL' },
        { type: 'Accident', expected: 'ACCIDENT' },
        { type: 'Property Damage', expected: 'PROPERTY_DAMAGE' },
        { type: 'Theft', expected: 'THEFT' },
        { type: 'Disability', expected: 'DISABILITY' },
        { type: 'Critical Illness', expected: 'CRITICAL_ILLNESS' },
        { type: 'Hospitalization', expected: 'HOSPITALIZATION' },
        { type: 'Unknown Type', expected: 'MEDICAL' } // Default
      ];

      testCases.forEach(({ type, expected }) => {
        const contextWithTestClaim = {
          ...mockContext,
          step_data: {
            policies: [],
            policy_details: [],
            claims_data: [{ ClaimNo: 'TEST001', ClaimType: type, Amount: '1000' }]
          }
        };

        const result = transformer.transformToCustomerPoliciesData(contextWithTestClaim, 123);
        expect(result.claims[0].claim_type).toBe(expected);
      });
    });
  });

  describe('statistics calculation', () => {
    it('should calculate policy statistics correctly', () => {
      const result = transformer.transformToCustomerPoliciesData(mockContext, 123);
      const stats = result.statistics;

      expect(stats.total_policies).toBe(2);
      expect(stats.active_policies).toBe(2); // Both policies are active
      expect(stats.total_premium_amount).toBe(13000); // 5000 + 8000
      expect(stats.total_coverage_amount).toBe(600000); // 100000 + 500000
      expect(stats.average_premium).toBe(6500); // 13000 / 2
    });

    it('should calculate claims statistics correctly', () => {
      const result = transformer.transformToCustomerPoliciesData(mockContext, 123);
      const stats = result.statistics;

      expect(stats.total_claims).toBe(2);
      expect(stats.approved_claims).toBe(1); // One approved claim
      expect(stats.active_claims).toBe(1); // One under review
      expect(stats.total_claims_amount).toBe(40000); // 15000 + 25000
    });

    it('should calculate policy type breakdown correctly', () => {
      const result = transformer.transformToCustomerPoliciesData(mockContext, 123);
      const breakdown = result.statistics.policy_type_breakdown;

      expect(breakdown.HEALTH).toBe(1);
      expect(breakdown.LIFE).toBe(1);
      expect(breakdown.AUTO).toBe(0);
      expect(breakdown.PROPERTY).toBe(0);
    });

    it('should handle zero policies and claims', () => {
      const emptyContext = {
        ...mockContext,
        step_data: { policies: [], policy_details: [], claims_data: [] }
      };

      const result = transformer.transformToCustomerPoliciesData(emptyContext, 123);
      const stats = result.statistics;

      expect(stats.total_policies).toBe(0);
      expect(stats.total_claims).toBe(0);
      expect(stats.average_premium).toBe(0);
      expect(stats.total_premium_amount).toBe(0);
    });
  });

  describe('data parsing utilities', () => {
    it('should parse amounts correctly', () => {
      const testCases = [
        { input: '1000', expected: 1000 },
        { input: '1,000.50', expected: 1000.50 },
        { input: 'THB 2,500', expected: 2500 },
        { input: undefined, expected: undefined },
        { input: 'invalid', expected: undefined }
      ];

      testCases.forEach(({ input, expected }) => {
        // Access private method through any cast for testing
        const result = (transformer as any).parseAmount(input);
        expect(result).toBe(expected);
      });
    });

    it('should parse dates correctly', () => {
      const testCases = [
        { input: '2024-01-01', expected: '2024-01-01T00:00:00.000Z' },
        { input: '2024-12-31T23:59:59', expected: '2024-12-31T23:59:59.000Z' },
        { input: 'invalid-date', expected: expect.any(String) }, // Should return current date
        { input: undefined, expected: expect.any(String) } // Should return current date
      ];

      testCases.forEach(({ input, expected }) => {
        const result = (transformer as any).parseDate(input);
        if (typeof expected === 'string' && expected.includes('2024')) {
          expect(result).toBe(expected);
        } else {
          expect(result).toMatch(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}Z$/);
        }
      });
    });
  });
});
