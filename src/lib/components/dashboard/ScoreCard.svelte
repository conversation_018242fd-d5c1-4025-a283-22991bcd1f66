<script lang="ts">
    import { COLORS } from '$lib/components/dashboard/colors';
    import { t } from '$lib/stores/i18n'; // Import the translation store

    export let title: string;
    export let value: number | string | null; // CORRECTED: Changed type to allow string, number, or null
    export let unit: string = ''; // Optional unit (e.g., %, min)
    export let description: string = '';
    export let valueColor: string = 'text-gray-900'; // Tailwind class for value color
    export let trendValue: number | null = null; // Changed type to allow null
    export let trendUnit: string = ''; // Unit for trend value
    export let isTrendPositiveIsGood: boolean = true; // NEW PROP: Controls if a positive trend is good (green) or bad (red)
</script>

<div class="flex flex-col h-full">
    <div class="flex items-center justify-between mb-2">
        <h2 class="text-xl font-semibold text-gray-700">{title}</h2>
    </div>
    <div class="flex items-baseline mb-0.5">
        {#if value === null || (typeof value === 'string' && value === 'N/A')} <p class="text-3xl font-bold text-gray-500 leading-none">{t('db.noDataAvailable')}</p>
        {:else if typeof value === 'number'} <p class="text-4xl font-bold {valueColor} leading-none">{value.toFixed(1)}</p> {#if unit}
                <span class="ml-2 text-xl font-semibold text-gray-500">{unit}</span>
            {/if}
        {:else} <p class="text-4xl font-bold {valueColor} leading-none">{value}</p>
            {#if unit}
                <span class="ml-2 text-xl font-semibold text-gray-500">{unit}</span>
            {/if}
        {/if}
    </div>
    <p class="text-sm text-gray-500 mb-1 flex-grow">{description}</p>

    {#if trendValue === null}
        <div class="flex items-center text-sm font-medium text-gray-500">
            {t('db.noDataAvailable')}
        </div>
    {:else if trendValue !== undefined}
        <div class="flex items-center text-sm font-medium">
            {#if trendValue > 0}
                <span class="{isTrendPositiveIsGood ? 'text-green-500' : 'text-red-500'} flex items-center">
                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18"></path></svg>
                    +{trendValue.toFixed(1)}{trendUnit}
                </span>
            {:else if trendValue < 0}
                <span class="{isTrendPositiveIsGood ? 'text-red-500' : 'text-green-500'} flex items-center">
                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3"></path></svg>
                    {trendValue.toFixed(1)}{trendUnit}
                </span>
            {:else}
                <span class="text-gray-500 flex items-center">
                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h8m-4 4v-8"></path></svg>
                    {trendValue.toFixed(1)}{trendUnit}
                </span>
            {/if}
            <span class="ml-1 text-gray-500">{t('dbScoreCard.comparedToPreviousPeriod')}</span>
        </div>
    {/if}
</div>