<script lang="ts">
	import { Timeline, TimelineItem, Badge, Card, Modal, Button, Input, Label, Select, Radio, Textarea, Toast } from 'flowbite-svelte';
	import {
		CheckCircleSolid,
		FileLinesSolid,
		DatabaseSolid,
		ServerSolid,
		EditOutline,
		CogSolid,
		RefreshOutline,
		ExclamationCircleOutline
	} from 'flowbite-svelte-icons';
	import { writable } from 'svelte/store';
	import { fly } from 'svelte/transition';

	// Types for step configuration
	interface StepConfig {
		id: number;
		title: string;
		description: string;
		icon: any;
		endpoint: string;
		method: 'GET' | 'POST';
		requestBody: any;
		responseExample: any;
		responseFormat: 'string' | 'json';
		badgeColor: 'blue' | 'green' | 'yellow' | 'purple' | 'indigo' | 'pink' | 'red';
		isModified: boolean;
	}

	// Modal state
	let editModalOpen = false;
	let currentEditingStep: StepConfig | null = null;
	let editForm: StepConfig | null = null;

	// Toast state
	let toastVisible = false;
	let toastMessage = '';
	let toastType: 'success' | 'error' = 'success';

	// Bearer token from step 1
	let bearerToken = '';

	// Step data based on the documentation - now with editable configuration
	const defaultApiSteps: StepConfig[] = [
		{
			id: 1,
			title: 'Get Bearer Token',
			description: 'Retrieve authentication token from TPA API',
			icon: ServerSolid,
			endpoint: 'https://uat.thirdpartyadmin.co.th:4443/TPA.TMS.Web.API_PREPROV2/api/GetToken',
			method: 'POST',
			requestBody: {
				"USERNAME": "BVTPA",
				"PASSWORD": "*d!n^+Cb@1",
				"SOCIAL_ID": "Value from platform_user_id in customer_customerplatformidentity table",
				"CHANNEL_ID": "Value from channel_id in customer_customerplatformidentity table",
				"CHANNEL": "LINE"
			},
			responseExample: '"eyJhbGciOiJBMTI4S1ciLCJlbmMiOiJBMTI4Q0JDLUhTMjU2IiwidHlwIjoiSldUIn0.nd..."',
			responseFormat: 'string',
			badgeColor: 'blue',
			isModified: false
		},
		{
			id: 2,
			title: 'Verify Citizen ID',
			description: 'Validate Citizen ID with external API',
			icon: CheckCircleSolid,
			endpoint: 'https://uat.thirdpartyadmin.co.th:4443/TPA.TMS.Web.API_PREPROV2/api/SearchCitizenID',
			method: 'POST',
			requestBody: {
				"SOCIAL_ID": "Value from platform_user_id",
				"CHANNEL_ID": "Value from channel_id",
				"CHANNEL": "LINE"
			},
			responseExample: {
				"ListOfSearchCitizenID": [
					{
						"Status": "1",
						"CitizenID": "2019086318637"
					}
				],
				"ErrorMessage": ""
			},
			responseFormat: 'json',
			badgeColor: 'purple',
			isModified: false
		},
		{
			id: 3,
			title: 'Fetch Policy List',
			description: 'Retrieve customer policy list using verified Citizen ID',
			icon: FileLinesSolid,
			endpoint: 'https://uat.thirdpartyadmin.co.th:4443/TPA.TMS.Web.API_PREPROV2/api/PolicyListSocial',
			method: 'POST',
			requestBody: {
				"SOCIAL_ID": "Value from platform_user_id",
				"CHANNEL_ID": "Value from channel_id",
				"CHANNEL": "LINE",
				"CITIZEN_ID": "Value from CitizenID in step 4 response"
			},
			responseExample: {
				"ListOfPolicyListSocial": [
					{
						"Name": "ทดสอบ2",
						"CitizenID": "2019086318637",
						"PolNo": "BVTPA_2024",
						"MemberCode": "202400006-2 Chatbot4",
						"EffFrom": "15/02/2024",
						"EffTo": "20/12/2025"
					}
				]
			},
			responseFormat: 'json',
			badgeColor: 'indigo',
			isModified: false
		},
		{
			id: 4,
			title: 'Fetch Policy & Claims Details',
			description: 'Get detailed policy and claims data for each MemberCode',
			icon: DatabaseSolid,
			endpoint: 'https://uat.thirdpartyadmin.co.th:4443/TPA.TMS.Web.API_PREPROV2/api/PolicyDetailSocial',
			method: 'POST',
			requestBody: {
				"SOCIAL_ID": "Value from platform_user_id",
				"CHANNEL_ID": "Value from channel_id",
				"CHANNEL": "LINE",
				"MEMBER_CODE": "Value from MemberCode in step 6"
			},
			responseExample: {
				"ListOfPolDet": "Policy details with coverage information",
				"ListOfPolClaim": "Claims data with status and amounts"
			},
			responseFormat: 'json',
			badgeColor: 'red',
			isModified: false
		}
	];

	// Reactive store for current step configurations
	const apiStepsStore = writable<StepConfig[]>(defaultApiSteps);
	let apiSteps: StepConfig[] = [];

	// Subscribe to store changes
	apiStepsStore.subscribe(value => {
		apiSteps = value;
		// Update bearer token when step 1 response changes
		if (value[0]?.responseExample && typeof value[0].responseExample === 'string') {
			bearerToken = value[0].responseExample.replace(/"/g, '');
		}
	});

	// Functions for editing steps
	function openEditModal(step: StepConfig) {
		currentEditingStep = step;
		editForm = { ...step }; // Create a copy for editing
		editModalOpen = true;
	}

	function closeEditModal() {
		editModalOpen = false;
		currentEditingStep = null;
		editForm = null;
	}

	function saveStepChanges() {
		if (!editForm || !currentEditingStep) return;

		// Validate form
		if (!editForm.title.trim() || !editForm.description.trim()) {
			showToast('Please fill in all required fields', 'error');
			return;
		}

		// Update the step in the store
		apiStepsStore.update(steps => {
			const updatedSteps = steps.map(step => {
				if (step.id === editForm!.id) {
					return { ...editForm!, isModified: true };
				}
				return step;
			});
			return updatedSteps;
		});

		showToast('Step configuration updated successfully', 'success');
		closeEditModal();
	}

	function resetStepToDefault(stepId: number) {
		const defaultStep = defaultApiSteps.find(step => step.id === stepId);
		if (!defaultStep) return;

		apiStepsStore.update(steps => {
			return steps.map(step => {
				if (step.id === stepId) {
					return { ...defaultStep, isModified: false };
				}
				return step;
			});
		});

		showToast('Step reset to default configuration', 'success');
	}

	function showToast(message: string, type: 'success' | 'error') {
		toastMessage = message;
		toastType = type;
		toastVisible = true;
		setTimeout(() => {
			toastVisible = false;
		}, 3000);
	}

	// HTTP method options
	const httpMethods = [
		{ value: 'GET', name: 'GET' },
		{ value: 'POST', name: 'POST' }
	];
</script>

<div class="space-y-6 rounded-lg bg-white p-6 shadow-md">
	<div class="flex items-center justify-between">
		<div>
			<h2 class="text-xl font-medium text-gray-700">Policy & Claims API Integration Steps</h2>
			<p class="text-sm text-gray-500">Step-by-step process for fetching insurance policies and claims data from external APIs</p>
		</div>
	</div>

	<div class="space-y-4">
		<Timeline>
			{#each apiSteps as step}
				<TimelineItem title={step.title} date="Step {step.id}">
					<svelte:component this={step.icon} slot="icon" class="w-4 h-4 text-{step.badgeColor}-600" />

					<Card class="mt-3" size="xl">
						<div class="space-y-4">
							<!-- Header with edit button and modified indicator -->
							<div class="flex items-start justify-between">
								<div class="flex-1">
									<p class="text-sm text-gray-600">{step.description}</p>
									{#if step.isModified}
										<Badge color="yellow" class="mt-1">Modified</Badge>
									{/if}
								</div>
								<div class="flex items-center space-x-2">
									<!-- <Button size="xs" color="light" on:click={() => openEditModal(step)}>
										<EditOutline class="w-3 h-3 mr-1" />
										Edit
									</Button> -->
									{#if step.isModified}
										<Button size="xs" color="alternative" on:click={() => resetStepToDefault(step.id)}>
											<RefreshOutline class="w-3 h-3 mr-1" />
											Reset
										</Button>
									{/if}
								</div>
							</div>

							<!-- API Endpoint with method -->
							{#if step.endpoint}
								<div>
									<Badge color={step.badgeColor} class="mb-2">
										{step.method} API Endpoint
									</Badge>
									<code class="block bg-gray-100 p-2 rounded text-xs break-all">
										{step.endpoint}
									</code>
								</div>
							{/if}

							<!-- Bearer Token section for API endpoints -->
							{#if step.endpoint && step.id > 1}
								<div>
									<Badge color="dark" class="mb-2">Bearer Token</Badge>
									<code class="block bg-gray-100 p-2 rounded text-xs break-all">
										{bearerToken}
									</code>
								</div>
							{/if}

							<!-- Request Body -->
							{#if step.requestBody}
								<div>
									<Badge color="dark" class="mb-2">Request Body</Badge>
									<pre class="bg-gray-50 p-3 rounded text-xs overflow-x-auto"><code>{JSON.stringify(step.requestBody, null, 2)}</code></pre>
								</div>
							{/if}

							<!-- Response Example with format indicator -->
							{#if step.responseExample}
								<div>
									<div class="flex items-center space-x-2 mb-2">
										<Badge color="green">Response Example</Badge>
										<Badge color="blue" class="text-xs">{step.responseFormat.toUpperCase()}</Badge>
									</div>
									{#if step.responseFormat === 'string'}
										<code class="block bg-green-50 p-2 rounded text-xs">{typeof step.responseExample === 'string' ? step.responseExample : JSON.stringify(step.responseExample)}</code>
									{:else}
										<pre class="bg-green-50 p-3 rounded text-xs overflow-x-auto"><code>{typeof step.responseExample === 'string' ? step.responseExample : JSON.stringify(step.responseExample, null, 2)}</code></pre>
									{/if}
								</div>
							{/if}
						</div>
					</Card>
				</TimelineItem>
			{/each}
		</Timeline>
	</div>

	<div class="mt-6 p-4 bg-blue-50 border-l-4 border-blue-400 rounded">
		<div class="flex">
			<div class="flex-shrink-0">
				<svg class="h-5 w-5 text-blue-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
					<path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
				</svg>
			</div>
			<div class="ml-3">
				<h3 class="text-sm font-medium text-blue-800">Important Notes</h3>
				<div class="mt-2 text-sm text-blue-700">
					<ul class="list-disc list-inside space-y-1">
						<li>Authentication token has 1-hour expiry and can be reused for multiple API calls</li>
						<li>Customer must have a valid Citizen ID to proceed with policy fetching</li>
						<li>Each policy contains a unique MemberCode used for detailed data retrieval</li>
						<li>Policy and claims data are stored in CustomerPolicies table with PolNo as composite primary key</li>
						<li>System checks for existing data and updates only if changes are detected</li>
						<li><strong>Click the Edit button on any step to customize its configuration</strong></li>
					</ul>
				</div>
			</div>
		</div>
	</div>
</div>

<!-- Edit Modal -->
<Modal bind:open={editModalOpen} size="lg" autoclose={false} class="w-full">
	<div slot="header" class="flex items-center space-x-2">
		<EditOutline class="w-5 h-5" />
		<span>Edit Step {editForm?.id}: {editForm?.title}</span>
	</div>

	{#if editForm}
		<div class="space-y-4">
			<!-- Basic Information -->
			<div class="grid grid-cols-1 gap-4">
				<div>
					<Label for="step-title" class="mb-2">Step Title *</Label>
					<Input id="step-title" bind:value={editForm.title} placeholder="Enter step title" required />
				</div>
				<div>
					<Label for="step-description" class="mb-2">Description *</Label>
					<Textarea id="step-description" bind:value={editForm.description} placeholder="Enter step description" rows={2} required />
				</div>
			</div>

			<!-- API Configuration -->
			{#if editForm.endpoint}
				<div class="border-t pt-4">
					<h4 class="text-sm font-medium text-gray-700 mb-3">API Configuration</h4>
					<div class="grid grid-cols-1 md:grid-cols-2 gap-4">
						<div>
							<Label for="http-method" class="mb-2">HTTP Method</Label>
							<Select id="http-method" bind:value={editForm.method} items={httpMethods} />
						</div>
						<div>
							<Label for="response-format" class="mb-2">Response Format</Label>
							<div class="flex space-x-4 mt-2">
								<Radio bind:group={editForm.responseFormat} value="string">String</Radio>
								<Radio bind:group={editForm.responseFormat} value="json">JSON</Radio>
							</div>
						</div>
					</div>
					<div class="mt-4">
						<Label for="endpoint-url" class="mb-2">Endpoint URL</Label>
						<Input id="endpoint-url" bind:value={editForm.endpoint} placeholder="Enter API endpoint URL" />
					</div>
				</div>
			{/if}

			<!-- Bearer Token Info (for steps > 1) -->
			{#if editForm.id > 1 && editForm.endpoint}
				<div class="border-t pt-4">
					<h4 class="text-sm font-medium text-gray-700 mb-3">Authentication</h4>
					<div>
						<Label for="bearer-token-display" class="mb-2">Bearer Token (from Step 1)</Label>
						<div class="flex items-center space-x-2">
							<Input
								id="bearer-token-display"
								value={bearerToken ? `Bearer ${bearerToken}` : 'Token will be generated from Step 1'}
								readonly
								class="flex-1"
							/>
						</div>
					</div>
				</div>
			{/if}

			<!-- Request Body -->
			{#if editForm.requestBody}
				<div class="border-t pt-4">
					<Label for="request-body" class="mb-2">Request Body (JSON)</Label>
					<Textarea
						id="request-body"
						placeholder="Enter request body as JSON"
						rows={6}
						value={JSON.stringify(editForm.requestBody, null, 2)}
						on:input={(e) => {
							try {
								if (editForm && e.target) {
									// @ts-ignore
									editForm.requestBody = JSON.parse(e.target.value);
								}
							} catch {
								// Keep as string if not valid JSON
							}
						}}
					/>
				</div>
			{/if}

			<!-- Response Example -->
			{#if editForm.responseExample}
				<div class="border-t pt-4">
					<Label for="response-example" class="mb-2">Response Example</Label>
					<Textarea
						id="response-example"
						placeholder="Enter response example"
						rows={6}
						value={typeof editForm.responseExample === 'string' ? editForm.responseExample : JSON.stringify(editForm.responseExample, null, 2)}
						on:input={(e) => {
							if (editForm && e.target) {
								if (editForm.responseFormat === 'json') {
									try {
										// @ts-ignore
										editForm.responseExample = JSON.parse(e.target.value);
									} catch {
										// Keep as string if not valid JSON
									}
								} else {
									// @ts-ignore
									editForm.responseExample = e.target.value;
								}
							}
						}}
					/>
				</div>
			{/if}
		</div>
	{/if}

	<div slot="footer" class="flex items-center justify-between">
		<div class="flex items-center space-x-2">
			{#if editForm?.isModified}
				<Button color="alternative" on:click={() => editForm && resetStepToDefault(editForm.id)}>
					<RefreshOutline class="w-4 h-4 mr-2" />
					Reset to Default
				</Button>
			{/if}
		</div>
		<div class="flex items-center space-x-2">
			<Button color="alternative" on:click={closeEditModal}>Cancel</Button>
			<Button color="primary" on:click={saveStepChanges}>
				<CheckCircleSolid class="w-4 h-4 mr-2" />
				Save Changes
			</Button>
		</div>
	</div>
</Modal>

<!-- Toast Notification -->
{#if toastVisible}
	<Toast
		color={toastType === 'success' ? 'green' : 'red'}
		position="top-right"
		transition={fly}
		params={{ x: 200 }}
		bind:toastStatus={toastVisible}
		class="fixed top-4 right-4 z-50"
	>
		<svelte:component this={toastType === 'success' ? CheckCircleSolid : ExclamationCircleOutline} slot="icon" class="w-5 h-5" />
		{toastMessage}
	</Toast>
{/if}
