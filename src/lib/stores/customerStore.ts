import { writable, derived, get } from 'svelte/store';
import type { Customer, CustomerPlatformIdentity } from '$lib/types/customer';

interface CustomerState {
    customers: Map<number, Customer>;
    selectedCustomerId: number | null;
    loading: boolean;
    error: string | null;
    filters: {
        search: string;
        platform: string | null;
        hasOpenTickets: boolean;
    };
    sortBy: 'lastActivity' | 'name' | 'openTickets';
    sortOrder: 'asc' | 'desc';
}

function createCustomerStore() {
    const { subscribe, set, update } = writable<CustomerState>({
        customers: new Map(),
        selectedCustomerId: null,
        loading: false,
        error: null,
        filters: {
            search: '',
            platform: null,
            hasOpenTickets: false
        },
        sortBy: 'lastActivity',
        sortOrder: 'desc'
    });

    return {
        subscribe,
        
        // Actions
        setCustomers: (customers: Customer[]) => {
            update(state => {
                const customerMap = new Map();
                customers.forEach(c => customerMap.set(c.customer_id, c));
                return { ...state, customers: customerMap };
            });
        },
        
        updateCustomer: (customer: Customer) => {
            update(state => {
                state.customers.set(customer.customer_id, customer);
                return { ...state, customers: new Map(state.customers) };
            });
        },
        
        selectCustomer: (customerId: number | null) => {
            update(state => ({ ...state, selectedCustomerId: customerId }));
        },
        
        setFilter: (filterName: keyof CustomerState['filters'], value: any) => {
            update(state => ({
                ...state,
                filters: { ...state.filters, [filterName]: value }
            }));
        },
        
        setSort: (sortBy: CustomerState['sortBy'], sortOrder: CustomerState['sortOrder']) => {
            update(state => ({ ...state, sortBy, sortOrder }));
        },
        
        setLoading: (loading: boolean) => {
            update(state => ({ ...state, loading }));
        },
        
        setError: (error: string | null) => {
            update(state => ({ ...state, error }));
        },
        
        // Getters
        getCustomer: (customerId: number): Customer | undefined => {
            const state = get({ subscribe });
            return state.customers.get(customerId);
        }
    };
}

export const customerStore = createCustomerStore();

// Derived stores
export const selectedCustomer = derived(
    customerStore,
    $customerStore => {
        if (!$customerStore.selectedCustomerId) return null;
        return $customerStore.customers.get($customerStore.selectedCustomerId);
    }
);

export const filteredCustomers = derived(
    customerStore,
    $customerStore => {
        let customers = Array.from($customerStore.customers.values());
        
        // Apply filters
        if ($customerStore.filters.search) {
            const search = $customerStore.filters.search.toLowerCase();
            customers = customers.filter(c => 
                c.name?.toLowerCase().includes(search) ||
                c.email?.toLowerCase().includes(search) ||
                c.phone?.includes(search)
            );
        }
        
        if ($customerStore.filters.platform) {
            customers = customers.filter(c => 
                c.platforms?.some(p => p.platform === $customerStore.filters.platform)
            );
        }
        
        if ($customerStore.filters.hasOpenTickets) {
            customers = customers.filter(c => c.open_tickets > 0);
        }
        
        // Sort
        customers.sort((a, b) => {
            let comparison = 0;
            
            switch ($customerStore.sortBy) {
                case 'lastActivity':
                    comparison = (b.last_message_time || 0) - (a.last_message_time || 0);
                    break;
                case 'name':
                    comparison = (a.name || '').localeCompare(b.name || '');
                    break;
                case 'openTickets':
                    comparison = b.open_tickets - a.open_tickets;
                    break;
            }
            
            return $customerStore.sortOrder === 'asc' ? comparison : -comparison;
        });
        
        return customers;
    }
);