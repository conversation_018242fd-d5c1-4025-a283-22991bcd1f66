from django.contrib import admin
from .models import Company, Document, Product, PolicyHolder, ProductProvider, ProductType 

# Register your models here.
admin.site.register(Company)
admin.site.register(Document)
admin.site.register(Product)
admin.site.register(PolicyHolder)
admin.site.register(ProductProvider)
admin.site.register(ProductType)

# admin.py
from django.contrib import admin
from .models import LangchainPgEmbedding, LangchainPgCollection

class LangchainPgEmbeddingAdmin(admin.ModelAdmin):
    list_display = ('id', 'collection_id', 'document', 'get_embedding_preview')
    list_filter = ('collection_id',)
    search_fields = ('document', 'cmetadata')
    readonly_fields = ('id',)
    list_per_page = 20
    
    def get_embedding_preview(self, obj):
        if obj.embedding:
            preview = str(obj.embedding)[:50] + '...' if len(str(obj.embedding)) > 50 else str(obj.embedding)
            return preview
        return "No embedding"
    
    get_embedding_preview.short_description = "Embedding Preview"
    
    def get_queryset(self, request):
        return super().get_queryset(request).using('vector_db')
    
    def save_model(self, request, obj, form, change):
        obj.save(using='vector_db')
    
    def delete_model(self, request, obj):
        obj.delete(using='vector_db')
    
    def delete_queryset(self, request, queryset):
        queryset._db = 'vector_db'
        queryset.delete()

admin.site.register(LangchainPgEmbedding, LangchainPgEmbeddingAdmin)