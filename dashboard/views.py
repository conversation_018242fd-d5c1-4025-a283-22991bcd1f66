from rest_framework.response import Response
from rest_framework import status
from datetime import (
    date,
    datetime,
    timedelta,
)  # Import timedelta for end_date adjustment
import json
from django.utils.translation import gettext_lazy as _

# Serializers
from .serializers import (
    ##### [Agent Performance] ######
    AgentMetricSummarySerializer,
    AgentPerformanceSummarySerializer,
    # AgentMetricSummarySerializer,
    TicketStatusSummarySerializer,
    ComprehensiveAgentPerformanceSerializer,
    ##### [Chat Performance] #####
    MessageSummarySerializer,
    MessageTimeSeriesSerializer,
    OverdueTicketSerializer,
    ClosedTicketsByCaseTypeSerializer,
    ClosedTicketsByCaseTopicSerializer,
    ClosedTicketsByCaseTypeAndTopicSerializer,
    ##### [Response Time Volume] #####
    IncomingMessageCountTimeSeriesSerializer,
    TicketCategoryBreakdownSerializer,
    CustomerMessageHeatmapSerializer,
    ##### [Work Quality] #####
    ResponderResponseTimeSerializer,
    SentimentAnalysisSummarySerializer,
    SentimentAnalysisTimeSeriesSerializer,
    SentimentAnalysisByCaseTypeSerializer,
    ClosedTicketSerializer
)

# Class Views to inherit from
from .base_views import RawSQLQueryAPIView  # Raw SQL Query APIs
from .base_views import RawSQLMetricComparisonAPIView  # For Time Comparison APIs
from .base_views import (
    RawSQLMetricComparisonPerAgentAPIView,
)  # For Per Agent Time Comparison APIs


from setting.models import SLA

##########################################################################################
# VIEWs for the [Work Quality] dashboard API endpoints
##########################################################################################


class CSATScoreTimeSeriesAPIView(RawSQLMetricComparisonAPIView):
    """
    API endpoint to provide a time series of average CSAT scores.
    Provides comparison and time-series data, showing null for periods with no data.
    """

    filename_title = _("CSAT Score Time Series")
    serializer_class = MessageTimeSeriesSerializer
    table_name = "ticket_message"
    date_column_name = "created_on"
    units = "score"
    metric_aggregate_sql = "AVG"

    def get_single_period_metric_sql(
        self, time_filter_clause: str, start_date: date, end_date: date
    ) -> tuple[str, list]:
        """
        Returns SQL to get the overall average CSAT score for a single period.
        Will return NULL if no data.
        """
        sql = f"""
        SELECT
            AVG(CAST(json_extract_path_text(metadata::json, 'value') AS DECIMAL)) AS metric_value
        FROM
            ticket_message
        WHERE
            metadata IS NOT NULL
            AND json_extract_path_text(metadata::json, 'action') = 'CSAT_Rating'
            AND json_extract_path_text(metadata::json, 'value') ~ '^\d+$'
            {time_filter_clause}
        ;
        """
        params_for_sql = [start_date, end_date]
        return sql, params_for_sql

    def get_time_series_metric_sql(
        self, time_filter_clause: str, start_date: date, end_date: date
    ) -> tuple[str, list]:
        """
        Returns SQL for time-series data of average CSAT scores.
        Will return NULL for periods with no data.
        """
        date_trunc_unit = self.time_series_granularity

        sql = f"""
        WITH date_series AS (
          SELECT
            DATE_TRUNC('{date_trunc_unit}', generate_series AT TIME ZONE 'Asia/Bangkok') AS series_date
          FROM
            generate_series((%s::date)::timestamp AT TIME ZONE 'Asia/Bangkok', (%s::date)::timestamp AT TIME ZONE 'Asia/Bangkok', INTERVAL '1 {date_trunc_unit}') AS generate_series
        ),
        csat_data AS (
          SELECT
            DATE_TRUNC('{date_trunc_unit}', created_on AT TIME ZONE 'Asia/Bangkok') AS csat_date,
            AVG(CAST(json_extract_path_text(metadata::json, 'value') AS DECIMAL)) AS avg_score
          FROM
            ticket_message
          WHERE
            metadata IS NOT NULL
            AND json_extract_path_text(metadata::json, 'action') = 'CSAT_Rating'
            AND json_extract_path_text(metadata::json, 'value') ~ '^\d+$'
            {time_filter_clause}
          GROUP BY
            DATE_TRUNC('{date_trunc_unit}', created_on AT TIME ZONE 'Asia/Bangkok')
        )
        SELECT
          ds.series_date::date AS time,
          csat_data.avg_score AS value -- --- REMOVED COALESCE(..., 0) ---
        FROM
          date_series ds
        LEFT JOIN
          csat_data ON ds.series_date = csat_data.csat_date
        ORDER BY
          ds.series_date;
        """
        params_for_sql = [start_date, end_date] * 2
        return sql, params_for_sql


class FirstResponseTimeAPIView(RawSQLMetricComparisonAPIView):
    """
    API endpoint to compare the average first response time in seconds.
    Provides comparison and time-series data.
    """

    filename_title = _("Average First Response Time")
    serializer_class = MessageTimeSeriesSerializer
    table_name = "ticket_message"
    date_column_name = "created_on"
    metric_aggregate_sql = "AVG"
    units = "seconds"

    def _get_base_first_response_ctes(
        self,
        time_filter_clause_tm: str,
        time_filter_clause_c: str,
        date_trunc_unit=None,
    ) -> str:
        """
        Helper to get the common CTEs for first response time calculation.
        time_filter_clause_tm: for ticket_message in agent_messages CTE
        time_filter_clause_c: for ticket_message in latest_customer_message_before_agent subquery
        date_trunc_unit: If provided, enables DATE_TRUNC and response_date for time series.
        """
        # Conditional selection and grouping for response_date
        response_date_select = ""
        response_date_group_by_far = ""  # for first_agent_response
        response_date_group_by_lcmba = ""  # for latest_customer_message_before_agent

        if date_trunc_unit:
            response_date_select = (
                f", date_trunc('{date_trunc_unit}', created_on AT TIME ZONE 'Asia/Bangkok') AS response_date"
            )
            response_date_group_by_far = (
                f", date_trunc('{date_trunc_unit}', created_on AT TIME ZONE 'Asia/Bangkok')"
            )
            response_date_group_by_lcmba = (
                f", f.response_date"  # Group by the aliased column in f
            )

        return f"""
        WITH agent_messages AS (
          SELECT
            ticket_id_id,
            created_on,
            user_name,
            created_by_id,
            is_self,
            CASE
              WHEN is_self = true AND created_by_id IS NULL THEN true
              WHEN is_self = true AND created_by_id IS NOT NULL AND lower(user_name) = 'system' THEN true
              ELSE false
            END AS is_chatbot
          FROM ticket_message tm
          WHERE tm.is_self = true
            {time_filter_clause_tm}
        ),
        first_agent_response AS (
          SELECT
            ticket_id_id
            {response_date_select}, -- Only include if date_trunc_unit is provided
            MIN(created_on) AS first_agent_response_time
          FROM agent_messages
          WHERE is_chatbot = false  -- only agent (not chatbot)
          GROUP BY ticket_id_id {response_date_group_by_far}
        ),
        latest_customer_message_before_agent AS (
          SELECT
            f.ticket_id_id,
            f.first_agent_response_time,
            -- Only select f.response_date if it was selected in first_agent_response
            {'f.response_date,' if date_trunc_unit else ''}
            MAX(c.created_on) AS last_customer_msg_time
          FROM first_agent_response f
          JOIN ticket_message c
            ON c.ticket_id_id = f.ticket_id_id
            AND c.is_self = false -- customer messages
            AND c.created_on < f.first_agent_response_time
            {time_filter_clause_c}
          GROUP BY f.ticket_id_id, f.first_agent_response_time {response_date_group_by_lcmba}
        )
        """

    def get_single_period_metric_sql(
        self, time_filter_clause: str, start_date: date, end_date: date
    ) -> tuple[str, list]:
        """
        Returns SQL for a single metric value (overall average first response time in seconds).
        """
        time_filter_clause_tm = time_filter_clause.replace(
            "created_on", "tm.created_on"
        )
        time_filter_clause_c = time_filter_clause.replace("created_on", "c.created_on")

        # For single period, date_trunc_unit is None, so response_date is not selected
        base_ctes = self._get_base_first_response_ctes(
            time_filter_clause_tm, time_filter_clause_c, date_trunc_unit=None
        )

        sql = f"""
        {base_ctes}
        SELECT
          AVG(EXTRACT(EPOCH FROM (first_agent_response_time - last_customer_msg_time))) AS metric_value
        FROM latest_customer_message_before_agent
        WHERE last_customer_msg_time IS NOT NULL;
        """
        params_for_sql = [start_date, end_date] * 2
        return sql, params_for_sql

    def get_time_series_metric_sql(
        self, time_filter_clause: str, start_date: date, end_date: date
    ) -> tuple[str, list]:
        """
        Returns SQL for time-series data of average first response time in seconds.
        """
        date_trunc_unit = self.time_series_granularity

        time_filter_clause_tm = time_filter_clause.replace(
            "created_on", "tm.created_on"
        )
        time_filter_clause_c = time_filter_clause.replace("created_on", "c.created_on")

        # For time series, pass date_trunc_unit to enable response_date selection
        base_ctes = self._get_base_first_response_ctes(
            time_filter_clause_tm, time_filter_clause_c, date_trunc_unit
        )

        sql = f"""
        WITH date_series AS (
          SELECT
            DATE_TRUNC('{date_trunc_unit}', generate_series AT TIME ZONE 'Asia/Bangkok') AS series_date
          FROM
            generate_series((%s::date)::timestamp AT TIME ZONE 'Asia/Bangkok', (%s::date)::timestamp AT TIME ZONE 'Asia/Bangkok', INTERVAL '1 {date_trunc_unit}') AS generate_series
        ),
        response_times_by_date AS (
          {base_ctes}
          SELECT
            response_date,
            AVG(EXTRACT(EPOCH FROM (first_agent_response_time - last_customer_msg_time))) AS avg_response_seconds
          FROM latest_customer_message_before_agent
          WHERE last_customer_msg_time IS NOT NULL
          GROUP BY response_date
        )
        SELECT
          ds.series_date::date AS date,
          rt.avg_response_seconds AS value
        FROM date_series ds
        LEFT JOIN response_times_by_date rt ON ds.series_date = rt.response_date
        ORDER BY ds.series_date;
        """
        params_for_sql = [start_date, end_date] * 3
        return sql, params_for_sql


class ResponderResponseTimeAPIView(RawSQLQueryAPIView):
    """
    API endpoint to provide average response time for both agents and chatbots.
    Supports time filtering.
    """

    file_title = _("Average Response Time")
    serializer_class = ResponderResponseTimeSerializer
    table_name = "ticket_message"
    date_column_name = "created_on"

    def get_sql_query(self, time_filter_clause: str, params: list) -> tuple[str, list]:
        time_filter_clause_m = time_filter_clause.replace("created_on", "m.created_on")
        time_filter_clause_c = time_filter_clause.replace("created_on", "c.created_on")
        time_filter_clause_c2 = time_filter_clause.replace(
            "created_on", "c2.created_on"
        )

        sql = f"""
        WITH messages AS (
          SELECT
            ticket_id_id,
            created_on,
            user_name,
            created_by_id,
            is_self,
            CASE
              WHEN is_self = true AND created_by_id IS NOT NULL AND lower(user_name) = 'system' THEN 'chatbot'
              WHEN is_self = true AND created_by_id IS NULL THEN 'chatbot'  
              WHEN is_self = true AND created_by_id IS NOT NULL THEN 'agent'
              WHEN is_self = false THEN 'customer'
              ELSE 'other'
            END AS message_type
          FROM ticket_message m
          WHERE 1=1 {time_filter_clause_m}
        ),
        response_pairs AS (
          SELECT
            m.ticket_id_id,
            m.created_on AS responder_time,
            m.message_type,
            (
              SELECT MAX(c.created_on)
              FROM messages c
              WHERE c.ticket_id_id = m.ticket_id_id
                AND c.message_type = 'customer'
                AND c.created_on < m.created_on
                {time_filter_clause_c}
            ) AS last_customer_msg_time,
            EXTRACT(EPOCH FROM (m.created_on - (
                SELECT MAX(c2.created_on)
                FROM messages c2
                WHERE c2.ticket_id_id = m.ticket_id_id
                  AND c2.message_type = 'customer'
                  AND c2.created_on < m.created_on
                  {time_filter_clause_c2}
            ))) AS time_difference_seconds
          FROM messages m
          WHERE m.message_type IN ('agent', 'chatbot')
        )
        SELECT
          'agent' AS responder_type,
          COUNT(*) AS total_count, 
          AVG(response_pairs.time_difference_seconds) AS raw_avg 
        FROM response_pairs
        WHERE message_type = 'agent' AND last_customer_msg_time IS NOT NULL

        UNION ALL

        SELECT
          'chatbot' AS responder_type,
          COUNT(*) AS total_count, 
          AVG(response_pairs.time_difference_seconds) AS raw_avg 
        FROM response_pairs
        WHERE message_type = 'chatbot' AND last_customer_msg_time IS NOT NULL;
        """
        all_params = params * 3

        return sql, all_params


class SentimentAnalysisSummaryAPIView(RawSQLQueryAPIView):
    """
    API endpoint to provide a summary of sentiment analysis (Positive, Neutral, Negative)
    for the latest analysis per ticket within a given date range.
    """

    filename_title = _("Sentiment Analysis Summary")
    serializer_class = SentimentAnalysisSummarySerializer
    table_name = "ticket_ticketanalysis"  # Primary table for general reference
    date_column_name = "updated_on"  # Date column for filtering the analysis entries

    def get_sql_query(self, time_filter_clause: str, params: list) -> tuple[str, list]:
        """
        Returns the SQL query for the sentiment analysis summary.
        The time_filter_clause and params are used for dynamic date filtering.
        """
        # The time_filter_clause will be applied to tta.updated_on
        # The params list will be used once for the time_filter_clause.

        # Ensure the time filter applies to the correct alias 'tta'
        filtered_time_clause = time_filter_clause.replace(
            self.date_column_name, "tta." + self.date_column_name
        )

        sql = f"""
        WITH latest_per_ticket AS (
          SELECT *
          FROM (
            SELECT
                tta.ticket_id,
                tta.sentiment,
                tta.updated_on,
                ROW_NUMBER() OVER (PARTITION BY tta.ticket_id ORDER BY tta.updated_on DESC) AS rn
            FROM ticket_ticketanalysis tta
            WHERE tta.sentiment IS NOT NULL
              {filtered_time_clause} -- Apply date filter here
          ) sub
          WHERE rn = 1
        )
        SELECT
          NOW() AS time, -- Timestamp of when the query was run
          COALESCE(COUNT(CASE WHEN t.sentiment = 'Positive' THEN 1 END), 0) AS "Positive",
          COALESCE(COUNT(CASE WHEN t.sentiment = 'Neutral' THEN 1 END), 0) AS "Neutral",
          COALESCE(COUNT(CASE WHEN t.sentiment = 'Negative' THEN 1 END), 0) AS "Negative"
        FROM latest_per_ticket t;
        """
        # The `params` argument already contains [start_date, end_date + 1 day]
        # and this is the only place the date filter is applied.
        return sql, params


class SentimentAnalysisTimeSeriesAPIView(RawSQLQueryAPIView):
    """
    API endpoint to provide a time series of sentiment analysis counts (Positive, Neutral, Negative)
    for the latest analysis per ticket, grouped by ticket creation date.
    """

    filename_title = _("Sentiment Analysis Count")
    serializer_class = SentimentAnalysisTimeSeriesSerializer
    table_name = "ticket_ticket"
    date_column_name = "created_on"
    time_series_granularity = "day"

    def get_sql_query(self, time_filter_clause: str, params: list) -> tuple[str, list]:
        """
        Constructs the SQL query for sentiment analysis time series data,
        ensuring all dates in the range are present with zero counts if no data exists.
        """
        date_trunc_unit = self.time_series_granularity

        # `params` received here are `[start_date, end_date + timedelta(days=1)]`
        # for the main WHERE clause filtering.
        # For generate_series, we need an inclusive end date.
        series_start_date = params[0]  # This is the actual start_date
        # Adjust end_date: params[1] is already `end_date + 1 day`, so subtract 1 day
        # to get the inclusive end for generate_series.
        series_end_date = params[1] - timedelta(days=1)

        sql = f"""
        WITH date_series AS (
          SELECT
            DATE_TRUNC('{date_trunc_unit}', generate_series AT TIME ZONE 'Asia/Bangkok') AS series_date
          FROM
            generate_series((%s::date)::timestamp AT TIME ZONE 'Asia/Bangkok', (%s::date)::timestamp AT TIME ZONE 'Asia/Bangkok', INTERVAL '1 {date_trunc_unit}') AS generate_series
        ),
        latest_per_ticket_analysis AS (
          SELECT
            sub.ticket_id,
            sub.sentiment,
            sub.updated_on
          FROM (
            SELECT
              tta_inner.*,
              ROW_NUMBER() OVER (PARTITION BY tta_inner.ticket_id ORDER BY tta_inner.updated_on DESC) AS rn
            FROM ticket_ticketanalysis tta_inner
            WHERE tta_inner.sentiment IS NOT NULL
          ) sub
          WHERE sub.rn = 1
        ),
        sentiment_data AS (
            SELECT
                DATE_TRUNC('{date_trunc_unit}', tt.created_on) AS sentiment_date,
                COUNT(CASE WHEN lpta.sentiment = 'Positive' THEN 1 END) AS positive_count,
                COUNT(CASE WHEN lpta.sentiment = 'Neutral' THEN 1 END) AS neutral_count,
                COUNT(CASE WHEN lpta.sentiment = 'Negative' THEN 1 END) AS negative_count
            FROM
                ticket_ticket tt
            JOIN
                latest_per_ticket_analysis lpta ON tt.id = lpta.ticket_id
            WHERE
                1=1 {time_filter_clause} {self.filter_conditions} -- Apply the main date filter here
            GROUP BY
                DATE_TRUNC('{date_trunc_unit}', tt.created_on)
        )
        SELECT
            ds.series_date::date AS time,
            COALESCE(sd.positive_count, 0) AS "Positive",
            COALESCE(sd.neutral_count, 0) AS "Neutral",
            COALESCE(sd.negative_count, 0) AS "Negative"
        FROM
            date_series ds
        LEFT JOIN
            sentiment_data sd ON ds.series_date = sd.sentiment_date
        ORDER BY
            ds.series_date;
        """
        all_params = [series_start_date, series_end_date] + params + self.extra_params
        return sql, all_params


class SentimentAnalysisByCaseTypeAPIView(
    RawSQLQueryAPIView
):  # Inherit from RawSQLQueryAPIView
    """
    API endpoint to provide sentiment analysis counts (Positive, Neutral, Negative)
    broken down by case type, based on the latest analysis per ticket
    within a given date range (filtered by ticket_ticketanalysis.created_on).
    """

    filename_title = _("Sentiment Analysis Counts By Case Type")
    serializer_class = SentimentAnalysisByCaseTypeSerializer
    table_name = "ticket_ticketanalysis"  # Primary table for date filtering
    date_column_name = "created_on"  # Date column for filtering the analysis entries

    def get_sql_query(self, time_filter_clause: str, params: list) -> tuple[str, list]:
        """
        Returns the SQL query for sentiment analysis by case type.
        The time_filter_clause and params are used for dynamic date filtering.
        """
        # The time_filter_clause will be applied to tta.created_on in latest_per_ticket CTE.
        # The params list will be used once for the time_filter_clause.

        # Ensure the time filter applies to the correct alias 'tta'
        # The time_filter_clause already uses self.date_column_name, which is 'created_on'.
        # We need to apply it to the 'created_on' column within ticket_ticketanalysis.
        # The template already has 'created_on' in the CTE, so just insert the clause.

        sql = f"""
        WITH latest_per_ticket AS (
          SELECT
            ticket_id,
            sentiment,
            created_on, -- Keep created_on here for the time filter
            ROW_NUMBER() OVER (PARTITION BY ticket_id ORDER BY updated_on DESC) AS rn
          FROM ticket_ticketanalysis tta -- Alias for clarity
          WHERE sentiment IS NOT NULL
            {time_filter_clause} -- Apply time filter here to tta.created_on
        ),
        latest_filtered_tickets AS (
            SELECT
                ticket_id,
                sentiment
            FROM latest_per_ticket
            WHERE rn = 1
        ),
        ticket_topics_with_case_type AS (
            SELECT
                tttop.ticket_id,
                ttt.case_type
            FROM ticket_ticket_topics tttop
            JOIN ticket_tickettopic ttt ON tttop.tickettopic_id = ttt.id
        )
        SELECT
            ttc.case_type AS "ประเภทเคส",
            COALESCE(COUNT(CASE WHEN lft.sentiment = 'Positive' THEN 1 END), 0) AS "Positive",
            COALESCE(COUNT(CASE WHEN lft.sentiment = 'Neutral' THEN 1 END), 0) AS "Neutral",
            COALESCE(COUNT(CASE WHEN lft.sentiment = 'Negative' THEN 1 END), 0) AS "Negative"
        FROM latest_filtered_tickets lft
        JOIN ticket_topics_with_case_type ttc ON lft.ticket_id = ttc.ticket_id
        GROUP BY ttc.case_type
        HAVING COUNT(*) > 0
        ORDER BY
            (COUNT(CASE WHEN lft.sentiment = 'Positive' THEN 1 END) +
             COUNT(CASE WHEN lft.sentiment = 'Neutral' THEN 1 END) +
             COUNT(CASE WHEN lft.sentiment = 'Negative' THEN 1 END)) DESC;
        """
        # The `params` argument already contains [start_date, end_date + 1 day]
        # and this is the only place the date filter is applied.
        return sql, params


##########################################################################################
# VIEWs for the [Response Time Volume] dashboard API endpoints
##########################################################################################


class IncomingTicketCountAPIView(RawSQLMetricComparisonAPIView):
    """
    API endpoint to count and compare distinct incoming tickets (is_self = 'false')
    between two time periods, providing time-series data.
    """

    serializer_class = MessageSummarySerializer  # Use your specific serializer

    filename_title = _("Incoming Ticket Count")
    table_name = "ticket_message"
    date_column_name = "created_on"
    filter_conditions = "AND is_self = 'false'"
    metric_aggregate_sql = "COUNT(DISTINCT ticket_id_id)"
    units = "tickets"

    def get_sql_query(self, time_filter_clause: str, params: list) -> tuple[str, list]:
        """
        Constructs the raw SQL query to count ticket messages, including
        the time filter from the parent class.
        """
        sql_query = f"""
        SELECT
          COUNT(*) AS "จำนวนข้อความขาเข้า"
        FROM
          ticket_message
        WHERE
          1=1 {time_filter_clause};
        """

    def get_time_series_metric_sql(
        self, time_filter_clause: str, start_date: date, end_date: date
    ) -> tuple[str, list]:
        """
        Constructs a SQL query that generates a full date series and then
        left-joins the ticket count data to ensure all days are included.
        """
        sql = f"""
        WITH date_series AS (
            SELECT
                generate_series(
                    %s::timestamp,
                    %s::timestamp,
                    '1 day'::interval
                ) AS date_bucket
        ),
        ticket_counts AS (
            SELECT
                DATE_TRUNC('day', created_on AT TIME ZONE 'Asia/Bangkok') AS date_bucket,
                COUNT(*) AS count
            FROM
                ticket_message
            WHERE
                1=1 {time_filter_clause}
            GROUP BY
                date_bucket
        )
        SELECT
            ds.date_bucket AS date,
            COALESCE(tc.count, 0) AS value
        FROM
            date_series ds
        LEFT JOIN
            ticket_counts tc ON ds.date_bucket = tc.date_bucket
        ORDER BY
            ds.date_bucket;
        """
        # The generate_series requires two parameters. The time_filter_clause requires two more.
        params = [
            start_date,
            end_date + timedelta(days=1),
            start_date,
            end_date + timedelta(days=1),
        ]

        return sql, params


class TicketCategoryTotalCountAPIView(RawSQLQueryAPIView):
    """
    API endpoint to provide a breakdown of tickets categorized by the system,
    grouped by category, for a given date range.
    """

    filename_title = _("Ticket Category Total Count")
    serializer_class = TicketCategoryBreakdownSerializer
    table_name = "ticket_ticketanalysis"  # Primary table for general reference
    date_column_name = "created_on"  # Date filter applies here

    def get_sql_query(self, time_filter_clause: str, params: list) -> tuple[str, list]:
        """
        Returns SQL to get the breakdown of ticket counts per category.
        The time_filter_clause will apply to ticket_ticketanalysis.created_on.
        """
        # Base CTE for latest ticket analysis entries, including the categorization logic.
        # The time_filter_clause will apply to ticket_ticketanalysis.created_on.
        base_cte = f"""
        WITH latest_per_ticket AS (
          SELECT *
          FROM (
            SELECT
                id,
                ticket_id,
                updated_on,
                created_on, -- Ensure created_on is selected for date filtering
                CAST(is_faq AS BOOLEAN) AS is_faq,
                CAST(is_recommendation AS BOOLEAN) AS is_recommendation,
                CAST(is_renewal AS BOOLEAN) AS is_renewal,
                CAST(is_claim AS BOOLEAN) AS is_claim,
                CAST(is_complain AS BOOLEAN) AS is_complain,
                CAST(is_insurance_policy AS BOOLEAN) AS is_insurance_policy,
                ROW_NUMBER() OVER (PARTITION BY ticket_id ORDER BY updated_on DESC) AS rn
            FROM ticket_ticketanalysis
            WHERE 1=1 {time_filter_clause} -- Apply date filter here directly
          ) sub
          WHERE rn = 1
        )
        """

        sql = f"""
        {base_cte}
        SELECT
            CASE
                WHEN is_faq THEN 'FAQ Service'
                WHEN is_recommendation THEN 'Recommendation Service'
                WHEN is_renewal THEN 'Renewal Service'
                WHEN is_claim THEN 'Claim Service'
                WHEN is_complain THEN 'Complaint Service'
                WHEN is_insurance_policy THEN 'Insurance Policy Service'
                ELSE 'Other Services'
            END AS category,
            COUNT(*) AS ticket_count
        FROM latest_per_ticket
        WHERE
            is_faq OR is_recommendation OR is_renewal OR is_claim OR
            is_complain OR is_insurance_policy OR
            (NOT is_faq AND NOT is_recommendation AND NOT is_renewal AND NOT is_claim AND NOT is_complain AND NOT is_insurance_policy)
        GROUP BY category
        HAVING COUNT(*) > 0
        ORDER BY ticket_count DESC;
        """
        # Parameters for time_filter_clause (2 parameters: start_date, end_date+1)
        # The base_cte uses the time_filter_clause once.
        params_for_sql = params

        return sql, params_for_sql


class IncomingMessageCountTimeSeriesAPIView(RawSQLQueryAPIView):
    """
    API endpoint to provide a time series of incoming customer message counts.
    Supports time filtering and ensures zero counts for periods with no data.
    """

    filename_title = _("Incoming Message Count")
    serializer_class = IncomingMessageCountTimeSeriesSerializer
    table_name = "ticket_message"
    date_column_name = "created_on"
    time_series_granularity = "day"

    # Store parsed dates from get() so get_sql_query can access them directly for generate_series
    _parsed_start_date = None
    _parsed_end_date = None

    def get(self, request, *args, **kwargs):
        default_end_date = date(2025, 7, 12)
        default_start_date = default_end_date - timedelta(days=6)

        user_start_date_str = request.query_params.get("start_date")
        user_end_date_str = request.query_params.get("end_date")

        try:
            if user_start_date_str:
                start_date = datetime.strptime(user_start_date_str, "%Y-%m-%d").date()
            else:
                start_date = default_start_date

            if user_end_date_str:
                end_date = datetime.strptime(user_end_date_str, "%Y-%m-%d").date()
            else:
                end_date = default_end_date

        except ValueError:
            return Response(
                {
                    "error": "Invalid date format. Use YYYY-MM-DD for start_date and end_date."
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        if start_date > end_date:
            return Response(
                {"error": "start_date cannot be after end_date."},
                status=status.HTTP_400_BAD_REQUEST,
            )

        # Determine time series granularity based on duration
        duration_timedelta = end_date - start_date
        if duration_timedelta > timedelta(days=364):
            self.time_series_granularity = "month"
        elif duration_timedelta > timedelta(days=30):
            self.time_series_granularity = "week"
        else:
            self.time_series_granularity = "day"

        # --- Store parsed dates for use in get_sql_query ---
        self._parsed_start_date = start_date
        self._parsed_end_date = end_date
        # --- End store parsed dates ---

        return super().get(request, *args, **kwargs)

    def get_sql_query(self, time_filter_clause: str, params: list) -> tuple[str, list]:
        """
        Returns the SQL query for the incoming message count time series,
        ensuring zero counts for periods with no data.
        """
        # Use the stored parsed dates for generate_series to ensure correct range
        series_start_date = self._parsed_start_date
        series_end_date = self._parsed_end_date  # Use the user's end_date directly

        # For generate_series, if granularity is 'day', we need to go up to and include end_date.
        # If granularity is 'week' or 'month', DATE_TRUNC will handle it.
        # The `params` list for time_filter_clause is [start_date, end_date + 1 day].
        # So for generate_series, we will use the actual start_date and end_date.

        sql_parts = []
        sql_parts.append("WITH date_series AS (\n")
        # Generate series from start_date up to and including end_date in local timezone
        sql_parts.append(
            f"  SELECT DATE_TRUNC('{self.time_series_granularity}', generate_series AT TIME ZONE 'Asia/Bangkok') AS series_date\n"
        )
        sql_parts.append(
            f"  FROM generate_series((%s::date)::timestamp AT TIME ZONE 'Asia/Bangkok', (%s::date)::timestamp AT TIME ZONE 'Asia/Bangkok', INTERVAL '1 {self.time_series_granularity}') AS generate_series\n"
        )
        sql_parts.append("),\n")
        sql_parts.append("message_counts AS (\n")
        sql_parts.append(
            f"  SELECT DATE_TRUNC('{self.time_series_granularity}', created_on AT TIME ZONE 'Asia/Bangkok') AS message_date,\n"
        )
        sql_parts.append("    COUNT(*) AS count\n")
        sql_parts.append("  FROM ticket_message\n")
        sql_parts.append("  WHERE is_self = false\n")
        sql_parts.append(f"    {time_filter_clause}\n")
        sql_parts.append(
            f"  GROUP BY DATE_TRUNC('{self.time_series_granularity}', created_on AT TIME ZONE 'Asia/Bangkok')\n"
        )
        sql_parts.append(")\n")
        sql_parts.append("SELECT\n")
        sql_parts.append(
            "  ds.series_date::date AS time, -- --- CAST TO DATE HERE --- \n"
        )
        sql_parts.append('  COALESCE(mc.count, 0) AS "จำนวนข้อความขาเข้า"\n')
        sql_parts.append("FROM\n")
        sql_parts.append("  date_series ds\n")
        sql_parts.append("LEFT JOIN\n")
        sql_parts.append("  message_counts mc ON ds.series_date = mc.message_date\n")
        sql_parts.append("ORDER BY\n")
        sql_parts.append("  ds.series_date;\n")

        sql = "".join(sql_parts)

        # Parameters:
        # 1. series_start_date (for generate_series) -> self._parsed_start_date
        # 2. series_end_date (for generate_series) -> self._parsed_end_date
        # 3. start_date (for time_filter_clause) -> params[0]
        # 4. end_date + 1 day (for time_filter_clause) -> params[1]

        # Combine parameters in the correct order
        # The `params` list from the base class is [start_date, end_date + 1 day]
        # So we need to use self._parsed_start_date and self._parsed_end_date for generate_series,
        # and params[0], params[1] for time_filter_clause.
        all_params = [series_start_date, series_end_date] + params

        return sql, all_params


class CustomerMessageHeatmapAPIView(RawSQLQueryAPIView):
    """
    API endpoint to provide a heatmap-like breakdown of customer message counts
    by hour of the day and day of the week.
    Supports time filtering.
    """

    filename_title = _("Customer Message Heatmap")
    serializer_class = CustomerMessageHeatmapSerializer
    table_name = "ticket_message"
    date_column_name = "created_on"

    def get_sql_query(self, time_filter_clause: str, params: list) -> tuple[str, list]:
        """
        Returns the SQL query for the customer message heatmap.
        The time_filter_clause and params are used for dynamic date filtering.
        """
        # Explicitly build the SQL string to avoid any hidden characters or f-string issues
        sql_parts = []
        sql_parts.append(
            """
        WITH hour_slots AS (
          SELECT generate_series(0, 23) AS hour
        ),
        message_data AS (
          SELECT
            EXTRACT(HOUR FROM created_on AT TIME ZONE 'Asia/Bangkok') AS hour,
            EXTRACT(DOW FROM created_on AT TIME ZONE 'Asia/Bangkok') AS dow -- DOW returns 0 for Sunday, 1 for Monday, etc.
          FROM
            ticket_message
          WHERE
            is_self = false  -- only customer messages
        """
        )
        # Add the time_filter_clause directly. This is where the 2 %s placeholders come from.
        sql_parts.append(time_filter_clause)
        sql_parts.append(
            """
        )
        SELECT
          TO_CHAR(make_timestamp(2000,1,1,hs.hour,0,0), 'HH24:MI') || '-' ||
          TO_CHAR(make_timestamp(2000,1,1,hs.hour + 1,0,0), 'HH24:MI') AS "ช่วงเวลา",
          COALESCE(COUNT(CASE WHEN md.dow = 1 THEN 1 END), 0) AS "จันทร์",
          COALESCE(COUNT(CASE WHEN md.dow = 2 THEN 1 END), 0) AS "อังคาร",
          COALESCE(COUNT(CASE WHEN md.dow = 3 THEN 1 END), 0) AS "พุธ",
          COALESCE(COUNT(CASE WHEN md.dow = 4 THEN 1 END), 0) AS "พฤหัสบดี",
          COALESCE(COUNT(CASE WHEN md.dow = 5 THEN 1 END), 0) AS "ศุกร์",
          COALESCE(COUNT(CASE WHEN md.dow = 6 THEN 1 END), 0) AS "เสาร์",
          COALESCE(COUNT(CASE WHEN md.dow = 0 THEN 1 END), 0) AS "อาทิตย์"
        FROM
          hour_slots hs
        LEFT JOIN
          message_data md ON hs.hour = md.hour
        GROUP BY
          hs.hour
        ORDER BY
          hs.hour;
        """
        )

        sql = "".join(sql_parts)

        # The `params` argument already contains [start_date, end_date + 1 day]
        # and this is the only place the date filter is applied.
        return sql, params


##########################################################################################
# VIEWs for the [Agent Performance] dashboard API endpoints
##########################################################################################


class AgentPerformanceSummaryAPIView(RawSQLQueryAPIView):
    serializer_class = AgentPerformanceSummarySerializer
    date_column_name = "created_on"
    units = "minutes"
    filename_title = _("Agent Performance Summary")

    def get_agents_filter_sql_and_params(self, request) -> tuple[str, list]:
        agent_ids_str = request.query_params.get("agent_ids")
        if agent_ids_str:
            try:
                agent_ids = [int(aid.strip()) for aid in agent_ids_str.split(",") if aid.strip()]
                if agent_ids:
                    placeholders = ", ".join(["%s"] * len(agent_ids))
                    return f" AND name IN (SELECT LOWER(TRIM(name)) FROM user_user WHERE id IN ({placeholders}))", agent_ids
            except ValueError:
                pass
        
        agents_str = request.query_params.get("agents")
        if agents_str:
            agent_names = [LOWER(TRIM(agent)) for agent in agents_str.split(",") if agent.strip()]
            if agent_names:
                placeholders = ", ".join(["%s"] * len(agent_names))
                return f" AND name IN ({placeholders})", agent_names

        return "", []

    def get_sql_query(self, time_filter_clause: str, params: list) -> tuple[str, list]:
        agent_filter_sql_part, agent_filter_params = (
            self.get_agents_filter_sql_and_params(self.request)
        )
        
        all_params = []
        all_params.extend(params)
        all_params.extend(params)
        all_params.extend(params)
        
        if agent_filter_params:
            all_params.extend(agent_filter_params)

        sql = f"""
        WITH all_agents AS (
            SELECT DISTINCT LOWER(TRIM(name)) AS name FROM user_user
            UNION
            SELECT DISTINCT LOWER(TRIM(user_name)) AS name FROM ticket_message WHERE is_self = true
        ),
        agent_messages AS (
            SELECT
                tm.ticket_id_id,
                tm.created_on AS agent_msg_time,
                LOWER(TRIM(tm.user_name)) AS user_name,
                tm.created_by_id,
                tm.is_self
            FROM ticket_message tm
            WHERE tm.is_self = true
            AND tm.created_on >= %s AND tm.created_on < %s
        ),
        agent_responses AS (
            SELECT
                a.ticket_id_id,
                a.created_by_id,
                a.user_name,
                a.agent_msg_time,
                (
                    SELECT MAX(c.created_on)
                    FROM ticket_message c
                    WHERE c.ticket_id_id = a.ticket_id_id
                      AND c.is_self = false
                      AND c.created_on < a.agent_msg_time
                ) AS last_customer_msg_time
            FROM agent_messages a
        ),
        avg_response_time_per_agent AS (
            SELECT
                LOWER(TRIM(COALESCE(uu.name, ar.user_name))) AS agent_name,
                AVG(EXTRACT(EPOCH FROM (ar.agent_msg_time - ar.last_customer_msg_time)) / 60.0) AS avg_response_time_minutes
            FROM agent_responses ar
            LEFT JOIN user_user uu ON ar.created_by_id = uu.id
            WHERE ar.last_customer_msg_time IS NOT NULL
            GROUP BY agent_name
        ),
        ordered_status AS (
            SELECT
                tsl.ticket_id_id,
                tsl.created_by_id,
                tsl.status_id_id,
                tsl.created_on,
                LEAD(tsl.status_id_id) OVER (PARTITION BY tsl.ticket_id_id ORDER BY tsl.created_on) AS next_status,
                LEAD(tsl.created_by_id) OVER (PARTITION BY tsl.ticket_id_id ORDER BY tsl.created_on) AS next_by,
                LEAD(tsl.created_on) OVER (PARTITION BY tsl.ticket_id_id ORDER BY tsl.created_on) AS next_time
            FROM ticket_statuslog tsl
            WHERE tsl.status_id_id IN (3, 6)
            AND tsl.created_on >= %s AND tsl.created_on < %s
        ),
        handling_intervals AS (
            SELECT
                ticket_id_id,
                created_by_id,
                next_by,
                created_on AS start_time,
                next_time AS end_time,
                status_id_id AS start_status,
                next_status AS end_status,
                EXTRACT(EPOCH FROM (next_time - created_on)) / 60 AS interval_minutes
            FROM ordered_status
            WHERE next_time IS NOT NULL
        ),
        filtered_intervals AS (
            SELECT *
            FROM handling_intervals
            WHERE
                (
                    start_status = 3 AND end_status = 6 AND created_by_id = next_by
                )
                OR
                (
                    start_status = 3 AND end_status = 3 AND created_by_id <> next_by
                )
        ),
        avg_handling_time_per_agent AS (
            SELECT
                LOWER(TRIM(u.name)) AS agent_name,
                AVG(f.interval_minutes) AS avg_handling_time_minutes
            FROM filtered_intervals f
            JOIN user_user u ON f.created_by_id = u.id
            GROUP BY agent_name
        ),
        avg_csat_score_per_agent AS (
            SELECT
                LOWER(TRIM(uu.name)) AS agent_name,
                AVG(CAST(tt.feedback->>'csat' AS DECIMAL)) AS average_csat_score
            FROM
                ticket_ticket tt
            JOIN
                user_user uu ON tt.owner_id_id = uu.id
            WHERE
                tt.feedback IS NOT NULL
                AND tt.feedback->>'csat' IS NOT NULL
                AND tt.feedback->>'csat' ~ '^\d+$'
                AND tt.owner_id_id IS NOT NULL
                AND tt.created_on >= %s AND tt.created_on < %s
            GROUP BY agent_name
        )
        SELECT
            a.name AS agent_name,
            ROUND(art.avg_response_time_minutes, 2) AS avg_response_time_minutes,
            ROUND(aht.avg_handling_time_minutes, 2) AS "เวลาจัดการเฉลี่ย (นาที)",
            ROUND(acs.average_csat_score, 2) AS "ความพึงพอใจเฉลี่ย"
        FROM all_agents a
        LEFT JOIN avg_response_time_per_agent art ON a.name = art.agent_name
        LEFT JOIN avg_handling_time_per_agent aht ON a.name = aht.agent_name
        LEFT JOIN avg_csat_score_per_agent acs ON a.name = acs.agent_name
        WHERE 1=1 {agent_filter_sql_part}
        ORDER BY a.name ASC;
        """

        return sql, all_params

class AgentPreviousAssignmentCountAPIView(RawSQLMetricComparisonPerAgentAPIView):
    """
    API endpoint to compare the count of tickets where an agent was the previous handler
    before the ticket was assigned (status_id = 3).
    Provides comparison and time-series data PER AGENT.
    """

    # serializer_class is now handled by the base class (AgentMetricSummarySerializer)
    filename_title = _("Agent Previous Assignment Count")
    table_name = "ticket_statuslog"
    date_column_name = "created_on"
    units = "tickets"
    serializer_class = AgentMetricSummarySerializer
    metric_aggregate_sql = "COUNT"

    def get_all_agents_sql(self) -> tuple[str, list]:
        """Returns SQL to fetch all agents (id, name)."""
        return "SELECT id, name FROM user_user;", []

    def _get_base_previous_assignment_ctes(
        self, time_filter_clause: str, agent_id: int
    ) -> str:
        """Helper to get the common CTEs for previous assignment count, filtered by agent_id."""
        # Qualify created_on with tsl. within the CTE's WHERE clause
        qualified_time_filter_clause = time_filter_clause.replace(
            "created_on", "tsl.created_on"
        )
        return f"""
        WITH ordered_log AS (
            SELECT
                id,
                ticket_id_id,
                status_id_id,
                created_on,
                created_by_id,
                LAG(created_by_id) OVER (PARTITION BY ticket_id_id ORDER BY created_on) AS previous_created_by
            FROM ticket_statuslog tsl
            WHERE 1=1 {qualified_time_filter_clause}
              AND tsl.created_by_id = %s -- Filter by agent_id here
        )
        """

    def get_single_period_metric_sql(
        self, time_filter_clause: str, start_date: date, end_date: date, agent_id: int
    ) -> tuple[str, list]:
        """
        Returns SQL for a single metric value (total count of previous assignments) for a specific agent.
        """
        base_ctes = self._get_base_previous_assignment_ctes(
            time_filter_clause, agent_id
        )

        sql = f"""
        {base_ctes}
        SELECT
            COUNT(*) AS metric_value
        FROM ordered_log ol
        WHERE ol.status_id_id = 3
          AND ol.previous_created_by = %s -- Ensure previous_created_by matches agent_id
        ;
        """
        # Parameters: 2 for time_filter_clause + 1 for tsl.created_by_id + 1 for ol.previous_created_by
        params_for_sql = [start_date, end_date, agent_id, agent_id]
        return sql, params_for_sql

    def get_time_series_metric_sql(
        self, time_filter_clause: str, start_date: date, end_date: date, agent_id: int
    ) -> tuple[str, list]:
        """
        Returns SQL for time-series data of previous assignment count for a specific agent.
        """
        date_trunc_unit = self.time_series_granularity
        base_ctes = self._get_base_previous_assignment_ctes(
            time_filter_clause, agent_id
        )

        sql = f"""
        {base_ctes}
        SELECT
            DATE_TRUNC('{date_trunc_unit}', ol.created_on) AS date,
            COUNT(*) AS value
        FROM ordered_log ol
        WHERE ol.status_id_id = 3
          AND ol.previous_created_by = %s -- Ensure previous_created_by matches agent_id
        GROUP BY DATE_TRUNC('{date_trunc_unit}', ol.created_on)
        ORDER BY date;
        """
        # Parameters: 2 for time_filter_clause + 1 for tsl.created_by_id + 1 for ol.previous_created_by
        params_for_sql = [start_date, end_date, agent_id, agent_id]
        return sql, params_for_sql


class AgentAssignedTicketsCountAPIView(RawSQLMetricComparisonPerAgentAPIView):
    """
    API endpoint to compare the count of tickets where an agent was the previous handler
    before the ticket was assigned (status_id = 3).
    Provides comparison and time-series data PER AGENT.
    """

    filename_title = _("Agent Assigned Tickets Count")
    table_name = "ticket_statuslog"
    date_column_name = "created_on"
    units = "tickets"
    metric_aggregate_sql = "COUNT"
    serializer_class = AgentMetricSummarySerializer

    def get_all_agents_sql(self) -> tuple[str, list]:
        """Returns SQL to fetch all agents (id, name)."""
        return "SELECT id, name FROM user_user;", []

    def _get_base_previous_assignment_ctes(
        self, time_filter_clause: str, agent_id: int
    ) -> str:
        """Helper to get the common CTEs for previous assignment count, filtered by agent_id."""
        # Qualify created_on with tsl. within the CTE's WHERE clause
        qualified_time_filter_clause = time_filter_clause.replace(
            "created_on", "tsl.created_on"
        )
        return f"""
        WITH ordered_log AS (
            SELECT
                id,
                ticket_id_id,
                status_id_id,
                created_on,
                created_by_id,
                LAG(created_by_id) OVER (PARTITION BY ticket_id_id ORDER BY created_on) AS previous_created_by
            FROM ticket_statuslog tsl
            WHERE 1=1 {qualified_time_filter_clause}
              AND tsl.created_by_id = %s -- Filter by agent_id here
        )
        """

    def get_single_period_metric_sql(
        self, time_filter_clause: str, start_date: date, end_date: date, agent_id: int
    ) -> tuple[str, list]:
        """
        Returns SQL for a single metric value (total count of previous assignments) for a specific agent.
        """
        base_ctes = self._get_base_previous_assignment_ctes(
            time_filter_clause, agent_id
        )

        sql = f"""
        {base_ctes}
        SELECT
            COUNT(*) AS metric_value
        FROM ordered_log ol
        WHERE ol.status_id_id = 3
          AND ol.previous_created_by = %s -- Ensure previous_created_by matches agent_id
        ;
        """
        # Parameters: 2 for time_filter_clause + 1 for tsl.created_by_id + 1 for ol.previous_created_by
        params_for_sql = [start_date, end_date + timedelta(days=1), agent_id, agent_id]
        return sql, params_for_sql

    def get_time_series_metric_sql(
        self, time_filter_clause: str, start_date: date, end_date: date, agent_id: int
    ) -> tuple[str, list]:
        """
        Returns SQL for time-series data of previous assignment count for a specific agent.
        """
        date_trunc_unit = self.time_series_granularity
        base_ctes = self._get_base_previous_assignment_ctes(
            time_filter_clause, agent_id
        )

        sql = f"""
        {base_ctes}
        SELECT
            DATE_TRUNC('{date_trunc_unit}', ol.created_on) AS date,
            COUNT(*) AS value
        FROM ordered_log ol
        WHERE ol.status_id_id = 3
          AND ol.previous_created_by = %s -- Ensure previous_created_by matches agent_id
        GROUP BY DATE_TRUNC('{date_trunc_unit}', ol.created_on)
        ORDER BY date;
        """
        # Parameters: 2 for time_filter_clause + 1 for tsl.created_by_id + 1 for ol.previous_created_by
        params_for_sql = [start_date, end_date + timedelta(days=1), agent_id, agent_id]
        return sql, params_for_sql


class AgentResponseRateWithin5MinAPIView(RawSQLMetricComparisonPerAgentAPIView):
    """
    API endpoint to compare the percentage of agent responses within 5 minutes.
    Provides comparison and time-series data PER AGENT.
    """

    filename_title = _("Agent Response Rate Within 5 Minutes")
    table_name = "ticket_message"
    date_column_name = "created_on"
    units = "%"
    metric_aggregate_sql = "AVG"
    serializer_class = AgentMetricSummarySerializer

    def get_all_agents_sql(self) -> tuple[str, list]:
        """Returns SQL to fetch all agents (id, name)."""
        # For this metric, agents are identified by created_by_id in ticket_message
        return (
            """
            SELECT DISTINCT uu.id, uu.name
            FROM ticket_message tm
            JOIN user_user uu ON tm.created_by_id = uu.id
            WHERE tm.is_self = true AND tm.created_by_id IS NOT NULL;
        """,
            [],
        )

    def _get_base_response_rate_ctes(
        self, time_filter_clause: str, agent_id: int
    ) -> str:
        """Helper to get the common CTEs for response rate calculation, filtered by agent_id."""
        qualified_time_filter_clause_tm = time_filter_clause.replace(
            "created_on", "tm.created_on"
        )
        qualified_time_filter_clause_c = time_filter_clause.replace(
            "created_on", "c.created_on"
        )

        return f"""
        WITH agent_messages AS (
          SELECT
            ticket_id_id,
            created_on AS agent_msg_time,
            user_name,
            created_by_id, -- --- INCLUDE created_by_id HERE ---
            is_self,
            CASE
              WHEN is_self = true AND created_by_id IS NULL THEN true
              WHEN is_self = true AND created_by_id IS NOT NULL AND lower(user_name) = 'system' THEN true
              ELSE false
            END AS is_chatbot
          FROM ticket_message tm
          WHERE tm.is_self = true
            AND tm.created_by_id = %s -- Filter by agent_id here
            {qualified_time_filter_clause_tm}
        ),
        agent_responses AS (
          SELECT
            a.ticket_id_id,
            a.agent_msg_time,
            a.user_name,
            a.created_by_id, -- --- INCLUDE created_by_id HERE ---
            (
              SELECT MAX(c.created_on)
              FROM ticket_message c
              WHERE c.ticket_id_id = a.ticket_id_id
                AND c.is_self = false -- customer messages only
                AND c.created_on < a.agent_msg_time
                {qualified_time_filter_clause_c}
            ) AS last_customer_msg_time
          FROM agent_messages a
          WHERE a.is_chatbot = false
        ),
        response_timings AS (
          SELECT
            user_name,
            agent_msg_time,
            last_customer_msg_time,
            created_by_id, -- --- INCLUDE created_by_id HERE ---
            EXTRACT(EPOCH FROM (agent_msg_time - last_customer_msg_time)) / 60.0 AS response_time_minutes
          FROM agent_responses
          WHERE last_customer_msg_time IS NOT NULL
        )
        """

    def get_single_period_metric_sql(
        self, time_filter_clause: str, start_date: date, end_date: date, agent_id: int
    ) -> tuple[str, list]:
        base_ctes = self._get_base_response_rate_ctes(time_filter_clause, agent_id)

        sql = f"""
        {base_ctes}
        SELECT
          COALESCE(
            COUNT(CASE WHEN response_time_minutes <= 5 THEN 1 END) * 100.0 / NULLIF(COUNT(*), 0)
          , 0) AS metric_value
        FROM response_timings
        WHERE created_by_id = %s -- --- NOW created_by_id IS AVAILABLE HERE ---
        ;
        """
        # Parameters: 1 for tm.created_by_id + 2 for tm.created_on + 2 for c.created_on + 1 for final created_by_id
        params_for_sql = [
            agent_id,
            start_date,
            end_date,
            start_date,
            end_date,
            agent_id,
        ]
        return sql, params_for_sql

    def get_time_series_metric_sql(
        self, time_filter_clause: str, start_date: date, end_date: date, agent_id: int
    ) -> tuple[str, list]:
        date_trunc_unit = self.time_series_granularity
        base_ctes = self._get_base_response_rate_ctes(time_filter_clause, agent_id)

        sql = f"""
        {base_ctes}
        SELECT
          DATE_TRUNC('{date_trunc_unit}', agent_msg_time AT TIME ZONE 'Asia/Bangkok') AS date,
          COALESCE(
            COUNT(CASE WHEN response_time_minutes <= 5 THEN 1 END) * 100.0 / NULLIF(COUNT(*), 0)
          , 0) AS value
        FROM response_timings
        WHERE created_by_id = %s -- --- NOW created_by_id IS AVAILABLE HERE ---
        GROUP BY DATE_TRUNC('{date_trunc_unit}', agent_msg_time AT TIME ZONE 'Asia/Bangkok')
        ORDER BY date;
        """
        # Parameters: 1 for tm.created_by_id + 2 for tm.created_on + 2 for c.created_on + 1 for final created_by_id
        params_for_sql = [
            agent_id,
            start_date,
            end_date,
            start_date,
            end_date,
            agent_id,
        ]
        return sql, params_for_sql


class ComprehensiveAgentPerformanceAPIView(RawSQLQueryAPIView):
    """
    API endpoint to retrieve a comprehensive summary of agent performance,
    including closed/unclosed ticket counts, average response time,
    average handling time, and average CSAT score.
    Supports time filtering and optional filtering by specific agent names or IDs.
    """

    filename_title = _("Comprehensive Agent Performance")
    serializer_class = ComprehensiveAgentPerformanceSerializer
    date_column_name = "created_on"
    table_name = "ticket_ticket"

    def get_agents_filter_sql_and_params(self, request) -> tuple[str, list]:
        """
        Helper to get the SQL filter clause and parameters for agent names or IDs.
        Prioritizes filtering by 'agent_ids' if provided. Otherwise, uses 'agents' (names).
        """
        agent_ids_str = request.query_params.get("agent_ids")
        if agent_ids_str:
            try:
                agent_ids = [
                    int(aid.strip()) for aid in agent_ids_str.split(",") if aid.strip()
                ]
                if agent_ids:
                    placeholders = ", ".join(["%s"] * len(agent_ids))
                    # --- FIX: Use alias 'u' which is defined in the main query's FROM clause ---
                    return f" AND u.id IN ({placeholders})", agent_ids
            except ValueError:
                # Log or handle invalid agent_ids if necessary
                pass

        agents_str = request.query_params.get("agents")
        if agents_str:
            agent_names = [
                agent.strip() for agent in agents_str.split(",") if agent.strip()
            ]
            if agent_names:
                placeholders = ", ".join(["%s"] * len(agent_names))
                # --- FIX: Use alias 'u' which is defined in the main query's FROM clause ---
                return f" AND u.name IN ({placeholders})", agent_names

        return "", []

    def get_sql_query(self, time_filter_clause: str, params: list) -> tuple[str, list]:
        # The number of date parameters for each CTE that uses the time filter.
        # This is a bit fragile, so careful review is needed.
        # 1. agent_messages CTE
        # 2. agent_responses subquery (inside agent_responses CTE)
        # 3. agent_ticket_status CTE
        # 4. latest_status_per_ticket CTE
        # 5. agent_handling_intervals_base CTE
        # 6. avg_csat_score CTE

        # We need to append `params` 6 times, once for each occurrence of the date filter.
        all_params = []
        all_params.extend(params)  # 1. agent_messages
        all_params.extend(params)  # 2. agent_responses subquery
        all_params.extend(params)  # 3. agent_ticket_status
        all_params.extend(params)  # 4. latest_status_per_ticket
        all_params.extend(params)  # 5. agent_handling_intervals_base
        all_params.extend(params)  # 6. avg_csat_score

        # Get the agent filter part and append its parameters to the end.
        agent_filter_sql_part, agent_filter_params = (
            self.get_agents_filter_sql_and_params(self.request)
        )
        all_params.extend(agent_filter_params)

        sql = f"""
        -- CTE: Calculate agent average response time
        WITH agent_messages AS (
          SELECT
            tm.ticket_id_id,
            tm.created_on AS agent_msg_time,
            tm.user_name,
            tm.created_by_id,
            tm.is_self,
            CASE
              WHEN tm.is_self = true AND tm.created_by_id IS NULL THEN true
              WHEN tm.is_self = true AND tm.created_by_id IS NOT NULL AND lower(tm.user_name) = 'system' THEN true
              ELSE false
            END AS is_chatbot
          FROM ticket_message tm
          WHERE tm.is_self = true
            {time_filter_clause.replace("created_on", "tm.created_on")}
        ),
        agent_responses AS (
          SELECT
            a.ticket_id_id,
            a.agent_msg_time,
            a.user_name,
            (
              SELECT MAX(c.created_on)
              FROM ticket_message c
              WHERE c.ticket_id_id = a.ticket_id_id
                AND c.is_self = false -- customer messages only
                AND c.created_on < a.agent_msg_time
                {time_filter_clause.replace("created_on", "c.created_on")}
            ) AS last_customer_msg_time
          FROM agent_messages a
          WHERE a.is_chatbot = false
        ),
        avg_response_time AS (
          SELECT
            user_name,
            AVG(EXTRACT(EPOCH FROM (agent_msg_time - last_customer_msg_time)) / 60.0) AS avg_response_minutes
          FROM agent_responses
          WHERE last_customer_msg_time IS NOT NULL
          GROUP BY user_name
        ),

        -- CTE: Ticket status logs by agent for counts
        agent_ticket_status AS (
          SELECT
            tsl.ticket_id_id,
            tsl.created_by_id,
            tsl.status_id_id,
            tsl.created_on
          FROM ticket_statuslog tsl
          WHERE tsl.created_by_id IS NOT NULL
            AND lower(tsl.created_by_id::text) != 'system'
            {time_filter_clause.replace("created_on", "tsl.created_on")}
        ),

        -- CTE: Latest status per ticket for unclosed count
        latest_status_per_ticket AS (
          SELECT DISTINCT ON (ticket_id_id)
            ticket_id_id,
            status_id_id
          FROM ticket_statuslog tsl
          WHERE 1=1 {time_filter_clause.replace("created_on", "tsl.created_on")}
          ORDER BY ticket_id_id, created_on DESC
        ),

        -- CTE: Handling Time Calculation
        agent_handling_intervals_base AS (
            SELECT
                tsl.ticket_id_id,
                tsl.created_by_id,
                tsl.status_id_id,
                tsl.created_on,
                LEAD(tsl.status_id_id) OVER (PARTITION BY tsl.ticket_id_id ORDER BY tsl.created_on) AS next_status,
                LEAD(tsl.created_by_id) OVER (PARTITION BY tsl.ticket_id_id ORDER BY tsl.created_on) AS next_by,
                LEAD(tsl.created_on) OVER (PARTITION BY tsl.ticket_id_id ORDER BY tsl.created_on) AS next_time
            FROM ticket_statuslog tsl
            WHERE tsl.status_id_id IN (3, 6)
              {time_filter_clause.replace("created_on", "tsl.created_on")}
        ),
        agent_handling_intervals AS (
            SELECT
                ticket_id_id,
                created_by_id,
                next_by,
                created_on AS start_time,
                next_time AS end_time,
                status_id_id AS start_status,
                next_status AS end_status,
                EXTRACT(EPOCH FROM (next_time - created_on)) / 60.0 AS interval_minutes
            FROM agent_handling_intervals_base
            WHERE next_time IS NOT NULL
        ),
        filtered_handling_intervals AS (
            SELECT *
            FROM agent_handling_intervals
            WHERE
                (start_status = 3 AND end_status = 6 AND created_by_id = next_by)
                OR
                (start_status = 3 AND end_status = 3 AND created_by_id <> next_by)
        ),
        avg_handling_time AS (
            SELECT
                uu.name AS agent_name,
                AVG(fhi.interval_minutes) AS avg_handling_time_minutes
            FROM filtered_handling_intervals fhi
            JOIN user_user uu ON fhi.created_by_id = uu.id
            GROUP BY uu.name
        ),

        -- CTE: Average CSAT Score
        avg_csat_score AS (
            SELECT
                uu.name AS agent_name,
                AVG(CAST(tt.feedback->>'csat' AS DECIMAL)) AS average_csat_score
            FROM
                ticket_ticket tt
            JOIN
                user_user uu ON tt.owner_id_id = uu.id
            WHERE
                tt.feedback IS NOT NULL
                AND tt.feedback->>'csat' IS NOT NULL
                AND tt.feedback->>'csat' ~ '^\d+$'
                AND tt.owner_id_id IS NOT NULL
                {time_filter_clause.replace("created_on", "tt.created_on")}
            GROUP BY
                uu.name
        )

        -- Final query: Combine all metrics per agent
        SELECT
          u.name AS "เจ้าหน้าที่",
          COALESCE(COUNT(DISTINCT CASE WHEN ats.status_id_id = 6 THEN ats.ticket_id_id END), 0) AS "ทิกเก็ตที่ปิดแล้ว",
          COALESCE(COUNT(DISTINCT CASE WHEN lsp.status_id_id != 6 THEN ats.ticket_id_id END), 0) AS "ทิกเก็ตที่ยังไม่ปิด",
          ROUND(art.avg_response_minutes, 2) AS "ตอบกลับเฉลี่ย (นาที)",
          ROUND(aht.avg_handling_time_minutes, 2) AS "เวลาจัดการเฉลี่ย (นาที)",
          ROUND(acs.average_csat_score, 2) AS "CSAT เฉลี่ย (เต็ม 5)"
        FROM user_user u
        LEFT JOIN agent_ticket_status ats ON u.id = ats.created_by_id
        LEFT JOIN latest_status_per_ticket lsp ON ats.ticket_id_id = lsp.ticket_id_id
        LEFT JOIN avg_response_time art ON art.user_name = u.name
        LEFT JOIN avg_handling_time aht ON aht.agent_name = u.name
        LEFT JOIN avg_csat_score acs ON acs.agent_name = u.name
        WHERE 1=1
          {agent_filter_sql_part}
        GROUP BY u.name, art.avg_response_minutes, aht.avg_handling_time_minutes, acs.average_csat_score
        ORDER BY u.name ASC;
        """
        return sql, all_params

    ##########################################################################################
    # VIEWs for the [Chat Performance] dashboard API endpoints
    ##########################################################################################


class IncomingMessageCountComparisonAPIView(RawSQLMetricComparisonAPIView):
    """
    API endpoint to compare the count of incoming messages between two periods,
    including time-series data and percentage change.
    """

    filename_title = _("Incoming Message Count")
    table_name = "ticket_message"
    date_column_name = "created_on"
    serializer_class = MessageSummarySerializer  # The MessageSummarySerializer can handle this structure
    time_series_granularity = "day"
    units = "messages"
    metric_aggregate_sql = (
        "COUNT(*)"  # This is the aggregate for the base class methods
    )

    # This is the additional filter for incoming messages
    filter_conditions = " AND is_self = false"


class TicketStatusCountAPIView(RawSQLMetricComparisonAPIView):
    """
    API endpoint to compare total ticket status log entries between two periods,
    including time-series data for plotting.
    """

    filename_title = _("Ticket Status Count")
    table_name = "ticket_statuslog"
    date_column_name = "created_on"
    serializer_class = MessageSummarySerializer
    time_series_granularity = "day"
    units = "status_logs"
    metric_aggregate_sql = (
        "COUNT(*)"  # Explicitly define for clarity, though it's the default
    )

    # No need to override get_single_period_metric_sql or get_time_series_metric_sql
    # as the default implementations in RawSQLMetricComparisonAPIView are sufficient.

    def get_detailed_data_sql(
        self, time_filter_clause: str, start_date: date, end_date: date
    ) -> tuple[str, list]:
        return "", []


class DistinctActiveTicketsCountAPIView(RawSQLMetricComparisonAPIView):
    """
    API endpoint to compare the count of distinct active tickets
    between two periods, including time-series data for plotting.
    
    A ticket is considered 'active' if its latest overall status is not
    'closed by bot' or 'Default' and it had a status change within the period.
    """

    filename_title = _("Distinct Incoming Tickets Count")
    table_name = "ticket_statuslog"
    date_column_name = "created_on"
    serializer_class = MessageSummarySerializer
    time_series_granularity = "day"
    units = "active_tickets"
    metric_aggregate_sql = "COUNT(DISTINCT ticket_id_id)"
    filter_conditions = ""
    extra_params: list = []

    def _get_active_tickets_ctes(self, date_col_for_filter: str) -> str:
        """
        Returns the common CTEs for active ticket logic.
        The date filter is now applied to the 'activity_in_period' CTE, not the
        latest_status CTE, which ensures a consistent 'latest status' is used.
        """
        # This CTE finds the LATEST status for ALL tickets, over ALL time.
        ctes_sql = f"""
        WITH latest_status AS (
          SELECT DISTINCT ON (ticket_id_id)
            ticket_id_id,
            status_id_id,
            created_by_id,
            created_on
          FROM ticket_statuslog
          ORDER BY ticket_id_id, created_on DESC
        ),
        -- This CTE finds ALL tickets that had a status change within the period.
        activity_in_period AS (
            SELECT DISTINCT ticket_id_id
            FROM ticket_statuslog
            WHERE DATE({date_col_for_filter}) >= %s AND DATE({date_col_for_filter}) <= %s
        ),
        -- This CTE combines the two above to get the latest status for tickets
        -- that were active in the period, and filters out the undesirable statuses.
        filtered_status AS (
          SELECT
            ls.ticket_id_id
          FROM latest_status ls
          JOIN activity_in_period aip ON ls.ticket_id_id = aip.ticket_id_id
          WHERE NOT (ls.status_id_id = 6 AND ls.created_by_id = 2)
            AND ls.status_id_id != 1
        )
        """
        return ctes_sql

    def get_single_period_metric_sql(
        self, time_filter_clause: str, start_date: date, end_date: date
    ) -> tuple[str, list]:
        """
        Overrides to provide SQL for total active tickets in a single period.
        """
        sql = f"""
        {self._get_active_tickets_ctes(self.date_column_name)}
        SELECT
          COUNT(DISTINCT ticket_id_id) AS metric_value
        FROM
          filtered_status;
        """
        # The params for the CTEs' filter now match the base class's filter logic.
        params = [start_date, end_date]
        return sql, params

    def get_time_series_metric_sql(
        self, time_filter_clause: str, start_date: date, end_date: date
    ) -> tuple[str, list]:
        """
        Overrides to provide SQL for time-series data of distinct active tickets.
        """
        date_trunc_unit = self.time_series_granularity

        sql = f"""
        WITH date_series AS (
          SELECT
            DATE_TRUNC('{date_trunc_unit}', generate_series AT TIME ZONE 'Asia/Bangkok') AS series_date
          FROM
            generate_series((%s::date)::timestamp AT TIME ZONE 'Asia/Bangkok', (%s::date)::timestamp AT TIME ZONE 'Asia/Bangkok', INTERVAL '1 {date_trunc_unit}') AS generate_series
        ),
        latest_status_overall AS (
            SELECT DISTINCT ON (ticket_id_id)
                ticket_id_id,
                status_id_id,
                created_by_id,
                created_on
            FROM ticket_statuslog
            ORDER BY ticket_id_id, created_on DESC
        ),
        activity_in_period AS (
            SELECT
                DATE_TRUNC('{date_trunc_unit}', created_on AT TIME ZONE 'Asia/Bangkok') AS period_date,
                ticket_id_id
            FROM ticket_statuslog
            WHERE DATE({self.date_column_name}) >= %s AND DATE({self.date_column_name}) <= %s
        ),
        active_tickets_by_period AS (
            SELECT
                aip.period_date,
                COUNT(DISTINCT aip.ticket_id_id) AS value
            FROM activity_in_period aip
            JOIN latest_status_overall lso ON aip.ticket_id_id = lso.ticket_id_id
            WHERE NOT (lso.status_id_id = 6 AND lso.created_by_id = 2)
              AND lso.status_id_id != 1
            GROUP BY aip.period_date
        )
        SELECT
            ds.series_date::date AS date,
            atbp.value
        FROM date_series ds
        LEFT JOIN active_tickets_by_period atbp ON ds.series_date = atbp.period_date
        ORDER BY
            ds.series_date;
        """

        time_series_params = [
            start_date,
            end_date,
            start_date,
            end_date,
        ]

        return sql, time_series_params


class ClosedTicketCountAPIView(RawSQLMetricComparisonAPIView):
    """
    API endpoint to compare the absolute count of closed tickets between two periods,
    based on the latest status of tickets.
    """

    filename_title = _("Closed Ticket Count")
    table_name = "ticket_statuslog"
    date_column_name = "created_on"
    serializer_class = MessageSummarySerializer
    time_series_granularity = "day"

    units = "closed_tickets"
    metric_aggregate_sql = "COUNT"

    def _get_closed_ticket_ctes(self, date_col_for_filter: str) -> str:
        """
        Helper to get the common CTEs for closed ticket count calculation.
        This version is simplified and only gets the latest status, without date filtering.
        """
        ctes_sql = f"""
        WITH latest_status AS (
          SELECT DISTINCT ON (ticket_id_id)
            ticket_id_id,
            status_id_id,
            created_by_id,
            created_on
          FROM ticket_statuslog
          ORDER BY ticket_id_id, created_on DESC
        )
        """
        return ctes_sql

    def get_single_period_metric_sql(
        self, time_filter_clause: str, start_date: date, end_date: date
    ) -> tuple[str, list]:
        """
        Overrides to provide SQL for the absolute count of closed tickets
        for a single period, considering latest status.
        """
        sql = f"""
        {self._get_closed_ticket_ctes(self.date_column_name)}
        , activity_in_period AS (
            SELECT DISTINCT ticket_id_id
            FROM ticket_statuslog
            WHERE DATE({self.date_column_name}) >= %s AND DATE({self.date_column_name}) <= %s
        )
        SELECT
          COUNT(ls.ticket_id_id) AS metric_value
        FROM latest_status ls
        JOIN activity_in_period aip ON ls.ticket_id_id = aip.ticket_id_id
        WHERE
          ls.status_id_id = 6;
        """
        # The params for the CTE's date filter now match the base class's filter logic.
        params = [start_date, end_date]
        return sql, params

    def get_time_series_metric_sql(
        self, time_filter_clause: str, start_date: date, end_date: date
    ) -> tuple[str, list]:
        """
        Overrides to provide SQL for time-series data of closed tickets.
        This version correctly filters for closed tickets that had a status change
        on a given day.
        """
        date_trunc_unit = self.time_series_granularity

        sql = f"""
        WITH date_series AS (
          SELECT
            DATE_TRUNC('{date_trunc_unit}', generate_series AT TIME ZONE 'Asia/Bangkok') AS series_date
          FROM
            generate_series((%s::date)::timestamp AT TIME ZONE 'Asia/Bangkok', (%s::date)::timestamp AT TIME ZONE 'Asia/Bangkok', INTERVAL '1 {date_trunc_unit}') AS generate_series
        ),
        latest_status_overall AS (
            -- This CTE is now unfiltered to get the true latest status
            SELECT DISTINCT ON (ticket_id_id)
                ticket_id_id,
                status_id_id,
                created_by_id,
                created_on
            FROM ticket_statuslog
            ORDER BY ticket_id_id, created_on DESC
        ),
        activity_in_period AS (
            -- This CTE finds tickets that had a status change on a given day
            SELECT
                DATE_TRUNC('{date_trunc_unit}', created_on AT TIME ZONE 'Asia/Bangkok') AS period_date,
                ticket_id_id
            FROM ticket_statuslog
            WHERE DATE({self.date_column_name}) >= %s AND DATE({self.date_column_name}) <= %s
        ),
        closed_tickets_by_period AS (
            -- This combines the two to get the count of closed tickets per day
            SELECT
                aip.period_date,
                COUNT(DISTINCT aip.ticket_id_id) AS value
            FROM activity_in_period aip
            JOIN latest_status_overall lso ON aip.ticket_id_id = lso.ticket_id_id
            WHERE lso.status_id_id = 6
            GROUP BY aip.period_date
        )
        SELECT
            ds.series_date::date AS date,
            ctbp.value
        FROM date_series ds
        LEFT JOIN closed_tickets_by_period ctbp ON ds.series_date = ctbp.period_date
        ORDER BY
            ds.series_date;
        """

        time_series_params = [
            start_date,
            end_date,
            start_date,
            end_date,
        ]

        return sql, time_series_params

    def get_detailed_data_sql(
        self, time_filter_clause: str, start_date: date, end_date: date
    ) -> tuple[str, list]:
        """
        Returns SQL to get detailed data (ticket_ids) for closed tickets in the main period.
        """
        sql = f"""
        {self._get_closed_ticket_ctes(self.date_column_name)}
        , activity_in_period AS (
            SELECT DISTINCT ticket_id_id
            FROM ticket_statuslog
            WHERE DATE({self.date_column_name}) >= %s AND DATE({self.date_column_name}) <= %s
        )
        SELECT
          ls.ticket_id_id
        FROM latest_status ls
        JOIN activity_in_period aip ON ls.ticket_id_id = aip.ticket_id_id
        WHERE ls.status_id_id = 6;
        """
        # The parameters are just the start and end date for the main CTE filter
        params = [start_date, end_date]
        return sql, params

class ClosedTicketRateAPIView(RawSQLMetricComparisonAPIView):
    """
    API endpoint to calculate and compare the PERCENTAGE RATE of closed tickets
    between two periods, including time-series data for plotting.
    """

    filename_title = _("Closed Ticket Rate")
    table_name = "ticket_statuslog"  # Primary table for closed status events
    date_column_name = "created_on"  # Date column for filtering
    serializer_class = MessageSummarySerializer
    time_series_granularity = "day"
    units = "%"  # Units for the rate is percentage
    metric_aggregate_sql = "AVG"

    def get_single_period_metric_sql(
        self, time_filter_clause: str, start_date: date, end_date: date
    ) -> tuple[str, list]:
        """
        Overrides the base method to use the user's provided CTE-based query
        for a single aggregate value.

        The `time_filter_clause` and `extra_params` are used to filter the
        data for the desired time period.
        """
        sql = f"""
        WITH inbox_message_count AS (
          SELECT
            COUNT(*) AS inbox_messages
          FROM
            ticket_message
          WHERE
            1=1 {time_filter_clause} -- Use the time filter clause here
            AND is_self = false
        ),
        latest_status AS (
          SELECT DISTINCT ON (ticket_id_id)
            ticket_id_id,
            status_id_id,
            created_by_id
          FROM ticket_statuslog
          WHERE 1=1 {time_filter_clause} -- And here
          ORDER BY ticket_id_id, created_on DESC
        ),
        closed_by_agent AS (
          SELECT
            COUNT(*) AS closed_tickets_by_agent
          FROM latest_status
          WHERE
            status_id_id = 6      -- Closed
            AND created_by_id != 2  -- Not by bot
        )
        SELECT
          CASE
            WHEN imc.inbox_messages = 0 THEN NULL
            ELSE ROUND((cba.closed_tickets_by_agent::decimal / imc.inbox_messages) * 100, 2)
          END AS close_per_message_percent
        FROM
          inbox_message_count imc,
          closed_by_agent cba;
        """
        # The time_filter_clause provides two '%s' placeholders, so we need to
        # duplicate the date parameters for each time it's used.
        # This is a bit fragile; a more robust solution would be to generate
        # the clause and params with a different helper, but this follows the
        # parent class's pattern.
        # Params: [start_date, end_date, start_date, end_date]
        params = [start_date, end_date, start_date, end_date]
        return sql, params

    def get_time_series_metric_sql(
        self, time_filter_clause: str, start_date: date, end_date: date
    ) -> tuple[str, list]:
        """
        Overrides the base method to provide a time series for the metric.

        This is a complex calculation that requires grouping the CTEs by date.
        We will return a modified version of the main query to provide
        the time series data.
        """
        sql = f"""
        WITH date_series AS (
            SELECT
                generate_series((%s::date)::timestamp AT TIME ZONE 'Asia/Bangkok', (%s::date)::timestamp AT TIME ZONE 'Asia/Bangkok', '1 day'::interval) as date_bucket
        ),
        inbox_message_count AS (
            SELECT
                DATE_TRUNC('{self.time_series_granularity}', created_on AT TIME ZONE 'Asia/Bangkok') AS date_bucket,
                COUNT(*) AS inbox_messages
            FROM
                ticket_message
            WHERE
                1=1 {time_filter_clause} AND is_self = false
            GROUP BY
                date_bucket
        ),
        latest_status AS (
            SELECT
                DISTINCT ON (ticket_id_id)
                ticket_id_id,
                status_id_id,
                created_by_id,
                DATE_TRUNC('{self.time_series_granularity}', created_on AT TIME ZONE 'Asia/Bangkok') AS date_bucket
            FROM
                ticket_statuslog
            WHERE
                1=1 {time_filter_clause}
            ORDER BY
                ticket_id_id, created_on DESC
        ),
        closed_by_agent AS (
            SELECT
                date_bucket,
                COUNT(*) AS closed_tickets_by_agent
            FROM latest_status
            WHERE
                status_id_id = 6 AND created_by_id != 2
            GROUP BY
                date_bucket
        )
        SELECT
            ds.date_bucket AS date,
            CASE
                WHEN imc.inbox_messages IS NULL OR imc.inbox_messages = 0 THEN NULL
                ELSE ROUND((COALESCE(cba.closed_tickets_by_agent, 0)::decimal / imc.inbox_messages) * 100, 2)
            END AS value
        FROM
            date_series ds
        LEFT JOIN inbox_message_count imc ON ds.date_bucket = imc.date_bucket
        LEFT JOIN closed_by_agent cba ON ds.date_bucket = cba.date_bucket
        ORDER BY
            date;
        """
        # The generate_series requires two parameters. The two time_filter_clauses require four more.
        params = [start_date, end_date, start_date, end_date, start_date, end_date]
        return sql, params

    def get_detailed_data_sql(
        self, time_filter_clause: str, start_date: date, end_date: date
    ) -> tuple[str, list]:
        """
        Returns an empty query since no detailed data is required by the original
        SQL query.
        """
        return "", []


class OverdueUnclosedTicketsAPIView(RawSQLQueryAPIView):
    """
    API endpoint to retrieve a list of overdue tickets that are currently UNCLOSED,
    fetching latest status and owner from logs. Date/time data is in augmentable format.
    """

    filename_title = _("Overdue Unclosed Tickets")
    serializer_class = OverdueTicketSerializer
    date_column_name = "created_on"
    metric_aggregate_sql = "AVG"

    def get_sql_query(self, time_filter_clause: str, params: list) -> tuple[str, list]:
        """
        Returns the SQL query for overdue tickets that are currently unclosed,
        including latest status and owner from log tables, numeric overdue time,
        and augmentable date/time fields.
        """
        overdue_filter = " AND tt.created_on < NOW() - INTERVAL '1 day'"

        # Apply the time filter clause to the main ticket table
        time_filter_clause_qualified_tt = time_filter_clause.replace(
            self.date_column_name, "tt." + self.date_column_name
        )
        # Apply the time filter clause to the status log table
        time_filter_clause_qualified_tsl = time_filter_clause.replace(
            self.date_column_name, "tsl." + self.date_column_name
        )
        # Apply the time filter clause to the owner log table
        time_filter_clause_qualified_tol = time_filter_clause.replace(
            self.date_column_name, "tol." + self.date_column_name
        )

        sql = f"""
        WITH latest_status_log AS (
            SELECT DISTINCT ON (ticket_id_id)
                ticket_id_id,
                status_id_id,
                created_on AT TIME ZONE 'Asia/Bangkok' AS created_on_ts -- Convert to local timezone
            FROM
                ticket_statuslog tsl
            WHERE
                1=1 {time_filter_clause_qualified_tsl}
            ORDER BY
                ticket_id_id, created_on DESC
        ),
        latest_owner_log AS (
            SELECT DISTINCT ON (ticket_id_id)
                ticket_id_id,
                new_owner_id_id AS owner_id,
                created_on AT TIME ZONE 'Asia/Bangkok' AS created_on_ts -- Convert to local timezone
            FROM
                ticket_ownerlog tol
            WHERE
                1=1 {time_filter_clause_qualified_tol}
            ORDER BY
                ticket_id_id, created_on DESC
        )
        SELECT
            tt.id AS "เลขทิกเก็ต",
            (NOW() - tt.created_on) AS "เวลาที่ใช้", -- Interval for DurationField
            ts.name AS "สถานะทิกเก็ต",
            cu.name AS "ลูกค้า",
            tp.name AS "ความสำคัญ",
            (SELECT ta_sub.sentiment
             FROM ticket_ticketanalysis ta_sub
             WHERE ta_sub.ticket_id = tt.id
             ORDER BY ta_sub.id DESC
             LIMIT 1) AS "ความรู้สึก",
            uu.name AS "เจ้าหน้าที่",
            -- --- CRITICAL CHANGES HERE: Remove ::date and add AT TIME ZONE ---
            tt.created_on AT TIME ZONE 'Asia/Bangkok' AS "เวลาที่สร้าง",
            NOW() AT TIME ZONE 'Asia/Bangkok' AS "เวลาปัจจุบัน"
            -- --- END CRITICAL CHANGES ---
        FROM ticket_ticket tt
        LEFT JOIN latest_status_log lsl ON tt.id = lsl.ticket_id_id
        LEFT JOIN ticket_status ts ON lsl.status_id_id = ts.id
        LEFT JOIN latest_owner_log lol ON tt.id = lol.ticket_id_id
        LEFT JOIN user_user uu ON lol.owner_id = uu.id
        LEFT JOIN ticket_ticketpriority tp ON tt.priority_id = tp.id
        LEFT JOIN customer_customer cu ON tt.customer_id_id = cu.customer_id
        WHERE
            lsl.status_id_id < 6 -- Assuming 'unclosed' means status ID is less than 6 (Closed)
            {overdue_filter}
            {time_filter_clause_qualified_tt}
        ORDER BY
            tt.created_on ASC;
        """
        return sql, params * 3


class OverdueClosedTicketsAPIView(RawSQLQueryAPIView):
    """
    API endpoint to retrieve a list of overdue tickets that are currently CLOSED,
    fetching latest status and owner from logs.
    """

    filename_title = _("Overdue Closed Tickets")
    serializer_class = OverdueTicketSerializer
    date_column_name = "fc.cycle_end"

    def get_sql_query(
        self, time_filter_clause: str, time_filter_params: list
    ) -> tuple[str, list]:
        """
        Constructs the SQL query for overdue closed tickets.
        """

        sql_query = f"""
        WITH StatusEvents AS (
            SELECT
                tsl.ticket_id_id,
                tsl.created_on AS event_time,
                tsl.status_id_id,
                LAG(tsl.status_id_id) OVER (PARTITION BY tsl.ticket_id_id ORDER BY tsl.created_on) AS prev_status_id,
                LEAD(tsl.created_on) OVER (PARTITION BY tsl.ticket_id_id ORDER BY tsl.created_on) AS next_event_time
            FROM
                ticket_statuslog tsl
        ),
        CycleMarkers AS (
            SELECT
                se.ticket_id_id,
                se.event_time,
                se.status_id_id,
                CASE
                    WHEN se.status_id_id NOT IN (1, 6) AND (se.prev_status_id IS NULL OR se.prev_status_id = 6) THEN se.event_time
                    ELSE NULL
                END AS general_active_period_start_candidate,
                CASE
                    WHEN se.status_id_id = 3 THEN se.event_time
                    ELSE NULL
                END AS assigned_time_candidate,
                CASE
                    WHEN se.status_id_id = 6 THEN se.event_time
                    ELSE NULL
                END AS cycle_end_time_candidate,
                SUM(CASE WHEN se.status_id_id = 6 THEN 1 ELSE 0 END) OVER (PARTITION BY se.ticket_id_id ORDER BY se.event_time) AS cycle_group
            FROM
                StatusEvents se
        ),
        GroupedCycles AS (
            SELECT
                cm.ticket_id_id,
                MIN(cm.general_active_period_start_candidate) FILTER (WHERE cm.general_active_period_start_candidate IS NOT NULL) AS actual_cycle_start_general,
                MIN(cm.assigned_time_candidate) FILTER (WHERE cm.assigned_time_candidate IS NOT NULL) AS actual_cycle_start_assigned,
                cm.cycle_group
            FROM
                CycleMarkers cm
            WHERE
                cm.status_id_id NOT IN (1, 6)
            GROUP BY
                cm.ticket_id_id, cm.cycle_group
        ),
        FinalCycles AS (
            SELECT
                gc.ticket_id_id,
                CASE
                    WHEN gc.actual_cycle_start_assigned IS NOT NULL AND gc.actual_cycle_start_assigned >= gc.actual_cycle_start_general
                    THEN gc.actual_cycle_start_assigned
                    ELSE gc.actual_cycle_start_general
                END AS cycle_start,
                (SELECT MIN(se.event_time) FROM StatusEvents se WHERE se.ticket_id_id = gc.ticket_id_id AND se.status_id_id = 6 AND se.event_time > gc.actual_cycle_start_general) AS cycle_end
            FROM
                GroupedCycles gc
            WHERE
                gc.actual_cycle_start_general IS NOT NULL
                AND (SELECT MIN(se.event_time) FROM StatusEvents se WHERE se.ticket_id_id = gc.ticket_id_id AND se.status_id_id = 6 AND se.event_time > gc.actual_cycle_start_general) IS NOT NULL
        )
        SELECT
            tt.id AS "เลขทิกเก็ต",
            ts.name AS "สถานะทิกเก็ต",
            cu.name AS "ลูกค้า",
            tp.name AS "ความสำคัญ",
            COALESCE(ta.sentiment, 'N/A') AS "ความรู้สึก",
            uu.name AS "เจ้าหน้าที่",
            -- Convert timestamps to local timezone (Asia/Bangkok)
            fc.cycle_start AT TIME ZONE 'Asia/Bangkok' AS "เวลาที่สร้าง",
            fc.cycle_end AT TIME ZONE 'Asia/Bangkok' AS "เวลาที่ปิด",
            (fc.cycle_end - fc.cycle_start) AS "เวลาที่ใช้"
   
        FROM
            ticket_ticket tt
        JOIN
            ticket_status ts ON tt.status_id_id = ts.id
        JOIN
            ticket_ticketpriority tp ON tt.priority_id = tp.id
        JOIN
            user_user uu ON tt.owner_id_id = uu.id
        LEFT JOIN
            customer_customer cu ON tt.customer_id_id = cu.customer_id
        LEFT JOIN LATERAL (
            SELECT
                ta_inner.sentiment
            FROM
                ticket_ticketanalysis ta_inner
            WHERE
                ta_inner.ticket_id = tt.id
            ORDER BY
                ta_inner.id DESC
            LIMIT 1
        ) AS ta ON TRUE
        JOIN FinalCycles fc ON tt.id = fc.ticket_id_id
        WHERE
            tt.status_id_id = 6
            AND fc.cycle_end IS NOT NULL
            AND EXTRACT(EPOCH FROM (fc.cycle_end - fc.cycle_start)) > 86400
            {time_filter_clause}
        ORDER BY
            fc.cycle_start DESC;
        """
        return sql_query, time_filter_params


# class AverageResponseTimeAPIView(RawSQLMetricComparisonAPIView):
#     """
#     API endpoint to compare the AVERAGE response time of agents
#     between two periods, including time-series data for plotting.
#     """

#     filename_title = _("Average Response Time")
#     table_name = "ticket_message"
#     date_column_name = "created_on"
#     serializer_class = MessageTimeSeriesSerializer
#     metric_aggregate_sql = "AVG"
#     units = "seconds"

#     def _get_agent_response_ctes(self) -> str:
#         """
#         Helper to get the common CTEs for average response time calculation
#         without applying any date filtering.
#         """
#         ctes_sql = f"""
#         WITH agent_messages AS (
#           SELECT
#             tm.ticket_id_id,
#             tm.created_on AS agent_msg_time,
#             tm.user_name,
#             tm.created_by_id,
#             tm.is_self,
#             CASE
#               WHEN tm.is_self = true AND tm.created_by_id IS NULL THEN true
#               WHEN tm.is_self = true AND tm.created_by_id IS NOT NULL AND lower(tm.user_name) = 'system' THEN true
#               ELSE false
#             END AS is_chatbot
#           FROM ticket_message tm
#           WHERE tm.is_self = true
#         ),
#         agent_responses AS (
#           SELECT
#             a.ticket_id_id,
#             a.agent_msg_time,
#             (
#               SELECT MAX(c.created_on)
#               FROM ticket_message c
#               WHERE c.ticket_id_id = a.ticket_id_id
#                 AND c.is_self = false
#                 AND c.created_on < a.agent_msg_time
#             ) AS last_customer_msg_time
#           FROM agent_messages a
#           WHERE a.is_chatbot = false
#         )
#         """
#         return ctes_sql

#     def get_single_period_metric_sql(
#         self, time_filter_clause: str, start_date: date, end_date: date
#     ) -> tuple[str, list]:
#         """
#         Returns SQL to get the overall average response time for a single period.
#         The date filter is now applied to the final result set.
#         """
#         ctes_sql = self._get_agent_response_ctes()
#         sql = f"""
#         {ctes_sql}
#         SELECT
#           AVG(EXTRACT(EPOCH FROM (agent_msg_time - last_customer_msg_time))) AS metric_value
#         FROM agent_responses
#         WHERE last_customer_msg_time IS NOT NULL
#           AND DATE(agent_msg_time) >= %s AND DATE(agent_msg_time) <= %s;
#         """
#         params = [start_date, end_date]
#         return sql, params

#     def get_time_series_metric_sql(
#         self, time_filter_clause: str, start_date: date, end_date: date
#     ) -> tuple[str, list]:
#         """
#         Returns SQL for time-series data of average response time.
#         The date filter is correctly applied to the agent_msg_time of the intervals.
#         """
#         date_trunc_unit = self.time_series_granularity
#         ctes_sql = self._get_agent_response_ctes()

#         sql = f"""
#         WITH date_series AS (
#           SELECT
#             DATE_TRUNC('{date_trunc_unit}', generate_series AT TIME ZONE 'Asia/Bangkok') AS series_date
#           FROM
#             generate_series((%s::date)::timestamp AT TIME ZONE 'Asia/Bangkok', (%s::date)::timestamp AT TIME ZONE 'Asia/Bangkok', INTERVAL '1 {date_trunc_unit}') AS generate_series
#         ),
#         response_data AS (
#             {ctes_sql}
#             SELECT
#                 DATE_TRUNC('{date_trunc_unit}', ar.agent_msg_time) AS response_date,
#                 AVG(EXTRACT(EPOCH FROM (ar.agent_msg_time - ar.last_customer_msg_time))) AS value
#             FROM agent_responses ar
#             WHERE ar.last_customer_msg_time IS NOT NULL
#               AND DATE(ar.agent_msg_time) >= %s AND DATE(ar.agent_msg_time) <= %s
#             GROUP BY
#                 DATE_TRUNC('{date_trunc_unit}', ar.agent_msg_time)
#         )
#         SELECT
#             ds.series_date::date AS date,
#             rd.value
#         FROM date_series ds
#         LEFT JOIN response_data rd ON ds.series_date = rd.response_date
#         ORDER BY
#             ds.series_date;
#         """
#         # Parameters: 2 for generate_series + 2 for the date filter on agent_msg_time
#         params = [
#             start_date,
#             end_date,
#             start_date,
#             end_date,
#         ]
#         return sql, params


class AverageResponseTimeAPIView(RawSQLMetricComparisonAPIView):
    """
    API endpoint to compare the AVERAGE response time of agents
    between two periods, including time-series data for plotting.
    """

    filename_title = _("Average Response Time")
    table_name = "ticket_message"
    date_column_name = "created_on"
    serializer_class = MessageTimeSeriesSerializer
    metric_aggregate_sql = "AVG"
    # units = "minute"  # options: "second", "minute", "hour"
    channel = "line"   # 👈 choose which channel you want to fetch SLA for

    @property
    def units(self) -> str:
        """
        Dynamically fetch the unit for response_time from SLA table.
        Default to 'second' if not found.
        """
        sla = SLA.objects.filter(name="response_time", channel=self.channel).first()
        return sla.unit if sla else "second"

    def _get_unit_divisor(self) -> float:
        """
        Return divisor for converting seconds into the desired unit.
        """
        if self.units == "minute":
            return 60.0
        elif self.units == "hour":
            return 3600.0
        return 1.0  # default is seconds

    def _get_epoch_expression(self, prefix: str = "ar") -> str:
        """
        Return the SQL expression for time difference in the chosen unit.
        `prefix` is table alias (ar = agent_responses).
        """
        divisor = self._get_unit_divisor()
        expr = f"EXTRACT(EPOCH FROM ({prefix}.agent_msg_time - {prefix}.last_customer_msg_time))"
        if divisor != 1.0:
            expr = f"({expr}) / {divisor}"
        return expr

    def _get_agent_response_ctes(self) -> str:
        """
        Helper to get the common CTEs for average response time calculation
        without applying any date filtering.
        """
        ctes_sql = """
        WITH agent_messages AS (
          SELECT
            tm.ticket_id_id,
            tm.created_on AS agent_msg_time,
            tm.user_name,
            tm.created_by_id,
            tm.is_self,
            CASE
              WHEN tm.is_self = true AND tm.created_by_id IS NULL THEN true
              WHEN tm.is_self = true AND tm.created_by_id IS NOT NULL AND lower(tm.user_name) = 'system' THEN true
              ELSE false
            END AS is_chatbot
          FROM ticket_message tm
          WHERE tm.is_self = true
        ),
        agent_responses AS (
          SELECT
            a.ticket_id_id,
            a.agent_msg_time,
            (
              SELECT MAX(c.created_on)
              FROM ticket_message c
              WHERE c.ticket_id_id = a.ticket_id_id
                AND c.is_self = false
                AND c.created_on < a.agent_msg_time
            ) AS last_customer_msg_time
          FROM agent_messages a
          WHERE a.is_chatbot = false
        )
        """
        return ctes_sql

    def get_single_period_metric_sql(
        self, time_filter_clause: str, start_date: date, end_date: date
    ) -> tuple[str, list]:
        """
        Returns SQL to get the overall average response time for a single period.
        """
        ctes_sql = self._get_agent_response_ctes()
        epoch_expr = self._get_epoch_expression(prefix="agent_responses")
        sql = f"""
        {ctes_sql}
        SELECT
          AVG({epoch_expr}) AS metric_value
        FROM agent_responses
        WHERE last_customer_msg_time IS NOT NULL
          AND DATE(agent_msg_time) >= %s AND DATE(agent_msg_time) <= %s;
        """
        params = [start_date, end_date]
        return sql, params

    def get_time_series_metric_sql(
        self, time_filter_clause: str, start_date: date, end_date: date
    ) -> tuple[str, list]:
        """
        Returns SQL for time-series data of average response time.
        """
        date_trunc_unit = self.time_series_granularity
        ctes_sql = self._get_agent_response_ctes()
        epoch_expr = self._get_epoch_expression(prefix="ar")

        sql = f"""
        WITH date_series AS (
          SELECT
            DATE_TRUNC('{date_trunc_unit}', generate_series AT TIME ZONE 'Asia/Bangkok') AS series_date
          FROM
            generate_series((%s::date)::timestamp AT TIME ZONE 'Asia/Bangkok',
                            (%s::date)::timestamp AT TIME ZONE 'Asia/Bangkok',
                            INTERVAL '1 {date_trunc_unit}') AS generate_series
        ),
        response_data AS (
            {ctes_sql}
            SELECT
                DATE_TRUNC('{date_trunc_unit}', ar.agent_msg_time) AS response_date,
                AVG({epoch_expr}) AS value
            FROM agent_responses ar
            WHERE ar.last_customer_msg_time IS NOT NULL
              AND DATE(ar.agent_msg_time) >= %s AND DATE(ar.agent_msg_time) <= %s
            GROUP BY
                DATE_TRUNC('{date_trunc_unit}', ar.agent_msg_time)
        )
        SELECT
            ds.series_date::date AS date,
            rd.value
        FROM date_series ds
        LEFT JOIN response_data rd ON ds.series_date = rd.response_date
        ORDER BY
            ds.series_date;
        """
        params = [start_date, end_date, start_date, end_date]
        return sql, params

class AgentResponseWithin6SecondsRateAPIView(RawSQLMetricComparisonAPIView):
    """
    API endpoint to compare the rate of agent responses within 6 seconds
    between two periods, including time-series data for plotting.
    """

    filename_title = _("6 Second Response Rate")
    table_name = "ticket_message"
    date_column_name = "created_on"
    serializer_class = MessageSummarySerializer
    time_series_granularity = "day"
    units = "%"

    filter_conditions = ""
    metric_aggregate_sql = "AVG"

    def _get_response_rate_ctes(self) -> str:
        """
        Helper to get the common CTEs for calculating agent response rate
        within 6 seconds, without applying any date filtering.
        """
        ctes_sql = f"""
        WITH agent_messages AS (
          SELECT
            ticket_id_id,
            created_on AS agent_msg_time,
            user_name,
            created_by_id,
            is_self,
            CASE
              WHEN is_self = true AND created_by_id IS NULL THEN true
              WHEN is_self = true AND created_by_id IS NOT NULL AND user_name = 'System' THEN true
              ELSE false
            END AS is_chatbot
          FROM ticket_message
          WHERE is_self = true
        ),
        agent_responses AS (
          SELECT
            a.ticket_id_id,
            a.agent_msg_time,
            (
              SELECT MAX(c.created_on)
              FROM ticket_message c
              WHERE c.ticket_id_id = a.ticket_id_id
                AND c.is_self = false
                AND c.created_on < a.agent_msg_time
            ) AS last_customer_msg_time
          FROM agent_messages a
          WHERE a.is_chatbot = false
        ),
        response_durations AS (
          SELECT
            ticket_id_id,
            agent_msg_time,
            last_customer_msg_time,
            CAST(EXTRACT(EPOCH FROM (agent_msg_time - last_customer_msg_time)) AS DOUBLE PRECISION) AS response_time_seconds
          FROM agent_responses
          WHERE last_customer_msg_time IS NOT NULL
        )
        """
        return ctes_sql

    def get_single_period_metric_sql(
        self, time_filter_clause: str, start_date: date, end_date: date
    ) -> tuple[str, list]:
        """
        Returns SQL to get the overall rate for a single period.
        The date filter is now applied to the final result set of intervals.
        """
        sql = f"""
        {self._get_response_rate_ctes()}
        SELECT
          ROUND(
            CASE
              WHEN COUNT(*) = 0 THEN 0
              ELSE 100.0 * COUNT(*) FILTER (WHERE response_time_seconds <= 6) / COUNT(*)
            END,
            2
          ) AS metric_value
        FROM response_durations
        WHERE DATE(agent_msg_time) >= %s AND DATE(agent_msg_time) <= %s;
        """
        params = [start_date, end_date]
        return sql, params

    def get_time_series_metric_sql(
        self, time_filter_clause: str, start_date: date, end_date: date
    ) -> tuple[str, list]:
        """
        Returns SQL for time-series data of the response rate.
        The date filter is correctly applied to the agent_msg_time of the intervals.
        """
        date_trunc_unit = self.time_series_granularity

        sql = f"""
        WITH date_series AS (
          SELECT
            DATE_TRUNC('{date_trunc_unit}', generate_series AT TIME ZONE 'Asia/Bangkok') AS series_date
          FROM
            generate_series((%s::date)::timestamp AT TIME ZONE 'Asia/Bangkok', (%s::date)::timestamp AT TIME ZONE 'Asia/Bangkok', INTERVAL '1 {date_trunc_unit}') AS generate_series
        ),
        response_data_by_period AS (
            {self._get_response_rate_ctes()}
            SELECT
                DATE_TRUNC('{date_trunc_unit}', agent_msg_time AT TIME ZONE 'Asia/Bangkok') AS period_date,
                COUNT(*) AS total_responses,
                COUNT(*) FILTER (WHERE response_time_seconds <= 6) AS fast_responses
            FROM response_durations
            WHERE DATE(agent_msg_time) >= %s AND DATE(agent_msg_time) <= %s
            GROUP BY
                DATE_TRUNC('{date_trunc_unit}', agent_msg_time AT TIME ZONE 'Asia/Bangkok')
        )
        SELECT
          ds.series_date::date AS date,
          ROUND(
            CASE
              WHEN rdbp.total_responses IS NULL OR rdbp.total_responses = 0 THEN 0
              ELSE 100.0 * rdbp.fast_responses / rdbp.total_responses
            END,
            2
          ) AS value
        FROM
          date_series ds
        LEFT JOIN
          response_data_by_period rdbp ON ds.series_date = rdbp.period_date
        ORDER BY
          ds.series_date;
        """
        params = [
            start_date,
            end_date,
            start_date,
            end_date,
        ]
        return sql, params

    def get_detailed_data_sql(
        self, time_filter_clause: str, start_date: date, end_date: date
    ) -> tuple[str, list]:
        """
        Returns SQL to get detailed data for response intervals in the main period.
        The date filter is applied to the final result set.
        """
        sql = f"""
        {self._get_response_rate_ctes()}
        SELECT
          ticket_id_id,
          agent_msg_time AT TIME ZONE 'Asia/Bangkok' AS agent_msg_time,
          last_customer_msg_time AT TIME ZONE 'Asia/Bangkok' AS last_customer_msg_time,
          response_time_seconds,
          (response_time_seconds <= 6) AS responded_within_6_seconds
        FROM response_durations
        WHERE DATE(agent_msg_time) >= %s AND DATE(agent_msg_time) <= %s
        ORDER BY agent_msg_time DESC;
        """
        params = [start_date, end_date]
        return sql, params



class AgentResponseWithinThresholdRateAPIView(RawSQLMetricComparisonAPIView):
    """
    API endpoint to compare the rate of agent responses within SLA threshold
    between two periods, including time-series data for plotting.
    """

    filename_title = _("Response Within SLA Threshold Rate")
    table_name = "ticket_message"
    date_column_name = "created_on"
    serializer_class = MessageSummarySerializer
    time_series_granularity = "day"
    units = "%"  # result is always percentage
    channel = "line"  # channel to use for SLA threshold

    filter_conditions = ""
    metric_aggregate_sql = "AVG"

    @property
    def threshold_seconds(self) -> float:
        """
        Fetch SLA threshold for response_time for the given channel and convert to seconds.
        """
        sla = SLA.objects.filter(name="response_time", channel=self.channel).first()
        if not sla:
            return 6.0  # default 6 seconds if SLA not found

        value = float(sla.value)
        if sla.unit == "minute":
            return value * 60
        elif sla.unit == "hour":
            return value * 3600
        # default: assume seconds
        return value

    def _get_response_rate_ctes(self) -> str:
        """
        Helper to get the common CTEs for calculating agent response durations.
        """
        return f"""
        WITH agent_messages AS (
          SELECT
            ticket_id_id,
            created_on AS agent_msg_time,
            user_name,
            created_by_id,
            is_self,
            CASE
              WHEN is_self = true AND created_by_id IS NULL THEN true
              WHEN is_self = true AND created_by_id IS NOT NULL AND lower(user_name) = 'system' THEN true
              ELSE false
            END AS is_chatbot
          FROM ticket_message
          WHERE is_self = true
        ),
        agent_responses AS (
          SELECT
            a.ticket_id_id,
            a.agent_msg_time,
            (
              SELECT MAX(c.created_on)
              FROM ticket_message c
              WHERE c.ticket_id_id = a.ticket_id_id
                AND c.is_self = false
                AND c.created_on < a.agent_msg_time
            ) AS last_customer_msg_time
          FROM agent_messages a
          WHERE a.is_chatbot = false
        ),
        response_durations AS (
          SELECT
            ticket_id_id,
            agent_msg_time,
            last_customer_msg_time,
            CAST(EXTRACT(EPOCH FROM (agent_msg_time - last_customer_msg_time)) AS DOUBLE PRECISION) AS response_time_seconds
          FROM agent_responses
          WHERE last_customer_msg_time IS NOT NULL
        )
        """

    def get_single_period_metric_sql(
        self, time_filter_clause: str, start_date: date, end_date: date
    ) -> tuple[str, list]:
        threshold = self.threshold_seconds
        sql = f"""
        {self._get_response_rate_ctes()}
        SELECT
          ROUND(
            CASE
              WHEN COUNT(*) = 0 THEN 0
              ELSE 100.0 * COUNT(*) FILTER (WHERE response_time_seconds <= {threshold}) / COUNT(*)
            END,
            2
          ) AS metric_value
        FROM response_durations
        WHERE DATE(agent_msg_time) >= %s AND DATE(agent_msg_time) <= %s;
        """
        params = [start_date, end_date]
        return sql, params

    def get_time_series_metric_sql(
        self, time_filter_clause: str, start_date: date, end_date: date
    ) -> tuple[str, list]:
        threshold = self.threshold_seconds
        date_trunc_unit = self.time_series_granularity

        sql = f"""
        WITH date_series AS (
          SELECT
            DATE_TRUNC('{date_trunc_unit}', generate_series AT TIME ZONE 'Asia/Bangkok') AS series_date
          FROM
            generate_series((%s::date)::timestamp AT TIME ZONE 'Asia/Bangkok',
                            (%s::date)::timestamp AT TIME ZONE 'Asia/Bangkok',
                            INTERVAL '1 {date_trunc_unit}') AS generate_series
        ),
        response_data_by_period AS (
            {self._get_response_rate_ctes()}
            SELECT
                DATE_TRUNC('{date_trunc_unit}', agent_msg_time AT TIME ZONE 'Asia/Bangkok') AS period_date,
                COUNT(*) AS total_responses,
                COUNT(*) FILTER (WHERE response_time_seconds <= {threshold}) AS fast_responses
            FROM response_durations
            WHERE DATE(agent_msg_time) >= %s AND DATE(agent_msg_time) <= %s
            GROUP BY DATE_TRUNC('{date_trunc_unit}', agent_msg_time AT TIME ZONE 'Asia/Bangkok')
        )
        SELECT
          ds.series_date::date AS date,
          ROUND(
            CASE
              WHEN rdbp.total_responses IS NULL OR rdbp.total_responses = 0 THEN 0
              ELSE 100.0 * rdbp.fast_responses / rdbp.total_responses
            END,
            2
          ) AS value
        FROM date_series ds
        LEFT JOIN response_data_by_period rdbp ON ds.series_date = rdbp.period_date
        ORDER BY ds.series_date;
        """
        params = [start_date, end_date, start_date, end_date]
        return sql, params

    def get_detailed_data_sql(
        self, time_filter_clause: str, start_date: date, end_date: date
    ) -> tuple[str, list]:
        threshold = self.threshold_seconds
        sql = f"""
        {self._get_response_rate_ctes()}
        SELECT
          ticket_id_id,
          agent_msg_time AT TIME ZONE 'Asia/Bangkok' AS agent_msg_time,
          last_customer_msg_time AT TIME ZONE 'Asia/Bangkok' AS last_customer_msg_time,
          response_time_seconds,
          (response_time_seconds <= {threshold}) AS responded_within_threshold
        FROM response_durations
        WHERE DATE(agent_msg_time) >= %s AND DATE(agent_msg_time) <= %s
        ORDER BY agent_msg_time DESC;
        """
        params = [start_date, end_date]
        return sql, params

# class AverageHandlingTimeAPIView(RawSQLMetricComparisonAPIView):
#     """
#     API endpoint to compare the AVERAGE handling time in minutes
#     between two periods, including time-series data for plotting.
#     This includes time spent by the same agent and time during agent handoffs.
#     """

#     filename_title = _("Average Handling Time")
#     table_name = "ticket_statuslog"
#     date_column_name = "created_on"
#     serializer_class = MessageSummarySerializer
#     metric_aggregate_sql = "AVG"
#     units = "minutes"
#     filter_conditions = ""

#     def _get_base_handling_time_ctes(self) -> str:
#         """
#         Returns the CTEs to calculate all handling intervals for all time.
#         No date filtering is applied here.
#         """
#         return f"""
#         WITH ordered_status AS (
#             SELECT
#                 ticket_id_id,
#                 created_by_id,
#                 status_id_id,
#                 created_on,
#                 LEAD(status_id_id) OVER (PARTITION BY ticket_id_id ORDER BY created_on) AS next_status,
#                 LEAD(created_by_id) OVER (PARTITION BY ticket_id_id ORDER BY created_on) AS next_by,
#                 LEAD(created_on) OVER (PARTITION BY ticket_id_id ORDER BY created_on) AS next_time
#             FROM ticket_statuslog
#             WHERE status_id_id IN (3, 6)
#         ),
#         handling_intervals AS (
#             SELECT
#                 ticket_id_id,
#                 created_by_id,
#                 next_by,
#                 start_time,
#                 end_time,
#                 start_status,
#                 end_status,
#                 EXTRACT(EPOCH FROM (end_time - start_time)) / 60.0 AS interval_minutes
#             FROM (
#               SELECT
#                   ticket_id_id,
#                   created_by_id,
#                   next_by,
#                   created_on AS start_time,
#                   next_time AS end_time,
#                   status_id_id AS start_status,
#                   next_status AS end_status
#               FROM ordered_status
#               WHERE next_time IS NOT NULL
#             ) sub
#         ),
#         filtered_intervals AS (
#             SELECT *
#             FROM handling_intervals
#             WHERE
#                 (start_status = 3 AND end_status = 6 AND created_by_id = next_by)
#                 OR
#                 (start_status = 3 AND end_status = 3 AND created_by_id <> next_by)
#         )
#         """

#     def get_single_period_metric_sql(
#         self, time_filter_clause: str, start_date: date, end_date: date
#     ) -> tuple[str, list]:
#         """
#         Overrides to provide SQL for average handling time in a single period.
#         The date filter is now applied to the final `filtered_intervals` CTE.
#         """
#         base_ctes = self._get_base_handling_time_ctes()
#         sql = f"""
#         {base_ctes}
#         SELECT
#             AVG(interval_minutes) AS metric_value
#         FROM filtered_intervals
#         WHERE DATE(start_time) >= %s AND DATE(start_time) <= %s;
#         """
#         # We pass only 2 parameters to match the 2 placeholders in the WHERE clause.
#         params = [start_date, end_date]
#         return sql, params

#     def get_time_series_metric_sql(
#         self, time_filter_clause: str, start_date: date, end_date: date
#     ) -> tuple[str, list]:
#         """
#         Overrides to provide SQL for time-series data of average handling time.
#         The date filter is correctly applied to the start_time of the intervals.
#         """
#         date_trunc_unit = self.time_series_granularity
#         base_ctes = self._get_base_handling_time_ctes()

#         sql = f"""
#         WITH date_series AS (
#           SELECT
#             DATE_TRUNC('{date_trunc_unit}', generate_series AT TIME ZONE 'Asia/Bangkok') AS series_date
#           FROM
#             generate_series((%s::date)::timestamp AT TIME ZONE 'Asia/Bangkok', (%s::date)::timestamp AT TIME ZONE 'Asia/Bangkok', INTERVAL '1 {date_trunc_unit}') AS generate_series
#         ),
#         time_data AS (
#             {base_ctes}
#             SELECT
#                 DATE_TRUNC('{date_trunc_unit}', start_time AT TIME ZONE 'Asia/Bangkok') AS handling_date,
#                 AVG(interval_minutes) AS avg_interval_minutes
#             FROM filtered_intervals
#             WHERE DATE(start_time) >= %s AND DATE(start_time) <= %s
#             GROUP BY DATE_TRUNC('{date_trunc_unit}', start_time AT TIME ZONE 'Asia/Bangkok')
#         )
#         SELECT
#           ds.series_date::date AS date,
#           td.avg_interval_minutes AS value
#         FROM
#           date_series ds
#         LEFT JOIN
#           time_data td ON ds.series_date = td.handling_date
#         ORDER BY
#           ds.series_date;
#         """
#         # Parameters: 2 for generate_series + 2 for the date filter on start_time
#         params = [start_date, end_date, start_date, end_date]
#         return sql, params

#     def get_detailed_data_sql(
#         self, time_filter_clause: str, start_date: date, end_date: date
#     ) -> tuple[str, list]:
#         """
#         Returns SQL to get detailed data for handling time intervals in the main period.
#         """
#         base_ctes = self._get_base_handling_time_ctes()
#         sql = f"""
#         {base_ctes}
#         SELECT
#           ticket_id_id,
#           start_time AT TIME ZONE 'Asia/Bangkok' AS start_time,
#           end_time AT TIME ZONE 'Asia/Bangkok' AS end_time,
#           interval_minutes
#         FROM filtered_intervals
#         WHERE DATE(start_time) >= %s AND DATE(start_time) <= %s
#         ORDER BY start_time DESC;
#         """
#         # The parameters are just the start and end date for the main CTE filter
#         params = [start_date, end_date]
#         return sql, params


class AverageHandlingTimeAPIView(RawSQLMetricComparisonAPIView):
    """
    API endpoint to compare the AVERAGE handling time
    between two periods, including time-series data for plotting.
    This includes time spent by the same agent and time during agent handoffs.
    """

    filename_title = _("Average Handling Time")
    table_name = "ticket_statuslog"
    date_column_name = "created_on"
    serializer_class = MessageSummarySerializer
    metric_aggregate_sql = "AVG"
    # units = "minute"  # options: "second", "minute", "hour"
    filter_conditions = ""
    channel = "line"   # 👈 choose which channel you want to fetch SLA for

    @property
    def units(self) -> str:
        """
        Dynamically fetch the unit for handling_time from SLA table.
        Default to 'minute' if not found.
        """
        sla = SLA.objects.filter(name="handling_time", channel=self.channel).first()
        return sla.unit if sla else "hour"

    def _get_unit_divisor(self) -> float:
        """
        Return divisor for converting seconds into the desired unit.
        """
        if self.units == "minute":
            return 60.0
        elif self.units == "hour":
            return 3600.0
        return 1.0  # default seconds

    def _get_interval_expression(self) -> str:
        """
        Build the SQL expression for interval calculation
        based on configured units.
        """
        divisor = self._get_unit_divisor()
        expr = "EXTRACT(EPOCH FROM (end_time - start_time))"
        if divisor != 1.0:
            expr = f"({expr}) / {divisor}"
        return expr

    def _get_base_handling_time_ctes(self) -> str:
        """
        Returns the CTEs to calculate all handling intervals for all time.
        """
        interval_expr = self._get_interval_expression()
        return f"""
        WITH ordered_status AS (
            SELECT
                ticket_id_id,
                created_by_id,
                status_id_id,
                created_on,
                LEAD(status_id_id) OVER (PARTITION BY ticket_id_id ORDER BY created_on) AS next_status,
                LEAD(created_by_id) OVER (PARTITION BY ticket_id_id ORDER BY created_on) AS next_by,
                LEAD(created_on) OVER (PARTITION BY ticket_id_id ORDER BY created_on) AS next_time
            FROM ticket_statuslog
            WHERE status_id_id IN (3, 6)
        ),
        handling_intervals AS (
            SELECT
                ticket_id_id,
                created_by_id,
                next_by,
                start_time,
                end_time,
                start_status,
                end_status,
                {interval_expr} AS interval_value
            FROM (
              SELECT
                  ticket_id_id,
                  created_by_id,
                  next_by,
                  created_on AS start_time,
                  next_time AS end_time,
                  status_id_id AS start_status,
                  next_status AS end_status
              FROM ordered_status
              WHERE next_time IS NOT NULL
            ) sub
        ),
        filtered_intervals AS (
            SELECT *
            FROM handling_intervals
            WHERE
                (start_status = 3 AND end_status = 6 AND created_by_id = next_by)
                OR
                (start_status = 3 AND end_status = 3 AND created_by_id <> next_by)
        )
        """

    def get_single_period_metric_sql(
        self, time_filter_clause: str, start_date: date, end_date: date
    ) -> tuple[str, list]:
        """
        SQL for average handling time in a single period.
        """
        base_ctes = self._get_base_handling_time_ctes()
        sql = f"""
        {base_ctes}
        SELECT
            AVG(interval_value) AS metric_value
        FROM filtered_intervals
        WHERE DATE(start_time) >= %s AND DATE(start_time) <= %s;
        """
        params = [start_date, end_date]
        return sql, params

    def get_time_series_metric_sql(
        self, time_filter_clause: str, start_date: date, end_date: date
    ) -> tuple[str, list]:
        """
        SQL for time-series average handling time.
        """
        date_trunc_unit = self.time_series_granularity
        base_ctes = self._get_base_handling_time_ctes()

        sql = f"""
        WITH date_series AS (
          SELECT
            DATE_TRUNC('{date_trunc_unit}', generate_series AT TIME ZONE 'Asia/Bangkok') AS series_date
          FROM
            generate_series((%s::date)::timestamp AT TIME ZONE 'Asia/Bangkok',
                            (%s::date)::timestamp AT TIME ZONE 'Asia/Bangkok',
                            INTERVAL '1 {date_trunc_unit}') AS generate_series
        ),
        time_data AS (
            {base_ctes}
            SELECT
                DATE_TRUNC('{date_trunc_unit}', start_time AT TIME ZONE 'Asia/Bangkok') AS handling_date,
                AVG(interval_value) AS avg_interval
            FROM filtered_intervals
            WHERE DATE(start_time) >= %s AND DATE(start_time) <= %s
            GROUP BY DATE_TRUNC('{date_trunc_unit}', start_time AT TIME ZONE 'Asia/Bangkok')
        )
        SELECT
          ds.series_date::date AS date,
          td.avg_interval AS value
        FROM
          date_series ds
        LEFT JOIN
          time_data td ON ds.series_date = td.handling_date
        ORDER BY
          ds.series_date;
        """
        params = [start_date, end_date, start_date, end_date]
        return sql, params

    def get_detailed_data_sql(
        self, time_filter_clause: str, start_date: date, end_date: date
    ) -> tuple[str, list]:
        """
        SQL to get detailed interval data for handling times.
        """
        base_ctes = self._get_base_handling_time_ctes()
        sql = f"""
        {base_ctes}
        SELECT
          ticket_id_id,
          start_time AT TIME ZONE 'Asia/Bangkok' AS start_time,
          end_time AT TIME ZONE 'Asia/Bangkok' AS end_time,
          interval_value
        FROM filtered_intervals
        WHERE DATE(start_time) >= %s AND DATE(start_time) <= %s
        ORDER BY start_time DESC;
        """
        params = [start_date, end_date]
        return sql, params


class HandlingRateWithin5MinAPIView(RawSQLMetricComparisonAPIView):
    """
    API endpoint to calculate and compare the percentage of handling intervals
    completed within 5 minutes between two periods, including time-series data.
    """

    filename_title = _("Handling Rate Within 5 Minutes")
    table_name = "ticket_statuslog"
    date_column_name = "created_on"
    serializer_class = MessageSummarySerializer
    time_series_granularity = "day"
    metric_aggregate_sql = "AVG"
    units = "%"

    filter_conditions = ""

    def _get_base_handling_ctes(self) -> str:
        """
        Helper to get the common CTEs for calculating all handling intervals.
        No date filtering is applied here.
        """
        return f"""
        WITH ordered_status AS (
            SELECT
                ticket_id_id,
                created_by_id,
                status_id_id,
                created_on,
                LEAD(status_id_id) OVER (PARTITION BY ticket_id_id ORDER BY created_on) AS next_status,
                LEAD(created_by_id) OVER (PARTITION BY ticket_id_id ORDER BY created_on) AS next_by,
                LEAD(created_on) OVER (PARTITION BY ticket_id_id ORDER BY created_on) AS next_time
            FROM ticket_statuslog
            WHERE status_id_id IN (3, 6)
        ),
        handling_intervals AS (
            SELECT
                ticket_id_id,
                created_by_id,
                next_by,
                created_on AS start_time,
                next_time AS end_time,
                status_id_id AS start_status,
                next_status AS end_status,
                EXTRACT(EPOCH FROM (next_time - created_on)) / 60.0 AS interval_minutes
            FROM ordered_status
            WHERE next_time IS NOT NULL
        ),
        filtered_intervals AS (
            SELECT *
            FROM handling_intervals
            WHERE
                (start_status = 3 AND end_status = 6 AND created_by_id = next_by)
                OR
                (start_status = 3 AND end_status = 3 AND created_by_id <> next_by)
        )
        """

    def get_single_period_metric_sql(
        self, time_filter_clause: str, start_date: date, end_date: date
    ) -> tuple[str, list]:
        """
        Returns SQL to get the overall rate for a single period.
        The date filter is now applied to the final `filtered_intervals` CTE.
        """
        base_ctes = self._get_base_handling_ctes()
        sql = f"""
        {base_ctes}
        SELECT
            ROUND(
                COUNT(CASE WHEN interval_minutes <= 5 THEN 1 END) * 100.0 / NULLIF(COUNT(*), 0),
                2
            ) AS metric_value
        FROM filtered_intervals
        WHERE DATE(start_time) >= %s AND DATE(start_time) <= %s;
        """
        params = [start_date, end_date]
        return sql, params

    def get_time_series_metric_sql(
        self, time_filter_clause: str, start_date: date, end_date: date
    ) -> tuple[str, list]:
        """
        Returns SQL for time-series data of the handling rate.
        The date filter is correctly applied to the start_time of the intervals.
        """
        date_trunc_unit = self.time_series_granularity
        base_ctes = self._get_base_handling_ctes()

        sql = f"""
        WITH date_series AS (
          SELECT
            DATE_TRUNC('{date_trunc_unit}', generate_series AT TIME ZONE 'Asia/Bangkok') AS series_date
          FROM
            generate_series((%s::date)::timestamp AT TIME ZONE 'Asia/Bangkok', (%s::date)::timestamp AT TIME ZONE 'Asia/Bangkok', INTERVAL '1 {date_trunc_unit}') AS generate_series
        ),
        rate_data AS (
            {base_ctes}
            SELECT
                DATE_TRUNC('{date_trunc_unit}', start_time AT TIME ZONE 'Asia/Bangkok') AS handling_date,
                COUNT(CASE WHEN interval_minutes <= 5 THEN 1 END) AS within_5_min_count,
                COUNT(*) AS total_count
            FROM filtered_intervals
            WHERE DATE(start_time) >= %s AND DATE(start_time) <= %s
            GROUP BY DATE_TRUNC('{date_trunc_unit}', start_time AT TIME ZONE 'Asia/Bangkok')
        )
        SELECT
          ds.series_date::date AS date,
          ROUND(
            rd.within_5_min_count * 100.0 / NULLIF(rd.total_count, 0),
            2
          ) AS value
        FROM
          date_series ds
        LEFT JOIN
          rate_data rd ON ds.series_date = rd.handling_date
        ORDER BY
          ds.series_date;
        """
        params = [start_date, end_date, start_date, end_date]
        return sql, params

class HandlingRateWithinSLAAPIView(RawSQLMetricComparisonAPIView):
    """
    API endpoint to calculate and compare the percentage of handling intervals
    completed within SLA threshold between two periods, including time-series data.
    """

    filename_title = _("Handling Rate Within SLA Threshold")
    table_name = "ticket_statuslog"
    date_column_name = "created_on"
    serializer_class = MessageSummarySerializer
    time_series_granularity = "day"
    metric_aggregate_sql = "AVG"
    units = "%"
    sla_channel = "line"  # channel to use for SLA threshold

    filter_conditions = ""

    @property
    def threshold_minutes(self) -> float:
        """
        Fetch SLA threshold for handling_time for the given channel and convert to minutes.
        Returns 5 minutes by default if SLA is missing or invalid.
        """
        sla = SLA.objects.filter(name="handling_time", channel=self.sla_channel).first()
        default_threshold = 5.0

        if not sla or not sla.value:
            return default_threshold

        try:
            value = float(sla.value)
        except (TypeError, ValueError):
            return default_threshold

        if sla.unit == "second":
            return value / 60.0
        elif sla.unit == "hour":
            return value * 60.0
        # default: assume minutes
        return value

    def _get_base_handling_ctes(self) -> str:
        return f"""
        WITH ordered_status AS (
            SELECT
                ticket_id_id,
                created_by_id,
                status_id_id,
                created_on,
                LEAD(status_id_id) OVER (PARTITION BY ticket_id_id ORDER BY created_on) AS next_status,
                LEAD(created_by_id) OVER (PARTITION BY ticket_id_id ORDER BY created_on) AS next_by,
                LEAD(created_on) OVER (PARTITION BY ticket_id_id ORDER BY created_on) AS next_time
            FROM ticket_statuslog
            WHERE status_id_id IN (3, 6)
        ),
        handling_intervals AS (
            SELECT
                ticket_id_id,
                created_by_id,
                next_by,
                created_on AS start_time,
                next_time AS end_time,
                status_id_id AS start_status,
                next_status AS end_status,
                EXTRACT(EPOCH FROM (next_time - created_on)) / 60.0 AS interval_minutes
            FROM ordered_status
            WHERE next_time IS NOT NULL
        ),
        filtered_intervals AS (
            SELECT *
            FROM handling_intervals
            WHERE
                (start_status = 3 AND end_status = 6 AND created_by_id = next_by)
                OR
                (start_status = 3 AND end_status = 3 AND created_by_id <> next_by)
        )
        """

    def get_single_period_metric_sql(
        self, time_filter_clause: str, start_date: date, end_date: date
    ) -> tuple[str, list]:
        threshold = self.threshold_minutes
        base_ctes = self._get_base_handling_ctes()
        sql = f"""
        {base_ctes}
        SELECT
            ROUND(
                COUNT(CASE WHEN interval_minutes <= {threshold} THEN 1 END) * 100.0 / NULLIF(COUNT(*), 0),
                2
            ) AS metric_value
        FROM filtered_intervals
        WHERE DATE(start_time) >= %s AND DATE(start_time) <= %s;
        """
        params = [start_date, end_date]
        return sql, params

    def get_time_series_metric_sql(
        self, time_filter_clause: str, start_date: date, end_date: date
    ) -> tuple[str, list]:
        threshold = self.threshold_minutes
        date_trunc_unit = self.time_series_granularity
        base_ctes = self._get_base_handling_ctes()

        sql = f"""
        WITH date_series AS (
          SELECT
            DATE_TRUNC('{date_trunc_unit}', generate_series AT TIME ZONE 'Asia/Bangkok') AS series_date
          FROM
            generate_series((%s::date)::timestamp AT TIME ZONE 'Asia/Bangkok',
                            (%s::date)::timestamp AT TIME ZONE 'Asia/Bangkok',
                            INTERVAL '1 {date_trunc_unit}') AS generate_series
        ),
        rate_data AS (
            {base_ctes}
            SELECT
                DATE_TRUNC('{date_trunc_unit}', start_time AT TIME ZONE 'Asia/Bangkok') AS handling_date,
                COUNT(CASE WHEN interval_minutes <= {threshold} THEN 1 END) AS within_sla_count,
                COUNT(*) AS total_count
            FROM filtered_intervals
            WHERE DATE(start_time) >= %s AND DATE(start_time) <= %s
            GROUP BY DATE_TRUNC('{date_trunc_unit}', start_time AT TIME ZONE 'Asia/Bangkok')
        )
        SELECT
          ds.series_date::date AS date,
          ROUND(
            rd.within_sla_count * 100.0 / NULLIF(rd.total_count, 0),
            2
          ) AS value
        FROM
          date_series ds
        LEFT JOIN
          rate_data rd ON ds.series_date = rd.handling_date
        ORDER BY
          ds.series_date;
        """
        params = [start_date, end_date, start_date, end_date]
        return sql, params


class TicketStatusCountAPIView(
    RawSQLQueryAPIView
):  # Assuming this is the intended view
    """
    API endpoint to get a summary of ticket counts by status.
    Inherits time filtering logic from RawSQLQueryAPIView, applying it to ticket_statuslog.created_on.
    """

    filename_title = _("Ticket Status Count")
    serializer_class = TicketStatusSummarySerializer
    date_column_name = "created_on"  # Column for time filtering in ticket_statuslog

    def get_sql_query(
        self, time_filter_clause: str, params: list
    ) -> tuple[str, list]:  # --- FIX: Return type hint ---
        """
        Returns the specific SQL query for ticket status summary.
        The `time_filter_clause` is embedded into the WHERE clause of the latest_status CTE.
        """
        sql = f"""
        WITH status_list AS (
            SELECT * FROM (VALUES
                (2, 'Open'),
                (3, 'Assigned'),
                (4, 'Waiting'),
                (5, 'Pending to Close'),
                (6, 'Closed')
            ) AS s(id, name)
            ),
            latest_status AS (
            SELECT DISTINCT ON (ticket_id_id)
                ticket_id_id,
                status_id_id
            FROM
                ticket_statuslog
            WHERE
                1=1 {time_filter_clause} -- Apply the time filter here (applies to created_on)
            ORDER BY
                ticket_id_id,
                created_on DESC
            ),
            status_counts AS (
            SELECT
                status_id_id,
                COUNT(*) AS ticket_count
            FROM
                latest_status
            WHERE
                status_id_id != 1  -- exclude 'Default' status
            GROUP BY
                status_id_id
            )
            SELECT
            sl.name AS status,
            COALESCE(sc.ticket_count, 0) AS "จำนวนทิกเก็ต"
            FROM
            status_list sl
            LEFT JOIN
            status_counts sc ON sl.id = sc.status_id_id
            ORDER BY
            sl.id;
        """

        return sql, params  # --- FIX: Return as a tuple ---


# ทิกเก็ตที่ปิดแล้วของเจ้าหน้าที่: ประเภทเคส
class ClosedTicketsByCaseTypeAPIView(RawSQLQueryAPIView):
    """
    API endpoint to get a summary of closed tickets by case type.
    Inherits time filtering logic from RawSQLQueryAPIView, applying it to ticket_statuslog.created_on.
    """

    filename_title = _("Closed Tickets by Case Type")
    serializer_class = ClosedTicketsByCaseTypeSerializer
    date_column_name = "created_on"

    def get_sql_query(self, time_filter_clause: str, params: list) -> str:
        """
        Returns the specific SQL query for closed tickets by case type.
        The `time_filter_clause` is embedded into the `latest_status` CTE's WHERE clause.
        """
        sql = f"""
        WITH latest_status AS (
          SELECT DISTINCT ON (ticket_id_id)
            ticket_id_id,
            status_id_id,
            created_by_id
          FROM ticket_statuslog -- <--- ASSUMED TABLE NAME
          WHERE 1=1 {time_filter_clause} -- Apply the time filter here
          ORDER BY ticket_id_id, created_on DESC
        ),
        closed_by_agent AS (
          SELECT
            ticket_id_id
          FROM latest_status
          WHERE status_id_id = 6  -- Closed status ID
            AND created_by_id != 2  -- Not closed by bot (assuming bot user_id is 2)
        )
        SELECT
          ttt.case_type AS "ประเภทเคส",
          COUNT(*) AS "จำนวนทิกเก็ต"
        FROM
          closed_by_agent cba
        JOIN
          ticket_ticket_topics tttopics ON cba.ticket_id_id = tttopics.ticket_id -- <--- ASSUMED TABLE NAME
        JOIN
          ticket_tickettopic ttt ON tttopics.tickettopic_id = ttt.id -- <--- ASSUMED TABLE NAME
        GROUP BY
          ttt.case_type
        ORDER BY
          "จำนวนทิกเก็ต" DESC;
        """
        return sql, params


# ทิกเก็ตที่ปิดแล้วของเจ้าหน้าที่: หัวข้อเคสย่อย
class ClosedTicketsByCaseTopicAPIView(RawSQLQueryAPIView):
    """
    API endpoint to get a summary of closed tickets by case topic.
    Inherits time filtering logic from RawSQLQueryAPIView, applying it to ticket_statuslog.created_on.
    Uses table names exactly as provided in the query.
    """

    filename_title = _("Closed Tickets by Case Topic")
    serializer_class = ClosedTicketsByCaseTopicSerializer
    date_column_name = "created_on"

    def get_sql_query(self, time_filter_clause: str, params: list) -> str:
        """
        Returns the specific SQL query for closed tickets by case topic.
        The `time_filter_clause` is embedded into the `latest_status` CTE's WHERE clause.
        Uses table names exactly as provided in the query (e.g., 'ticket_statuslog').
        """
        sql = f"""
        WITH latest_status AS (
          SELECT DISTINCT ON (ticket_id_id)
            ticket_id_id,
            status_id_id,
            created_by_id
          FROM ticket_statuslog -- Using table name as provided
          WHERE 1=1 {time_filter_clause} -- Apply the time filter here
          ORDER BY ticket_id_id, created_on DESC
        ),
        closed_by_agent AS (
          SELECT
            ticket_id_id
          FROM latest_status
          WHERE status_id_id = 6  -- Closed status ID
            AND created_by_id != 2  -- Not closed by bot (assuming bot user_id is 2)
        )
        SELECT
          ttt.case_topic AS "หัวข้อเคสย่อย",
          COUNT(*) AS "จำนวนทิกเก็ต"
        FROM
          closed_by_agent cba
        JOIN
          ticket_ticket_topics tttopics ON cba.ticket_id_id = tttopics.ticket_id -- Using table name as provided
        JOIN
          ticket_tickettopic ttt ON tttopics.tickettopic_id = ttt.id -- Using table name as provided
        GROUP BY
          ttt.case_topic
        ORDER BY
          "จำนวนทิกเก็ต" DESC;
        """

        return sql, params


# ทิกเก็ตที่ปิดแล้วของเจ้าหน้าที่: ประเภทเคสและหัวข้อย่อย
class ClosedTicketsByCaseTypeAndTopicAPIView(RawSQLQueryAPIView):
    """
    API endpoint to get a summary of closed tickets by case type AND case topic.
    Inherits time filtering logic from RawSQLQueryAPIView, applying it to ticket_statuslog.created_on.
    Uses table names exactly as provided in the query.
    """

    filename_title = _("Closed Tickets by Case Type and Topic")
    serializer_class = ClosedTicketsByCaseTypeAndTopicSerializer
    date_column_name = "created_on"

    def get_sql_query(self, time_filter_clause: str, params: list) -> str:
        """
        Returns the specific SQL query for closed tickets by case type and topic.
        The `time_filter_clause` is embedded into the `latest_status` CTE's WHERE clause.
        Uses table names exactly as provided in the query (e.g., 'ticket_statuslog').
        """
        sql = f"""
        WITH latest_status AS (
          SELECT DISTINCT ON (ticket_id_id)
            ticket_id_id,
            status_id_id,
            created_by_id
          FROM ticket_statuslog -- Using table name as provided
          WHERE 1=1 {time_filter_clause} -- Apply the time filter here
          ORDER BY ticket_id_id, created_on DESC
        ),
        closed_by_agent AS (
          SELECT
            ticket_id_id
          FROM latest_status
          WHERE status_id_id = 6  -- Closed status ID
            AND created_by_id != 2  -- Not closed by bot (assuming bot user_id is 2)
        )
        SELECT
          ttt.case_type AS "ประเภทเคส",
          ttt.case_topic AS "หัวข้อย่อย",
          COUNT(*) AS "จำนวนทิกเก็ต"
        FROM
          closed_by_agent cba
        JOIN
          ticket_ticket_topics tttopics ON cba.ticket_id_id = tttopics.ticket_id -- Using table name as provided
        JOIN
          ticket_tickettopic ttt ON tttopics.tickettopic_id = ttt.id -- Using table name as provided
        GROUP BY
          ttt.case_type,
          ttt.case_topic
        ORDER BY
          "จำนวนทิกเก็ต" DESC;
        """

        return sql, params

class ClosedTicketsWithCSATAPIView(RawSQLQueryAPIView):
    """
    API endpoint to retrieve a list of closed tickets,
    fetching latest status and owner from logs.
    """

    filename_title = _("Closed Tickets")
    serializer_class = ClosedTicketSerializer
    date_column_name = "fc.cycle_end"

    def get_csat_filter(self):
        """
        Get CSAT filter value from request parameters.
        Returns the csat parameter from query string, defaulting to 1 if not provided.
        """
        csat = self.request.query_params.get('csat')
        if csat is not None:
            try:
                return int(csat)
            except (ValueError, TypeError):
                return 1  # Default to 1 if invalid value
        return 1  # Default to 1 if not provided

    def get_sql_query(
        self, time_filter_clause: str, time_filter_params: list
    ) -> tuple[str, list]:
        """
        Constructs the SQL query for closed tickets.
        """
        csat_filter = self.get_csat_filter()

        sql_query = f"""
        WITH StatusEvents AS (
            SELECT
                tsl.ticket_id_id,
                tsl.created_on AS event_time,
                tsl.status_id_id,
                LAG(tsl.status_id_id) OVER (PARTITION BY tsl.ticket_id_id ORDER BY tsl.created_on) AS prev_status_id,
                LEAD(tsl.created_on) OVER (PARTITION BY tsl.ticket_id_id ORDER BY tsl.created_on) AS next_event_time
            FROM
                ticket_statuslog tsl
        ),
        CycleMarkers AS (
            SELECT
                se.ticket_id_id,
                se.event_time,
                se.status_id_id,
                CASE
                    WHEN se.status_id_id NOT IN (1, 6) AND (se.prev_status_id IS NULL OR se.prev_status_id = 6) THEN se.event_time
                    ELSE NULL
                END AS general_active_period_start_candidate,
                CASE
                    WHEN se.status_id_id = 3 THEN se.event_time
                    ELSE NULL
                END AS assigned_time_candidate,
                CASE
                    WHEN se.status_id_id = 6 THEN se.event_time
                    ELSE NULL
                END AS cycle_end_time_candidate,
                SUM(CASE WHEN se.status_id_id = 6 THEN 1 ELSE 0 END) OVER (PARTITION BY se.ticket_id_id ORDER BY se.event_time) AS cycle_group
            FROM
                StatusEvents se
        ),
        GroupedCycles AS (
            SELECT
                cm.ticket_id_id,
                MIN(cm.general_active_period_start_candidate) FILTER (WHERE cm.general_active_period_start_candidate IS NOT NULL) AS actual_cycle_start_general,
                MIN(cm.assigned_time_candidate) FILTER (WHERE cm.assigned_time_candidate IS NOT NULL) AS actual_cycle_start_assigned,
                cm.cycle_group
            FROM
                CycleMarkers cm
            WHERE
                cm.status_id_id NOT IN (1, 6)
            GROUP BY
                cm.ticket_id_id, cm.cycle_group
        ),
        FinalCycles AS (
            SELECT
                gc.ticket_id_id,
                CASE
                    WHEN gc.actual_cycle_start_assigned IS NOT NULL AND gc.actual_cycle_start_assigned >= gc.actual_cycle_start_general
                    THEN gc.actual_cycle_start_assigned
                    ELSE gc.actual_cycle_start_general
                END AS cycle_start,
                (SELECT MIN(se.event_time) FROM StatusEvents se WHERE se.ticket_id_id = gc.ticket_id_id AND se.status_id_id = 6 AND se.event_time > gc.actual_cycle_start_general) AS cycle_end
            FROM
                GroupedCycles gc
            WHERE
                gc.actual_cycle_start_general IS NOT NULL
                AND (SELECT MIN(se.event_time) FROM StatusEvents se WHERE se.ticket_id_id = gc.ticket_id_id AND se.status_id_id = 6 AND se.event_time > gc.actual_cycle_start_general) IS NOT NULL
        )
        SELECT
            tt.id AS "เลขทิกเก็ต",
            ts.name AS "สถานะทิกเก็ต",
            cu.name AS "ลูกค้า",
            tp.name AS "ความสำคัญ",
            COALESCE(ta.sentiment, 'N/A') AS "ความรู้สึก",
            uu.name AS "เจ้าหน้าที่",
            -- Convert timestamps to local timezone (Asia/Bangkok)
            fc.cycle_start AT TIME ZONE 'Asia/Bangkok' AS "เวลาที่สร้าง",
            fc.cycle_end AT TIME ZONE 'Asia/Bangkok' AS "เวลาที่ปิด",
            (fc.cycle_end - fc.cycle_start) AS "เวลาที่ใช้",
            -- Extract CSAT score from feedback JSONB field
            CASE 
                WHEN tt.feedback IS NOT NULL AND tt.feedback ? 'csat'
                THEN (tt.feedback->>'csat')::INTEGER
                ELSE NULL
            END AS "คะแนน CSAT"
   
        FROM
            ticket_ticket tt
        JOIN
            ticket_status ts ON tt.status_id_id = ts.id
        JOIN
            ticket_ticketpriority tp ON tt.priority_id = tp.id
        JOIN
            user_user uu ON tt.owner_id_id = uu.id
        LEFT JOIN
            customer_customer cu ON tt.customer_id_id = cu.customer_id
        LEFT JOIN LATERAL (
            SELECT
                ta_inner.sentiment
            FROM
                ticket_ticketanalysis ta_inner
            WHERE
                ta_inner.ticket_id = tt.id
            ORDER BY
                ta_inner.id DESC
            LIMIT 1
        ) AS ta ON TRUE
        JOIN FinalCycles fc ON tt.id = fc.ticket_id_id
        WHERE
            tt.status_id_id = 6
            AND fc.cycle_end IS NOT NULL
            -- Add CSAT filter condition
            AND CASE 
                WHEN %s IS NOT NULL THEN
                    tt.feedback IS NOT NULL 
                    AND tt.feedback ? 'csat'
                    AND (tt.feedback->>'csat')::INTEGER = %s
                ELSE TRUE
            END
            {time_filter_clause}
        ORDER BY
            fc.cycle_start DESC;
        """
        
        # Add CSAT parameters to the parameter list
        csat_params = [csat_filter, csat_filter]
        all_params = csat_params + time_filter_params
        
        return sql_query, all_params