# Requirements
- Fetch customer's insurance policies and claims data from external APIs
- Store retrieved data as JSON in new database table called 'CustomerPolicies'
- CustomerPolicies table should have the following fields:
    - customer_id (integer, foreign key to Customer table)
    - policy_no (string), composite primary key, unique, extracted value from field called 'PolNo' from received policy details
    - policies (JSON) - for storing policies data received from API
    - claims (JSON) - for storing claims data received from API
    - updated_on (datetime)
    - updated_by (integer, foreign key to User table)
- Policy and claims data should be fetched and stored/updated in CustomerPolicies table when user triggers at frontend
- Backend should have API endpoints for frontend to call for fetching policy and claims data
- When fetching data from external APIs, backend should check for existing data in CustomerPolicies table using extracted PolNo field and update only if there are changes, otherwise create new record
- Frontend should call backend API endpoints to fetch policy and claims data when user triggers
- Policy and claims data should be displayed in frontend when user requests in src/lib/components/customer/tabs/PoliciesTab.svelte
- JSON file at backend for storing API calling steps below that can be customized and for backend to follow


# Journey
1. User clicks on fetch button in PoliciesTab.svelte
2. Only if such customer has Citizen ID, frontend calls backend API endpoint to fetch policy and claims data
3. Backend checks for existing data in CustomerPolicies table using extracted PolNo field and update only if there are changes, otherwise create new record
4. Backend returns policy and claims data to frontend
5. Frontend displays policy and claims data in PoliciesTab.svelte

# Steps for API callings
1. Get token. 
    - POST https://uat.thirdpartyadmin.co.th:4443/TPA.TMS.Web.API_PREPROV2/api/GetToken. 
    - Body { 
        "USERNAME": "BVTPA", 
        "PASSWORD": "*d!n^+Cb@1", 
        "SOCIAL_ID": Value from platform_user_id in customer_customerplatformidentity table,
        "CHANNEL_ID": Value from channel_id in customer_customerplatformidentity table,
        "CHANNEL": "LINE"
    }
    - Response: String e.g., "112233445566"

2. Store token in cache at backend. Token has 1 hour expiry. Token can be reused for multiple API calls within 1 hour.

3. Check if customer has Citizen ID. If not, return error to frontend.

4. Verify Citizen ID
    - POST https://uat.thirdpartyadmin.co.th:4443/TPA.TMS.Web.API_PREPROV2/api/SearchCitizenID
    - Set received token from step 1 as Bearer Token 
    - Body {
        "SOCIAL_ID": Value from platform_user_id in customer_customerplatformidentity table,
        "CHANNEL_ID": Value from channel_id in customer_customerplatformidentity table,
        "CHANNEL": "LINE"
    }
    - Response: JSON
    - Response example: 
    {
        "ListOfSearchCitizenID": [
            {
                "Status": "1",
                "CitizenID": "2019086318637"
            }
        ],
        "ErrorMessage": " "
    }
    - If Status is 1, Citizen ID is valid. If not, return error to frontend.

5. Fetch policy list
    - POST  https://uat.thirdpartyadmin.co.th:4443/TPA.TMS.Web.API_PREPROV2/api/PolicyListSocial
    - Set received token from step 1 as Bearer Token 
    - Body {
        "SOCIAL_ID": Value from platform_user_id in customer_customerplatformidentity table,
        "CHANNEL_ID": Value from channel_id in customer_customerplatformidentity table,
        "CHANNEL": "LINE",
        "CITIZEN_ID": Value from CitizenID in step 4 response
    }
    - Response: JSON
    - Result contains multiple policies (list). Each of them has field 'MemberCode' as unique identifier.
    - Response example: 

    {
        "ListOfPolicyListSocial": [
            {
                "Name": "ทดสอบ2",
                "NameEN": "ทดสอบ2",
                "Surname": "Chatbot",
                "SurnameEN": "Chatbot",
                "CitizenID": "2019086318637",
                "CardType": "Group Insurance",
                "InsurerCode": "INS202200006",
                "InsurerName": "บริษัท บลูเวนเจอร์ ทีพีเอ จำกัด",
                "InsurerNameEN": "บริษัท บลูเวนเจอร์ ทีพีเอ จำกัด",
                "InsurerDedicateedLine": " ",
                "CompanyCode": "COM202200080",
                "CompanyName": "บริษัท บลูเวนเจอร์ ทีพีเอ จำกัด",
                "CompanyNameEN": "บริษัท บลูเวนเจอร์ ทีพีเอ จำกัด",
                "StaffNo": " ",
                "EffFrom": "15/02/2024",
                "EffTo": "20/12/2025",
                "PolNo": "BVTPA_2024",
                "PlanCode": "PLAN 3",
                "PlanName": "แผน 3",
                "TelNo": " ",
                "Email": "",
                "MemberCode": "*********-2 Chatbot4",
                "EvenMBAccident": "Inpatient Benefits|Outpatient Benefits|PA",
                "EvenMBAccidentTH": "Inpatient Benefits|Outpatient Benefits|PA",
                "EvenMBAccidentEN": "Inpatient Benefits|Outpatient Benefits|PA",
                "EvenMBIllness": "Dental|Flexible Benefits|Inpatient Benefits|Outpatient Benefits",
                "EvenMBIllnessTH": "Dental|Flexible Benefits|Inpatient Benefits|Outpatient Benefits",
                "EvenMBIllnessEN": "Dental|Flexible Benefits|Inpatient Benefits|Outpatient Benefits",
                "EvenMBOtherTH": "",
                "EvenMBOtherEN": "",
                "BirthDate": "",
                "Gender": "Unknown",
                "Age": "0",
                "Passport": "",
                "CountryCode": "",
                "CertificateNo": "BVTPA_2024"
            }
        ],
        "ErrorMessage": " "
    }

6. Extract and save MemberCode from each policy in step 5 response to a temporary list

7. For each MemberCode in temporary list, fetch policy and claims details
    - POST  https://uat.thirdpartyadmin.co.th:4443/TPA.TMS.Web.API_PREPROV2/api/PolicyDetailSocial
    - Set received token from step 1 as Bearer Token 
    - Body {
        "SOCIAL_ID": Value from platform_user_id in customer_customerplatformidentity table,
        "CHANNEL_ID": Value from channel_id in customer_customerplatformidentity table,
        "CHANNEL": "LINE",
        "MEMBER_CODE": Value from MemberCode in step 6 response
    }
    - Response: JSON
    - Response example: 

    {
        "ListOfPolDet": [
            {
                "MainBenefit": "Outpatient Benefits",
                "MainBenefitEN": "Outpatient Benefits",
                "Coverage": [
                    {
                        "CovNo": "1",
                        "CovNoEN": "1",
                        "CovDesc": "ผู้ป่วยนอก",
                        "CovDescEN": "ผู้ป่วยนอก",
                        "CovLimit": "2,000",
                        "CovLimitEN": "2,000 ",
                        "CovUtilized": " (0 ครั้ง)",
                        "CovUtilizedEN": " (0 visit)"
                    },
                    {
                        "CovNo": "หมายเหตุ",
                        "CovNoEN": "Remarks",
                        "CovDesc": "1. (1) สูงสุดไม่เกิน 14,000 บาท/ปี",
                        "CovDescEN": "1. Max limit (1) = 14,000 baht /year",
                        "CovLimit": "",
                        "CovLimitEN": " ",
                        "CovUtilized": "",
                        "CovUtilizedEN": ""
                    },
                    {
                        "CovNo": "",
                        "CovNoEN": "",
                        "CovDesc": "2. (1) สูงสุดไม่เกิน 10 ครั้ง/ปี",
                        "CovDescEN": "2. Max limit (1) = 10 visit(s)/year",
                        "CovLimit": "",
                        "CovLimitEN": " ",
                        "CovUtilized": "",
                        "CovUtilizedEN": ""
                    }
                ]
            },
            {
                "MainBenefit": "Inpatient Benefits",
                "MainBenefitEN": "Inpatient Benefits",
                "Coverage": [
                    {
                        "CovNo": "1",
                        "CovNoEN": "1",
                        "CovDesc": "ค่าห้องผู้ป่วยปกติ และค่าอาหาร",
                        "CovDescEN": "ค่าห้องผู้ป่วยปกติ และค่าอาหาร",
                        "CovLimit": "2,500",
                        "CovLimitEN": "2,500 ",
                        "CovUtilized": "-",
                        "CovUtilizedEN": "-"
                    },
                    {
                        "CovNo": "2",
                        "CovNoEN": "2",
                        "CovDesc": "ค่าห้องผู้ป่วยหนัก และค่าอาหาร",
                        "CovDescEN": "ค่าห้องผู้ป่วยหนัก และค่าอาหาร",
                        "CovLimit": "5,000",
                        "CovLimitEN": "5,000 ",
                        "CovUtilized": "-",
                        "CovUtilizedEN": "-"
                    },
                    {
                        "CovNo": "3",
                        "CovNoEN": "3",
                        "CovDesc": "ค่ารักษาพยาบาลทั่วไป",
                        "CovDescEN": "ค่ารักษาพยาบาลทั่วไป",
                        "CovLimit": "125,000",
                        "CovLimitEN": "125,000 ",
                        "CovUtilized": "-",
                        "CovUtilizedEN": "-"
                    },
                    {
                        "CovNo": "4",
                        "CovNoEN": "4",
                        "CovDesc": "ค่ารถพยาบาล",
                        "CovDescEN": "ค่ารถพยาบาล",
                        "CovLimit": "125,000",
                        "CovLimitEN": "125,000 ",
                        "CovUtilized": "-",
                        "CovUtilizedEN": "-"
                    },
                    {
                        "CovNo": "5",
                        "CovNoEN": "5",
                        "CovDesc": "ค่าแพทย์ที่ปรึกษา กรณีไม่ผ่าตัด",
                        "CovDescEN": "ค่าแพทย์ที่ปรึกษา กรณีไม่ผ่าตัด",
                        "CovLimit": "125,000",
                        "CovLimitEN": "125,000 ",
                        "CovUtilized": "-",
                        "CovUtilizedEN": "-"
                    },
                    {
                        "CovNo": "6",
                        "CovNoEN": "6",
                        "CovDesc": "ยากลับบ้าน",
                        "CovDescEN": "ยากลับบ้าน",
                        "CovLimit": "2,000",
                        "CovLimitEN": "2,000 ",
                        "CovUtilized": "-",
                        "CovUtilizedEN": "-"
                    },
                    {
                        "CovNo": "7",
                        "CovNoEN": "7",
                        "CovDesc": "ค่าแพทย์ผ่าตัดและหัตถการ",
                        "CovDescEN": "ค่าแพทย์ผ่าตัดและหัตถการ",
                        "CovLimit": "125,000",
                        "CovLimitEN": "125,000 ",
                        "CovUtilized": "-",
                        "CovUtilizedEN": "-"
                    },
                    {
                        "CovNo": "8",
                        "CovNoEN": "8",
                        "CovDesc": "ค่าแพทย์ที่ปรึกษา กรณีผ่าตัด",
                        "CovDescEN": "ค่าแพทย์ที่ปรึกษา กรณีผ่าตัด",
                        "CovLimit": "125,000",
                        "CovLimitEN": "125,000 ",
                        "CovUtilized": "-",
                        "CovUtilizedEN": "-"
                    },
                    {
                        "CovNo": "9",
                        "CovNoEN": "9",
                        "CovDesc": "ค่าแพทย์เยี่ยมไข้",
                        "CovDescEN": "ค่าแพทย์เยี่ยมไข้",
                        "CovLimit": "275,000",
                        "CovLimitEN": "275,000 ",
                        "CovUtilized": "-",
                        "CovUtilizedEN": "-"
                    },
                    {
                        "CovNo": "หมายเหตุ",
                        "CovNoEN": "Remarks",
                        "CovDesc": "1. (3)+(4)+(5)+(6) สูงสุดไม่เกิน 250,000 บาท/ครั้ง",
                        "CovDescEN": "1. Max limit (3)+(4)+(5)+(6) = 250,000 baht /disability",
                        "CovLimit": "",
                        "CovLimitEN": " ",
                        "CovUtilized": "",
                        "CovUtilizedEN": ""
                    },
                    {
                        "CovNo": "",
                        "CovNoEN": "",
                        "CovDesc": "2. (1)+(2) สูงสุดไม่เกิน 90 วัน/ครั้ง",
                        "CovDescEN": "2. Max limit (1)+(2) = 90 day(s) /disability",
                        "CovLimit": "",
                        "CovLimitEN": " ",
                        "CovUtilized": "",
                        "CovUtilizedEN": ""
                    },
                    {
                        "CovNo": "",
                        "CovNoEN": "",
                        "CovDesc": "3. (7)+(8) สูงสุดไม่เกิน 125,000 บาท/ครั้ง",
                        "CovDescEN": "3. Max limit (7)+(8) = 125,000 baht /disability",
                        "CovLimit": "",
                        "CovLimitEN": " ",
                        "CovUtilized": "",
                        "CovUtilizedEN": ""
                    },
                    {
                        "CovNo": "",
                        "CovNoEN": "",
                        "CovDesc": "4. ผลประโยชน์ IPD สูงสุดไม่เกิน 275,000 บาท/ปี",
                        "CovDescEN": "4. Max limit IPD = 275,000 baht/year",
                        "CovLimit": "",
                        "CovLimitEN": " ",
                        "CovUtilized": "",
                        "CovUtilizedEN": ""
                    },
                    {
                        "CovNo": "",
                        "CovNoEN": "",
                        "CovDesc": "5. (9) รวมกันไม่เกิน 90 วัน/ครั้ง",
                        "CovDescEN": "5. Max limit (9) = 90 day(s) /disability",
                        "CovLimit": "",
                        "CovLimitEN": " ",
                        "CovUtilized": "",
                        "CovUtilizedEN": ""
                    },
                    {
                        "CovNo": "",
                        "CovNoEN": "",
                        "CovDesc": "6. (2) รวมกันไม่เกิน 15 วัน/ครั้ง",
                        "CovDescEN": "6. Max limit (2) = 15 day(s) /disability",
                        "CovLimit": "",
                        "CovLimitEN": " ",
                        "CovUtilized": "",
                        "CovUtilizedEN": ""
                    }
                ]
            },
            {
                "MainBenefit": "PA",
                "MainBenefitEN": "PA",
                "Coverage": [
                    {
                        "CovNo": "1",
                        "CovNoEN": "1",
                        "CovDesc": "ค่ารักษาพยาบาลจากอุบัติเหตุทั่วไป (ไม่รวมมอเตอร์ไซด์)",
                        "CovDescEN": "ค่ารักษาพยาบาลจากอุบัติเหตุทั่วไป (ไม่รวมมอเตอร์ไซด์)",
                        "CovLimit": "20,000",
                        "CovLimitEN": "20,000 ",
                        "CovUtilized": "-",
                        "CovUtilizedEN": "-"
                    },
                    {
                        "CovNo": "2",
                        "CovNoEN": "2",
                        "CovDesc": "ค่ารักษาพยาบาลจากอุบัติเหตุมอเตอร์ไซด์",
                        "CovDescEN": "ค่ารักษาพยาบาลจากอุบัติเหตุมอเตอร์ไซด์",
                        "CovLimit": "20,000",
                        "CovLimitEN": "20,000 ",
                        "CovUtilized": "-",
                        "CovUtilizedEN": "-"
                    }
                ]
            },
            {
                "MainBenefit": "Dental",
                "MainBenefitEN": "Dental",
                "Coverage": [
                    {
                        "CovNo": "1",
                        "CovNoEN": "1",
                        "CovDesc": "ทันตกรรม",
                        "CovDescEN": "ทันตกรรม",
                        "CovLimit": "3,000",
                        "CovLimitEN": "3,000 ",
                        "CovUtilized": "-",
                        "CovUtilizedEN": "-"
                    }
                ]
            },
            {
                "MainBenefit": "Flexible Benefits",
                "MainBenefitEN": "Flexible Benefits",
                "Coverage": [
                    {
                        "CovNo": "1",
                        "CovNoEN": "1",
                        "CovDesc": "ค่าส่งเสริมสุขภาพสายตา",
                        "CovDescEN": "ค่าส่งเสริมสุขภาพสายตา",
                        "CovLimit": "3,000",
                        "CovLimitEN": "3,000 ",
                        "CovUtilized": "-",
                        "CovUtilizedEN": "-"
                    },
                    {
                        "CovNo": "2",
                        "CovNoEN": "2",
                        "CovDesc": "คลาสฟิตเนส เครื่องออกกำลังกาย อุปกรณ์กีฬา",
                        "CovDescEN": "คลาสฟิตเนส เครื่องออกกำลังกาย อุปกรณ์กีฬา",
                        "CovLimit": "3,000",
                        "CovLimitEN": "3,000 ",
                        "CovUtilized": "-",
                        "CovUtilizedEN": "-"
                    },
                    {
                        "CovNo": "หมายเหตุ",
                        "CovNoEN": "Remarks",
                        "CovDesc": "1. (1)+(2)+(3)+(4) สูงสุดไม่เกิน 3,000 บาท/ปี",
                        "CovDescEN": "1. Max limit (1)+(2)+(3)+(4) = 3,000 baht /year",
                        "CovLimit": "",
                        "CovLimitEN": " ",
                        "CovUtilized": "",
                        "CovUtilizedEN": ""
                    }
                ]
            }
        ],
        "ListOfPolClaim": [
            {
                "ClmInsurerCode": "INS202200006",
                "ClmInsurerTH": "บริษัท บลูเวนเจอร์ ทีพีเอ จำกัด",
                "ClmInsurerEN": "บริษัท บลูเวนเจอร์ ทีพีเอ จำกัด",
                "ClmCompanyCode": "COM202200080",
                "ClmCompanyTH": "บริษัท บลูเวนเจอร์ ทีพีเอ จำกัด",
                "ClmCompanyEN": "บริษัท บลูเวนเจอร์ ทีพีเอ จำกัด",
                "ClmCardType": "Group Insurance",
                "ClmPolNo": "BVTPA_2024",
                "ClmNo": "C20250001714",
                "ClmSource": "Reimbursement",
                "ClmType": "Accident",
                "ClmDiagCode": "A25.9",
                "ClmDiagTH": "ไข้ที่เกิดจากหนู ที่มิได้ระบุรายละเอียด",
                "ClmDiagEN": "Rat-bite fever,unspecified                                                     ",
                "ClmStatus": "Open",
                "ClmStatusTxt": "อยู่ระหว่างดำเนินการ",
                "ClmStatusTxtEN": "In Progress",
                "ClmVisitDate": "23/05/2025",
                "ClmDischargeDate": "23/05/2025",
                "ClmIncurredAmt": "1000000",
                "ClmPayable": "2000",
                "ClmPaymentDate": "",
                "ClmProviderCode": "PVR201600944",
                "ClmProviderTH": "พญาไท 2 BDMS",
                "ClmProviderEN": "PHYATHAI 2 HOSPITAL"
            },
            {
                "ClmInsurerCode": "INS202200006",
                "ClmInsurerTH": "บริษัท บลูเวนเจอร์ ทีพีเอ จำกัด",
                "ClmInsurerEN": "บริษัท บลูเวนเจอร์ ทีพีเอ จำกัด",
                "ClmCompanyCode": "COM202200080",
                "ClmCompanyTH": "บริษัท บลูเวนเจอร์ ทีพีเอ จำกัด",
                "ClmCompanyEN": "บริษัท บลูเวนเจอร์ ทีพีเอ จำกัด",
                "ClmCardType": "Group Insurance",
                "ClmPolNo": "BVTPA_2024",
                "ClmNo": "C20250001706",
                "ClmSource": "Credit",
                "ClmType": "Accident",
                "ClmDiagCode": "",
                "ClmDiagTH": "",
                "ClmDiagEN": "",
                "ClmStatus": "Open",
                "ClmStatusTxt": "อยู่ระหว่างดำเนินการ",
                "ClmStatusTxtEN": "In Progress",
                "ClmVisitDate": "02/05/2025",
                "ClmDischargeDate": "02/05/2025",
                "ClmIncurredAmt": "0",
                "ClmPayable": "0",
                "ClmPaymentDate": "",
                "ClmProviderCode": "PVR201600946",
                "ClmProviderTH": "พระรามเก้า",
                "ClmProviderEN": "PRARAM 9 HOSPITAL"
            }
        ],
        "ErrorMessage": " "
    }

8. Store policy and claims data in CustomerPolicies table
    - customer_id: Extracted from platform_user_id in customer_customerplatformidentity table
    - policy_no: Extracted from PolNo field in received policy details
    - policies: JSON stringified received policy details
    - claims: JSON stringified received claims details
    - updated_on: Current datetime
    - updated_by: 1 (hardcoded for now)