import json
import logging
from datetime import datetime
from django.utils import timezone
from setting.models import SystemSettings
from user.models import User
from connectors.models import LineChannel

logger = logging.getLogger('django.subscription')


class StorageQuotaService:
    """Simple service to get storage usage for quota monitoring"""

    @classmethod
    def get_storage_usage(cls):
        """
        Get current storage usage for quota monitoring.
        Returns simplified storage metrics or None if unavailable.
        """
        try:
            from devproject.utils.azure_storage import AzureBlobStorage

            azure_storage = AzureBlobStorage()
            usage_data = azure_storage.get_storage_usage()

            logger.info(f"Storage usage retrieved: {usage_data['total_size_gb']:.2f} GB, "
                       f"{usage_data['total_blob_count']} blobs")

            return usage_data

        except Exception as e:
            logger.warning(f"Could not retrieve storage usage for quota monitoring: {str(e)}")
            return None


class SubscriptionDataService:
    SETTING_KEY = 'SUBSCRIPTION'
    
    @classmethod
    def get_subscription_data(cls):
        """Get the subscription data from SystemSettings"""
        try:
            setting = SystemSettings.objects.get(key=cls.SETTING_KEY)

            # status depends on expiration date
            data = setting.get_json_value()
            if data.get('expires_at'):
                expires_at = datetime.fromisoformat(data['expires_at'].replace('Z', '+00:00'))
                data['status'] = 'expired' if timezone.now() > expires_at else 'active'

            return data
        except SystemSettings.DoesNotExist:
            return None
    
    @classmethod
    def save_subscription_data(cls, data, user=None):
        """Save subscription data to SystemSettings"""
        setting, created = SystemSettings.objects.get_or_create(
            key=cls.SETTING_KEY,
            defaults={
                'value_type': 'json',
                'description': 'Organization subscription data',
                'updated_by': user
            }
        )
        
        setting.set_json_value(data)
        if user:
            setting.updated_by = user
        setting.save()
        
        return data
    
    @classmethod
    def has_active_subscription(cls):
        """Check if there's an active subscription"""
        data = cls.get_subscription_data()
        return data is not None and data.get('status') == 'active'
    
    @classmethod
    def is_subscription_expired(cls):
        """Check if subscription is expired"""
        data = cls.get_subscription_data()
        if not data or not data.get('expires_at'):
            return True
        
        expires_at = datetime.fromisoformat(data['expires_at'].replace('Z', '+00:00'))
        return timezone.now() > expires_at


class QuotaValidationService:
    @classmethod
    def can_create_user(cls):
        """Check if a new user can be created based on subscription quota"""
        data = SubscriptionDataService.get_subscription_data()
        
        if not data:
            return {'allowed': False, 'error': 'No active subscription found'}
        
        if data.get('status') != 'active':
            return {'allowed': False, 'error': f'Subscription is {data.get("status", "inactive")}'}
        
        # Check if subscription is expired
        if SubscriptionDataService.is_subscription_expired():
            return {'allowed': False, 'error': 'Subscription has expired'}
        
        # Get quota limits
        quota = data.get('quota', {})
        max_users = quota.get('max_active_users')
        
        if max_users is None:
            return {'allowed': False, 'error': 'No user quota defined in subscription'}
        
        # Get current active user count
        current_users = User.objects.filter(is_active=True).count()

        # Exclude two built-in Admin and System accounts
        current_users = current_users - 2
        
        return {
            'allowed': current_users < max_users,
            'quota_info': {
                'current_users': current_users,
                'max_users': max_users,
                'remaining_slots': max_users - current_users,
            }
        }
    
    @classmethod
    def get_quota_status(cls):
        """Get current quota status including storage usage"""
        data = SubscriptionDataService.get_subscription_data()

        if not data:
            return {'error': 'No subscription found'}

        quota = data.get('quota', {})
        current_users = User.objects.filter(is_active=True).count()
        current_users = current_users - 2 # Exclude two built-in Admin and System accounts

        # Get max and current active LINE account count
        max_line_accounts = quota.get('max_line_accounts')
        current_line_accounts = LineChannel.objects.filter(is_deleted=False, is_active=True).count()

        # Get current storage usage
        storage_usage = StorageQuotaService.get_storage_usage()
        
        # Calculate storage quota status
        max_storage_gb = quota.get('max_storage_gb')
        current_storage_gb = storage_usage.get('total_size_gb', 0) if storage_usage else 0

        result = {
            'organization_name': data.get('organization_name'),
            'tier_name': data.get('tier_name'),
            'status': data.get('status') == 'active' and not SubscriptionDataService.is_subscription_expired(),
            'expires_at': data.get('expires_at'),
            'quota': {
                'max_active_users': quota.get('max_active_users'),
                'current_active_users': current_users,
                'max_line_accounts': max_line_accounts,
                'current_line_accounts': current_line_accounts,
                'max_ai_workflow_units': quota.get('max_ai_workflow_units'),
                'current_ai_workflow_units': 0, # TODO: Get actual number
                'max_messages_per_min': quota.get('max_messages_per_min'),
                'max_storage_gb': max_storage_gb,
                'current_storage_gb': current_storage_gb,
            },
            'features': data.get('features', {})
        }

        return result


    @classmethod
    def check_feature_access(cls, feature_name):
        """Check if a specific feature is enabled in current subscription"""
        data = SubscriptionDataService.get_subscription_data()
        
        if not data or data.get('status') != 'active':
            return False
        
        if SubscriptionDataService.is_subscription_expired():
            return False
        
        features = data.get('features', {})
        return features.get(feature_name, False)


    @classmethod
    def can_create_line_account(cls):
        """Check if a new LINE account can be created based on subscription quota"""
        quota_status = cls.get_quota_status()
        
        if 'error' in quota_status:
            return {'allowed': False, 'error': quota_status['error']}
        
        if quota_status.get('status') != 'active':
            return {'allowed': False, 'error': f'Subscription is {quota_status.get("status", "inactive")}'}
        
        # Check if subscription is expired
        if SubscriptionDataService.is_subscription_expired():
            return {'allowed': False, 'error': 'Subscription has expired'}
        
        quota = quota_status.get('quota', {})
        max_line_accounts = quota.get('max_line_accounts')
        current_line_accounts = quota.get('current_line_accounts', 0)
        
        # Handle unlimited case
        if max_line_accounts == "unlimited":
            return {'allowed': True}
        
        if max_line_accounts is None:
            return {'allowed': False, 'error': 'No LINE account quota defined in subscription'}
        
        return {
            'allowed': current_line_accounts < max_line_accounts,
            'quota_info': {
                'current_line_accounts': current_line_accounts,
                'max_line_accounts': max_line_accounts,
                'remaining_slots': max_line_accounts - current_line_accounts,
            }
        }