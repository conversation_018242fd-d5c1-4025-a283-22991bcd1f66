import os
import logging
from django.apps import AppConfig
from django.utils import timezone
from datetime import datetime

logger = logging.getLogger(__name__)


class SubscriptionConfig(AppConfig):
    default_auto_field = 'django.db.models.BigAutoField'
    name = 'subscription'

    def ready(self):
        """Initialize subscription activation during Django startup"""
        self._auto_activate_subscription()

    def _auto_activate_subscription(self):
        """
        Automatically activate subscription based on environment variables.

        Reads SUBSCRIPTION_KEY, SUBSCRIPTION_COMPANY, and SUBSCRIPTION_EXPIRES_AT
        from environment variables and activates the subscription if all are present.
        """
        try:
            # Import here to avoid circular imports during Django startup
            from .services import SubscriptionDataService

            # Read environment variables
            subscription_key = os.getenv('SUBSCRIPTION_KEY')
            subscription_company = os.getenv('SUBSCRIPTION_COMPANY')
            subscription_expires_at = os.getenv('SUBSCRIPTION_EXPIRES_AT')

            # Check if all required environment variables are present
            if not all([subscription_key, subscription_company, subscription_expires_at]):
                logger.info("Subscription environment variables not fully configured. Removing existing subscription if any.")
                self._remove_subscription()
                return

            # Validate expiration date format
            try:
                # Parse ISO 8601 date string to validate format
                datetime.fromisoformat(subscription_expires_at.replace('Z', '+00:00'))
            except (ValueError, AttributeError) as e:
                logger.error(f"Invalid SUBSCRIPTION_EXPIRES_AT format: {subscription_expires_at}. Expected ISO 8601 format (e.g., '2025-12-31T23:59:59Z'). Error: {e}")
                return

            # Determine tier based on subscription key prefix
            tier_mapping = {
                'PRM-': ('premium', 'Premium'),
                'ENT-': ('enterprise', 'Enterprise'),
                'ENP-': ('enterprise_plus', 'Enterprise Plus')
            }

            tier_id, tier_name = None, None
            for prefix, (t_id, t_name) in tier_mapping.items():
                if subscription_key.startswith(prefix):
                    tier_id, tier_name = t_id, t_name
                    break

            if not tier_id:
                logger.error(f"Invalid subscription key format: {subscription_key}. Must start with PRM-, ENT-, or ENP-")
                return

            # Get tier configuration
            tier_config = self._get_tier_config(tier_id, subscription_key, subscription_company, subscription_expires_at)

            if not tier_config:
                logger.error(f"Unknown tier configuration for tier_id: {tier_id}")
                return

            # Check if subscription already exists and is the same
            existing_data = SubscriptionDataService.get_subscription_data()
            if existing_data and self._is_same_subscription(existing_data, tier_config):
                logger.info(f"Subscription already activated with the same configuration for {subscription_company}")
                return

            # Save subscription data
            SubscriptionDataService.save_subscription_data(tier_config)

            logger.info(f"Successfully auto-activated {tier_name} subscription for {subscription_company} (expires: {subscription_expires_at})")

        except Exception as e:
            logger.error(f"Failed to auto-activate subscription: {e}")
            # Don't raise the exception to prevent Django startup failure

    def _remove_subscription(self):
        """Remove existing subscription data when environment variables are not configured"""
        try:
            from .services import SubscriptionDataService
            from setting.models import SystemSettings

            try:
                setting = SystemSettings.objects.get(key=SubscriptionDataService.SETTING_KEY)
                setting.delete()
                logger.info("Removed existing subscription data due to missing environment variables")
            except SystemSettings.DoesNotExist:
                logger.debug("No existing subscription data to remove")

        except Exception as e:
            logger.error(f"Failed to remove subscription data: {e}")

    def _is_same_subscription(self, existing_data, new_config):
        """Check if the existing subscription matches the new configuration"""
        return (
            existing_data.get('subscription_key') == new_config.get('subscription_key') and
            existing_data.get('organization_name') == new_config.get('organization_name') and
            existing_data.get('expires_at') == new_config.get('expires_at')
        )

    def _get_tier_config(self, tier_id, subscription_key, company_name, expires_at):
        """Get tier configuration based on tier_id"""
        activated_on = timezone.now().isoformat()

        # Define tier configurations (based on init_subscription.py)
        tier_configs = {
            'premium': {
                "organization_name": company_name,
                "subscription_key": subscription_key,
                "tier_id": "premium",
                "tier_name": "Premium",
                "status": "active",
                "activated_on": activated_on,
                "expires_at": expires_at,
                "quota": {
                    "max_active_users": 5,
                    "max_line_accounts": 5,
                    "max_ai_workflow_units": 1,
                    "max_messages_per_min": 30,
                    "max_storage_gb": 250
                },
                "features": {
                    "custom_transfer_algo": False,
                    "custom_case_desc": False,
                    "custom_ai_workflow": False,
                    "ai_quick_reply": True,
                    "ai_smart_reply": False,
                    "ai_memory": False,
                    "crm_integration": False,
                    "crm_notify_claim": False,
                    "crm_case_system": False,
                    "dashboard_sla_config": False,
                    "dashboard_sla_alert": False,
                    "broadcasting": True
                },
                "metadata": {
                    "billing_contact": "<EMAIL>",
                    "technical_contact": "<EMAIL>"
                }
            },
            'enterprise': {
                "organization_name": company_name,
                "subscription_key": subscription_key,
                "tier_id": "enterprise",
                "tier_name": "Enterprise",
                "status": "active",
                "activated_on": activated_on,
                "expires_at": expires_at,
                "quota": {
                    "max_active_users": 20,
                    "max_line_accounts": "unlimited",
                    "max_ai_workflow_units": "unlimited",
                    "max_messages_per_min": 100,
                    "max_storage_gb": 1000
                },
                "features": {
                    "custom_transfer_algo": True,
                    "custom_case_desc": True,
                    "custom_ai_workflow": True,
                    "ai_quick_reply": True,
                    "ai_smart_reply": True,
                    "ai_memory": True,
                    "crm_integration": False,
                    "crm_notify_claim": False,
                    "crm_case_system": False,
                    "dashboard_sla_config": True,
                    "dashboard_sla_alert": True,
                    "broadcasting": True
                },
                "metadata": {
                    "billing_contact": "<EMAIL>",
                    "technical_contact": "<EMAIL>"
                }
            },
            'enterprise_plus': {
                "organization_name": company_name,
                "subscription_key": subscription_key,
                "tier_id": "enterprise_plus",
                "tier_name": "Enterprise Plus",
                "status": "active",
                "activated_on": activated_on,
                "expires_at": expires_at,
                "quota": {
                    "max_active_users": "unlimited",
                    "max_line_accounts": "unlimited",
                    "max_ai_workflow_units": "unlimited",
                    "max_messages_per_min": "unlimited",
                    "max_storage_gb": "unlimited"
                },
                "features": {
                    "custom_transfer_algo": True,
                    "custom_case_desc": True,
                    "custom_ai_workflow": True,
                    "ai_quick_reply": True,
                    "ai_smart_reply": True,
                    "ai_memory": True,
                    "crm_integration": True,
                    "crm_notify_claim": True,
                    "crm_case_system": True,
                    "dashboard_sla_config": True,
                    "dashboard_sla_alert": True,
                    "broadcasting": True
                },
                "metadata": {
                    "billing_contact": "<EMAIL>",
                    "technical_contact": "<EMAIL>"
                }
            }
        }

        return tier_configs.get(tier_id)
