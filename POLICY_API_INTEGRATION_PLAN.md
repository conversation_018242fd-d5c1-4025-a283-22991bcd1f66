# Policy Claims API Integration Implementation Plan

## Overview

This document outlines the comprehensive plan for replacing mock data in the `PoliciesTab.svelte` component with real API integration following the specifications defined in `policy-claims-workflow.json`.

## Current State Analysis

### Existing Implementation
- **Component**: `src/lib/components/customer/tabs/PoliciesTab.svelte`
- **Current API Service**: `src/lib/api/features/customer/customers.service.ts`
- **Mock Data**: Comprehensive mock data structure with 8 policies and 7 claims
- **API Call**: Simple GET request to `/api/customers/${id}/policies-claims/`
- **Fallback**: Falls back to mock data when API fails

### Existing Infrastructure
✅ **Already Available:**
- TypeScript types for policies and claims (`src/lib/types/customer.ts`)
- Workflow configuration types (`policy-claims-workflow.types.ts`)
- Workflow utilities (`policy-claims-workflow.utils.ts`)
- Component UI with filtering, sorting, and modals
- Error handling and loading states
- Polling service integration

❌ **Missing:**
- Implementation of the 4-step workflow process
- Data transformation from external API to internal types
- Proper error handling for each workflow step
- Integration with existing workflow utilities

## API Workflow Specification

### 4-Step Process (from policy-claims-workflow.json)

1. **Get Bearer Token** (`get_bearer_token`)
   - **Endpoint**: `https://uat.thirdpartyadmin.co.th:4443/TPA.TMS.Web.API_PREPROV2/api/GetToken`
   - **Method**: POST
   - **Purpose**: Authenticate with TPA system
   - **Extracts**: `bearer_token`

2. **Verify Citizen ID** (`verify_citizen_id`)
   - **Endpoint**: `https://uat.thirdpartyadmin.co.th:4443/TPA.TMS.Web.API_PREPROV2/api/SearchCitizenID`
   - **Method**: POST
   - **Purpose**: Validate customer's citizen ID
   - **Extracts**: `citizen_id`
   - **Validation**: `$.ListOfSearchCitizenID[0].Status == '1'`

3. **Fetch Policy List** (`fetch_policy_list`)
   - **Endpoint**: `https://uat.thirdpartyadmin.co.th:4443/TPA.TMS.Web.API_PREPROV2/api/PolicyListSocial`
   - **Method**: POST
   - **Purpose**: Get list of customer policies
   - **Extracts**: `policies`, `member_codes`

4. **Fetch Policy Details** (`fetch_policy_details`)
   - **Endpoint**: `https://uat.thirdpartyadmin.co.th:4443/TPA.TMS.Web.API_PREPROV2/api/PolicyDetailSocial`
   - **Method**: POST
   - **Purpose**: Get detailed policy and claims data
   - **Extracts**: `policy_details`, `claims_data`
   - **Iteration**: Over `member_codes` from step 3

### Configuration Parameters
- **Fixed Values**: `social_id`, `channel_id`, `citizen_id`
- **Retry Logic**: 2-3 retries per step
- **Timeout**: 10 minutes total
- **Retry Delay**: 5 seconds

## Implementation Strategy

### 1. Create Workflow Execution Service

**File**: `src/lib/api/features/customer/policy-workflow-executor.service.ts`

**Responsibilities:**
- Execute the 4-step workflow process
- Handle authentication and token management
- Implement retry logic with exponential backoff
- Extract and validate data from each step
- Transform external API responses to internal types

**Key Methods:**
```typescript
class PolicyWorkflowExecutor {
  async executeWorkflow(customerId: string): Promise<CustomerPoliciesData>
  private async executeStep(step: WorkflowStep, context: WorkflowExecutionContext): Promise<StepExecutionResult>
  private async getBearerToken(context: WorkflowExecutionContext): Promise<string>
  private async verifyCitizenId(bearerToken: string, context: WorkflowExecutionContext): Promise<string>
  private async fetchPolicyList(bearerToken: string, citizenId: string, context: WorkflowExecutionContext): Promise<any[]>
  private async fetchPolicyDetails(bearerToken: string, memberCode: string, context: WorkflowExecutionContext): Promise<any>
}
```

### 2. Create Data Transformation Service

**File**: `src/lib/api/features/customer/policy-data-transformer.service.ts`

**Responsibilities:**
- Transform TPA API responses to internal `Policy` and `Claim` types
- Calculate statistics and aggregations
- Handle data validation and sanitization
- Map external field names to internal schema

**Key Methods:**
```typescript
class PolicyDataTransformer {
  transformToCustomerPoliciesData(rawData: any, customerId: number): CustomerPoliciesData
  private transformPolicies(rawPolicies: any[]): Policy[]
  private transformClaims(rawClaims: any[]): Claim[]
  private calculateStatistics(policies: Policy[], claims: Claim[]): PolicyStatistics
  private mapPolicyFields(rawPolicy: any): Policy
  private mapClaimFields(rawClaim: any): Claim
}
```

### 3. Update Customer Service

**File**: `src/lib/api/features/customer/customers.service.ts`

**Changes:**
- Replace simple GET request with workflow execution
- Implement proper error handling and fallback
- Add caching mechanism for performance
- Integrate with existing service architecture

```typescript
async getCustomerPoliciesAndClaims(id: string, token: string): Promise<CustomerPoliciesResponse> {
  try {
    // Execute workflow
    const workflowExecutor = new PolicyWorkflowExecutor();
    const policiesData = await workflowExecutor.executeWorkflow(id);
    
    return {
      customer_policies: [policiesData],
      res_status: 200
    };
  } catch (error) {
    // Fallback to existing behavior or mock data
    console.warn('Workflow execution failed, using fallback:', error);
    throw error;
  }
}
```

## Data Transformation Mapping

### External API → Internal Types

**Policy Mapping:**
```typescript
// TPA API Response → Internal Policy Type
{
  PolNo: string → policy_number: string
  Name: string → product.name: string
  EffFrom: string → start_date: string
  EffTo: string → end_date: string
  MemberCode: string → Used for fetching details
  // Additional fields from PolicyDetailSocial response
}
```

**Claims Mapping:**
```typescript
// TPA API Response → Internal Claim Type
{
  // Fields from ListOfPolClaim
  ClaimNo: string → claim_number: string
  Status: string → claim_status: ClaimStatus
  Amount: number → claimed_amount: number
  // Additional transformation logic needed
}
```

### Statistics Calculation
- Count policies by status and type
- Calculate financial totals and averages
- Aggregate claims data
- Determine recent activity metrics

## Error Handling Strategy

### 1. Step-Level Error Handling
- **Retry Logic**: Implement exponential backoff for each step
- **Validation**: Validate responses at each step before proceeding
- **Timeout Handling**: Respect individual step timeouts
- **Partial Success**: Handle cases where some steps succeed but others fail

### 2. Workflow-Level Error Handling
- **Graceful Degradation**: Fall back to cached data or mock data
- **Error Logging**: Comprehensive logging for debugging
- **User Feedback**: Provide meaningful error messages to users
- **Recovery**: Implement recovery mechanisms for transient failures

### 3. Component-Level Error Handling
- **Loading States**: Show appropriate loading indicators
- **Error Display**: User-friendly error messages
- **Retry Options**: Allow users to retry failed operations
- **Fallback UI**: Maintain functionality with limited data

## Code Structure & Dependencies

### New Files to Create
1. `src/lib/api/features/customer/policy-workflow-executor.service.ts`
2. `src/lib/api/features/customer/policy-data-transformer.service.ts`
3. `src/lib/api/features/customer/policy-cache.service.ts` (optional)
4. `src/lib/api/features/customer/policy-workflow.types.ts` (additional types)

### Files to Modify
1. `src/lib/api/features/customer/customers.service.ts` - Update main service method
2. `src/lib/components/customer/tabs/PoliciesTab.svelte` - Enhanced error handling
3. `src/lib/api/features/index.ts` - Export new services if needed

### Dependencies
- **Existing**: All required dependencies are already available
- **HTTP Client**: Use existing fetch-based approach
- **Type Safety**: Leverage existing TypeScript types
- **Error Handling**: Use existing ApiError class

## Testing Strategy

### 1. Unit Tests
- **Workflow Executor**: Test each step execution
- **Data Transformer**: Test transformation logic
- **Error Handling**: Test retry and fallback mechanisms
- **Validation**: Test data validation and sanitization

### 2. Integration Tests
- **End-to-End Workflow**: Test complete workflow execution
- **API Mocking**: Mock external TPA API responses
- **Error Scenarios**: Test various failure conditions
- **Performance**: Test timeout and retry behavior

### 3. Component Tests
- **Loading States**: Test UI during API calls
- **Error Display**: Test error message display
- **Data Rendering**: Test with real API data structure
- **Fallback Behavior**: Test mock data fallback

### 4. Manual Testing
- **Real API Integration**: Test with actual TPA endpoints
- **Network Conditions**: Test under various network conditions
- **User Scenarios**: Test common user workflows
- **Edge Cases**: Test with unusual data scenarios

## Implementation Sequence

### Phase 1: Foundation (Week 1)
1. **Create Workflow Executor Service**
   - Implement basic workflow execution framework
   - Add step-by-step execution logic
   - Implement retry mechanisms

2. **Create Data Transformer Service**
   - Implement basic transformation logic
   - Map external API fields to internal types
   - Add data validation

3. **Unit Tests**
   - Test workflow executor components
   - Test data transformation logic
   - Test error handling mechanisms

### Phase 2: Integration (Week 2)
1. **Update Customer Service**
   - Integrate workflow executor
   - Implement fallback mechanisms
   - Add comprehensive error handling

2. **Component Updates**
   - Enhance error handling in PoliciesTab
   - Add better loading states
   - Improve user feedback

3. **Integration Tests**
   - Test end-to-end workflow
   - Test component integration
   - Test error scenarios

### Phase 3: Optimization (Week 3)
1. **Performance Optimization**
   - Implement caching mechanisms
   - Optimize API calls
   - Add request deduplication

2. **Enhanced Error Handling**
   - Improve error messages
   - Add recovery mechanisms
   - Implement partial data handling

3. **Testing & Validation**
   - Comprehensive testing with real API
   - Performance testing
   - User acceptance testing

### Phase 4: Deployment (Week 4)
1. **Configuration Management**
   - Environment-specific configurations
   - Feature flags for gradual rollout
   - Monitoring and logging setup

2. **Documentation**
   - API integration documentation
   - Troubleshooting guides
   - Maintenance procedures

3. **Deployment & Monitoring**
   - Gradual rollout
   - Performance monitoring
   - Error tracking and alerting

## Configuration & Environment Setup

### Environment Variables
```env
# TPA API Configuration
TPA_API_BASE_URL=https://uat.thirdpartyadmin.co.th:4443/TPA.TMS.Web.API_PREPROV2
TPA_API_USERNAME=BVTPA
TPA_API_PASSWORD=*d!n^+Cb@1

# Workflow Configuration
POLICY_WORKFLOW_TIMEOUT_MINUTES=10
POLICY_WORKFLOW_RETRY_DELAY_SECONDS=5
POLICY_WORKFLOW_MAX_RETRIES=3

# Feature Flags
ENABLE_REAL_POLICY_API=false
ENABLE_POLICY_CACHING=true
POLICY_CACHE_TTL_MINUTES=30
```

### Development Setup
1. **API Access**: Ensure access to TPA UAT environment
2. **Test Data**: Prepare test customer data with known policies
3. **Monitoring**: Set up logging and monitoring for API calls
4. **Fallback**: Ensure mock data is always available as fallback

## Risk Mitigation

### Technical Risks
- **API Reliability**: Implement robust retry and fallback mechanisms
- **Data Quality**: Add comprehensive validation and sanitization
- **Performance**: Implement caching and optimization strategies
- **Security**: Secure credential management and API communication

### Business Risks
- **User Experience**: Maintain functionality even when API fails
- **Data Accuracy**: Validate transformed data against business rules
- **Compliance**: Ensure data handling meets regulatory requirements
- **Scalability**: Design for future growth and additional data sources

## Success Criteria

### Functional Requirements
✅ **Complete Workflow Execution**: All 4 steps execute successfully
✅ **Data Transformation**: External API data correctly mapped to internal types
✅ **Error Handling**: Graceful handling of all error scenarios
✅ **Fallback Mechanism**: Seamless fallback to mock data when needed
✅ **UI Integration**: Component works seamlessly with real data

### Performance Requirements
✅ **Response Time**: API calls complete within acceptable timeframes
✅ **Reliability**: 99%+ success rate for API calls
✅ **Caching**: Effective caching reduces redundant API calls
✅ **Error Recovery**: Quick recovery from transient failures

### Quality Requirements
✅ **Test Coverage**: 90%+ test coverage for new code
✅ **Documentation**: Comprehensive documentation for maintenance
✅ **Monitoring**: Effective monitoring and alerting in place
✅ **Security**: Secure handling of credentials and data

## Next Steps

1. **Review and Approval**: Review this plan with stakeholders
2. **Resource Allocation**: Assign development resources
3. **Environment Setup**: Prepare development and testing environments
4. **Implementation Start**: Begin Phase 1 development
5. **Regular Reviews**: Weekly progress reviews and adjustments

---

**Document Version**: 1.0  
**Last Updated**: 2025-01-14  
**Author**: AI Assistant  
**Status**: Draft - Pending Review
