# PoliciesTab Reactive Workflow Implementation

## Overview

This document describes the implementation of the reactive workflow for refreshing policies data when `customer.customer_id` or `platformId` values change in the PoliciesTab component.

## Problem Statement

When users switch between different customers or select different platforms, the policies data needs to be refreshed to reflect the new selection. The system should:

1. Check cached policies data first for the new customer/platform combination
2. Load from cache if available, or fetch from API if not cached
3. Update all relevant reactive variables and UI state
4. Ensure efficient caching to minimize unnecessary API calls

## Implementation Details

### 1. Tracking Variables

Added tracking variables to monitor changes in customer and platform:

```typescript
// Track current customer ID and platform ID to reset flags when either changes
let currentCustomerId: number | null = null;
let currentPlatformId: number | null = null;
```

### 2. Enhanced Reactive Statements

#### Initial Data Loading
The existing reactive statement was enhanced to handle both customer and platform changes:

```typescript
// Reactive data loading when customer or platform changes
$: if (customer && customer.customer_id && access_token && !initialDataLoaded && !initialDataLoading) {
    console.log('Reactive data loading triggered for customer:', customer.customer_id, 'platform:', platformId);
    
    // Check if customer has changed
    const customerChanged = currentCustomerId !== null && currentCustomerId !== customer.customer_id;
    // Check if platform has changed
    const platformChanged = currentPlatformId !== platformId;
    
    // Reset flags if either customer or platform has changed
    if (customerChanged || platformChanged) {
        console.log('Customer or platform changed, resetting flags:', {
            customerChanged,
            platformChanged,
            oldCustomerId: currentCustomerId,
            newCustomerId: customer.customer_id,
            oldPlatformId: currentPlatformId,
            newPlatformId: platformId
        });
        initialDataLoaded = false;
        initialDataLoading = false;
    }
    
    // Update tracking variables
    currentCustomerId = customer.customer_id;
    currentPlatformId = platformId;
    initializeData();
}
```

#### Post-Load Data Refresh
Added a new reactive statement to handle changes after initial data has been loaded:

```typescript
// Reactive data refresh when customer or platform changes after initial load
$: if (customer && customer.customer_id && access_token && initialDataLoaded) {
    // Check if customer has changed
    const customerChanged = currentCustomerId !== null && currentCustomerId !== customer.customer_id;
    // Check if platform has changed  
    const platformChanged = currentPlatformId !== platformId;
    
    if (customerChanged || platformChanged) {
        console.log('Customer or platform changed after initial load, refreshing data:', {
            customerChanged,
            platformChanged,
            oldCustomerId: currentCustomerId,
            newCustomerId: customer.customer_id,
            oldPlatformId: currentPlatformId,
            newPlatformId: platformId
        });
        
        // Update tracking variables
        currentCustomerId = customer.customer_id;
        currentPlatformId = platformId;
        
        // Refresh data with cache-first approach
        loadPoliciesData();
    }
}
```

### 3. Cache-First Workflow

The existing `loadPoliciesData()` function already implements the cache-first approach:

1. **Check Cache**: Uses `policiesCacheService.get(customer.customer_id, platformId)` to check for cached data
2. **Load from Cache**: If cached data exists and is valid, loads immediately
3. **Fetch from API**: If no cached data, fetches from API
4. **Update Cache**: Stores successful API responses in cache using `policiesCacheService.set()`
5. **Update UI State**: Updates all reactive variables (`policiesData`, `workflowStatus`, `workflowStep`, etc.)

### 4. Cache Service Integration

The implementation leverages the existing `PoliciesCacheService` which provides:

- **Platform-specific caching**: Each customer/platform combination has its own cache key
- **Automatic expiration**: 1-hour cache duration with automatic cleanup
- **Cache statistics**: Hit rate tracking and performance monitoring
- **Force refresh**: Ability to bypass cache when needed

## Workflow Sequence

### When Customer/Platform Changes:

1. **Detection**: Reactive statements detect changes in `customer.customer_id` or `platformId`
2. **Cache Check**: `policiesCacheService.get(customerId, platformId)` checks for existing cached data
3. **Cache Hit**: If cached data exists and is valid:
   - Load data immediately from cache
   - Update `policiesData` and filtered arrays
   - Set `workflowStatus` to 'success'
   - Update UI instantly
4. **Cache Miss**: If no cached data exists:
   - Execute API workflow to fetch fresh data
   - Update `workflowStatus` to 'executing' with progress steps
   - Cache successful response for future use
   - Update UI with new data
5. **State Management**: Update all reactive variables and UI components

## Benefits

### Performance
- **Instant Loading**: Cached data loads immediately without API delays
- **Reduced API Calls**: Minimizes unnecessary requests to backend services
- **Efficient Memory Usage**: Automatic cache cleanup prevents memory leaks

### User Experience
- **Seamless Transitions**: Smooth switching between customers and platforms
- **Visual Feedback**: Clear workflow status indicators during data loading
- **Consistent State**: All UI components stay synchronized with current selection

### Maintainability
- **Centralized Logic**: All caching logic contained in dedicated service
- **Clear Separation**: Reactive statements handle detection, service handles caching
- **Comprehensive Logging**: Detailed console output for debugging and monitoring

## Testing

A comprehensive test suite (`policies-tab-reactive-workflow.test.ts`) validates:

1. **Customer Changes**: Data refresh when switching between customers
2. **Platform Changes**: Data refresh when platform selection changes
3. **Cache Efficiency**: Verification that cache is used for previously loaded data
4. **API Call Optimization**: Tracking of API calls to ensure minimal requests
5. **UI State Updates**: Verification that UI reflects current customer/platform data

## Future Enhancements

1. **Preemptive Caching**: Cache data for likely next selections
2. **Background Refresh**: Periodic cache updates for active customers
3. **Cache Persistence**: Store cache data across browser sessions
4. **Advanced Analytics**: Detailed cache performance metrics and optimization
