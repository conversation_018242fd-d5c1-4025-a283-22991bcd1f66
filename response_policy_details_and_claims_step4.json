{"ListOfPolDet": [{"MainBenefit": "HB", "MainBenefitEN": "HB", "Coverage": [{"CovNo": "1", "CovNoEN": "1", "CovDesc": "ชดเชย-กรณีเจ็บป่วยด้วยโรค", "CovDescEN": "ชดเชย-กรณีเจ็บป่วยด้วยโรค", "CovLimit": "1,000", "CovLimitEN": "1,000 ", "CovUtilized": "-", "CovUtilizedEN": "-"}, {"CovNo": "2", "CovNoEN": "2", "CovDesc": "ชดเชย-กรณีอุบัติเหตุทั่วไป (ไม่รวมมอเตอร์ไซด์)", "CovDescEN": "ชดเชย-กรณีอุบัติเหตุทั่วไป (ไม่รวมมอเตอร์ไซด์)", "CovLimit": "1,000", "CovLimitEN": "1,000 ", "CovUtilized": "-", "CovUtilizedEN": "-"}, {"CovNo": "3", "CovNoEN": "3", "CovDesc": "ชดเชย-กรณีอุบัติเหตุมอเตอร์ไซด์", "CovDescEN": "ชดเชย-กรณีอุบัติเหตุมอเตอร์ไซด์", "CovLimit": "1,000", "CovLimitEN": "1,000 ", "CovUtilized": "-", "CovUtilizedEN": "-"}, {"CovNo": "หมายเหตุ", "CovNoEN": "Remarks", "CovDesc": "1. (3) รวมกันไม่เกิน 10 วัน/ครั้ง", "CovDescEN": "1. Max limit (3) = 10 day(s) /disability", "CovLimit": "", "CovLimitEN": " ", "CovUtilized": "", "CovUtilizedEN": ""}, {"CovNo": "", "CovNoEN": "", "CovDesc": "2. (2) รวมกันไม่เกิน 10 วัน/ครั้ง", "CovDescEN": "2. Max limit (2) = 10 day(s) /disability", "CovLimit": "", "CovLimitEN": " ", "CovUtilized": "", "CovUtilizedEN": ""}, {"CovNo": "", "CovNoEN": "", "CovDesc": "3. (1) รวมกันไม่เกิน 10 วัน/ครั้ง", "CovDescEN": "3. Max limit (1) = 10 day(s) /disability", "CovLimit": "", "CovLimitEN": " ", "CovUtilized": "", "CovUtilizedEN": ""}]}, {"MainBenefit": "Outpatient Benefits", "MainBenefitEN": "Outpatient Benefits", "Coverage": [{"CovNo": "1", "CovNoEN": "1", "CovDesc": "ผู้ป่วยนอก", "CovDescEN": "ผู้ป่วยนอก", "CovLimit": "2,000", "CovLimitEN": "2,000 ", "CovUtilized": "4,499.50 (3 ครั้ง)", "CovUtilizedEN": "4,499.50 (3 visit)"}, {"CovNo": "หมายเหตุ", "CovNoEN": "Remarks", "CovDesc": "1. (1) สูงสุดไม่เกิน 14,000 บาท/ปี", "CovDescEN": "1. Max limit (1) = 14,000 baht /year", "CovLimit": "", "CovLimitEN": " ", "CovUtilized": "", "CovUtilizedEN": ""}, {"CovNo": "", "CovNoEN": "", "CovDesc": "2. (1) สูงสุดไม่เกิน 10 ครั้ง/ปี", "CovDescEN": "2. Max limit (1) = 10 visit(s)/year", "CovLimit": "", "CovLimitEN": " ", "CovUtilized": "", "CovUtilizedEN": ""}]}, {"MainBenefit": "Inpatient Benefits", "MainBenefitEN": "Inpatient Benefits", "Coverage": [{"CovNo": "1", "CovNoEN": "1", "CovDesc": "ค่าห้องผู้ป่วยปกติ และค่าอาหาร", "CovDescEN": "ค่าห้องผู้ป่วยปกติ และค่าอาหาร", "CovLimit": "5,500", "CovLimitEN": "5,500 ", "CovUtilized": "1 วัน", "CovUtilizedEN": "1 day(s)"}, {"CovNo": "2", "CovNoEN": "2", "CovDesc": "ค่าห้องผู้ป่วยหนัก และค่าอาหาร", "CovDescEN": "ค่าห้องผู้ป่วยหนัก และค่าอาหาร", "CovLimit": "11,000", "CovLimitEN": "11,000 ", "CovUtilized": "-", "CovUtilizedEN": "-"}, {"CovNo": "3", "CovNoEN": "3", "CovDesc": "ค่ารักษาพยาบาลทั่วไป", "CovDescEN": "ค่ารักษาพยาบาลทั่วไป", "CovLimit": "600,000", "CovLimitEN": "600,000 ", "CovUtilized": "-", "CovUtilizedEN": "-"}, {"CovNo": "4", "CovNoEN": "4", "CovDesc": "ค่ารถพยาบาล", "CovDescEN": "ค่ารถพยาบาล", "CovLimit": "600,000", "CovLimitEN": "600,000 ", "CovUtilized": "-", "CovUtilizedEN": "-"}, {"CovNo": "5", "CovNoEN": "5", "CovDesc": "ค่าแพทย์ที่ปรึกษา กรณีไม่ผ่าตัด", "CovDescEN": "ค่าแพทย์ที่ปรึกษา กรณีไม่ผ่าตัด", "CovLimit": "600,000", "CovLimitEN": "600,000 ", "CovUtilized": "-", "CovUtilizedEN": "-"}, {"CovNo": "6", "CovNoEN": "6", "CovDesc": "ยากลับบ้าน", "CovDescEN": "ยากลับบ้าน", "CovLimit": "2,000", "CovLimitEN": "2,000 ", "CovUtilized": "-", "CovUtilizedEN": "-"}, {"CovNo": "7", "CovNoEN": "7", "CovDesc": "ค่าแพทย์ผ่าตัดและหัตถการ", "CovDescEN": "ค่าแพทย์ผ่าตัดและหัตถการ", "CovLimit": "600,000", "CovLimitEN": "600,000 ", "CovUtilized": "-", "CovUtilizedEN": "-"}, {"CovNo": "8", "CovNoEN": "8", "CovDesc": "ค่าแพทย์ที่ปรึกษา กรณีผ่าตัด", "CovDescEN": "ค่าแพทย์ที่ปรึกษา กรณีผ่าตัด", "CovLimit": "600,000", "CovLimitEN": "600,000 ", "CovUtilized": "-", "CovUtilizedEN": "-"}, {"CovNo": "9", "CovNoEN": "9", "CovDesc": "ค่าแพทย์เยี่ยมไข้", "CovDescEN": "ค่าแพทย์เยี่ยมไข้", "CovLimit": "1,225,000", "CovLimitEN": "1,225,000 ", "CovUtilized": "-", "CovUtilizedEN": "-"}, {"CovNo": "หมายเหตุ", "CovNoEN": "Remarks", "CovDesc": "1. (3)+(4)+(5)+(6) สูงสุดไม่เกิน 600,000 บาท/ครั้ง", "CovDescEN": "1. Max limit (3)+(4)+(5)+(6) = 600,000 baht /disability", "CovLimit": "", "CovLimitEN": " ", "CovUtilized": "", "CovUtilizedEN": ""}, {"CovNo": "", "CovNoEN": "", "CovDesc": "2. (1)+(2) สูงสุดไม่เกิน 90 วัน/ครั้ง", "CovDescEN": "2. Max limit (1)+(2) = 90 day(s) /disability", "CovLimit": "", "CovLimitEN": " ", "CovUtilized": "", "CovUtilizedEN": ""}, {"CovNo": "", "CovNoEN": "", "CovDesc": "3. (7)+(8) สูงสุดไม่เกิน 600,000 บาท/ครั้ง", "CovDescEN": "3. Max limit (7)+(8) = 600,000 baht /disability", "CovLimit": "", "CovLimitEN": " ", "CovUtilized": "", "CovUtilizedEN": ""}, {"CovNo": "", "CovNoEN": "", "CovDesc": "4. ผลประโยชน์ IPD สูงสุดไม่เกิน 1,225,000 บาท/ปี", "CovDescEN": "4. Max limit IPD = 1,225,000 baht/year", "CovLimit": "", "CovLimitEN": " ", "CovUtilized": "", "CovUtilizedEN": ""}, {"CovNo": "", "CovNoEN": "", "CovDesc": "5. (9) รวมกันไม่เกิน 90 วัน/ครั้ง", "CovDescEN": "5. Max limit (9) = 90 day(s) /disability", "CovLimit": "", "CovLimitEN": " ", "CovUtilized": "", "CovUtilizedEN": ""}, {"CovNo": "", "CovNoEN": "", "CovDesc": "6. (2) รวมกันไม่เกิน 15 วัน/ครั้ง", "CovDescEN": "6. Max limit (2) = 15 day(s) /disability", "CovLimit": "", "CovLimitEN": " ", "CovUtilized": "", "CovUtilizedEN": ""}]}, {"MainBenefit": "PA", "MainBenefitEN": "PA", "Coverage": [{"CovNo": "1", "CovNoEN": "1", "CovDesc": "ค่ารักษาพยาบาลจากอุบัติเหตุทั่วไป (ไม่รวมมอเตอร์ไซด์)", "CovDescEN": "ค่ารักษาพยาบาลจากอุบัติเหตุทั่วไป (ไม่รวมมอเตอร์ไซด์)", "CovLimit": "20,000", "CovLimitEN": "20,000 ", "CovUtilized": "-", "CovUtilizedEN": "-"}, {"CovNo": "2", "CovNoEN": "2", "CovDesc": "ค่ารักษาพยาบาลจากอุบัติเหตุมอเตอร์ไซด์", "CovDescEN": "ค่ารักษาพยาบาลจากอุบัติเหตุมอเตอร์ไซด์", "CovLimit": "20,000", "CovLimitEN": "20,000 ", "CovUtilized": "-", "CovUtilizedEN": "-"}]}], "ListOfPolClaim": [{"ClmInsurerCode": "INS201900003", "ClmInsurerTH": "DEMO ASSURANCE CO.,LTD.", "ClmInsurerEN": "DEMO ASSURANCE CO.,LTD.", "ClmCompanyCode": "COM202400111", "ClmCompanyTH": "บริษัท DEMO", "ClmCompanyEN": "บริษัท DEMO", "ClmCardType": "Group Insurance", "ClmPolNo": "T001", "ClmNo": "C20250006464", "ClmSource": "Credit", "ClmType": "Illness", "ClmDiagCode": "K35", "ClmDiagTH": "ไส้ติ่งอักเสบเฉียบพลัน", "ClmDiagEN": "Acute appendicitis", "ClmStatus": "Approved", "ClmStatusTxt": "อนุมัติ", "ClmStatusTxtEN": "Approved", "ClmVisitDate": "01/08/2025", "ClmDischargeDate": "02/08/2025", "ClmIncurredAmt": "61000", "ClmPayable": "61000", "ClmPaymentDate": "", "ClmProviderCode": "PVR201601011", "ClmProviderTH": "จุฬารัตน์ 3", "ClmProviderEN": "CHULARAT 3 HOSPITAL"}, {"ClmInsurerCode": "INS201900003", "ClmInsurerTH": "DEMO ASSURANCE CO.,LTD.", "ClmInsurerEN": "DEMO ASSURANCE CO.,LTD.", "ClmCompanyCode": "COM202400111", "ClmCompanyTH": "บริษัท DEMO", "ClmCompanyEN": "บริษัท DEMO", "ClmCardType": "Group Insurance", "ClmPolNo": "T001", "ClmNo": "C20250001156", "ClmSource": "Credit", "ClmType": "Illness", "ClmDiagCode": "R11", "ClmDiagTH": "คลื่นไส้ และ อาเจียน", "ClmDiagEN": "Nausea and vomiting                                                                               ", "ClmStatus": "Open", "ClmStatusTxt": "อยู่ระหว่างดำเนินการ", "ClmStatusTxtEN": "In Progress", "ClmVisitDate": "02/04/2025", "ClmDischargeDate": "04/04/2025", "ClmIncurredAmt": "15900.50", "ClmPayable": "15900.50", "ClmPaymentDate": "", "ClmProviderCode": "PVR201600944", "ClmProviderTH": "พญาไท 2 BDMS", "ClmProviderEN": "PHYATHAI 2 HOSPITAL"}, {"ClmInsurerCode": "INS201900003", "ClmInsurerTH": "DEMO ASSURANCE CO.,LTD.", "ClmInsurerEN": "DEMO ASSURANCE CO.,LTD.", "ClmCompanyCode": "COM202400111", "ClmCompanyTH": "บริษัท DEMO", "ClmCompanyEN": "บริษัท DEMO", "ClmCardType": "Group Insurance", "ClmPolNo": "T001", "ClmNo": "C20250001152", "ClmSource": "Credit", "ClmType": "Illness", "ClmDiagCode": "", "ClmDiagTH": "", "ClmDiagEN": "", "ClmStatus": "Open", "ClmStatusTxt": "อยู่ระหว่างดำเนินการ", "ClmStatusTxtEN": "In Progress", "ClmVisitDate": "31/03/2025", "ClmDischargeDate": "01/04/2025", "ClmIncurredAmt": "999", "ClmPayable": "999", "ClmPaymentDate": "", "ClmProviderCode": "PVR201600915", "ClmProviderTH": "เกษมราษฎร์บางแค", "ClmProviderEN": "KASEMRAD BANGKAE HOSPITAL"}, {"ClmInsurerCode": "INS201900003", "ClmInsurerTH": "DEMO ASSURANCE CO.,LTD.", "ClmInsurerEN": "DEMO ASSURANCE CO.,LTD.", "ClmCompanyCode": "COM202400111", "ClmCompanyTH": "บริษัท DEMO", "ClmCompanyEN": "บริษัท DEMO", "ClmCardType": "Group Insurance", "ClmPolNo": "T001", "ClmNo": "C20250001123", "ClmSource": "Credit", "ClmType": "Illness", "ClmDiagCode": "R53", "ClmDiagTH": "อ่อนเพลียและอ่อนล้า", "ClmDiagEN": "Malaise and fatigue                                                                                ", "ClmStatus": "Authorized", "ClmStatusTxt": "อยู่ระหว่างดำเนินการ", "ClmStatusTxtEN": "In Progress", "ClmVisitDate": "30/03/2025", "ClmDischargeDate": "30/03/2025", "ClmIncurredAmt": "0", "ClmPayable": "0", "ClmPaymentDate": "", "ClmProviderCode": "PVR201800065", "ClmProviderTH": "พริ้นซ์ สุวรรณภูมิ", "ClmProviderEN": "PRINC HOSPITAL"}, {"ClmInsurerCode": "INS201900003", "ClmInsurerTH": "DEMO ASSURANCE CO.,LTD.", "ClmInsurerEN": "DEMO ASSURANCE CO.,LTD.", "ClmCompanyCode": "COM202400111", "ClmCompanyTH": "บริษัท DEMO", "ClmCompanyEN": "บริษัท DEMO", "ClmCardType": "Group Insurance", "ClmPolNo": "T001", "ClmNo": "C20250001122", "ClmSource": "Credit", "ClmType": "Illness", "ClmDiagCode": "J10", "ClmDiagTH": "ไข้หวัดใหญ่ที่ตรวจพบเชื้อไวรัสไข้หวัดใหญ่", "ClmDiagEN": "Influenza due to identified influenza virus", "ClmStatus": "Approved", "ClmStatusTxt": "อนุมัติ", "ClmStatusTxtEN": "Approved", "ClmVisitDate": "28/03/2025", "ClmDischargeDate": "28/03/2025", "ClmIncurredAmt": "10000000", "ClmPayable": "2000", "ClmPaymentDate": "", "ClmProviderCode": "PVR201601067", "ClmProviderTH": "วิชัยเวชอินเตอร์เนชั่นแนลสมุทรสาคร", "ClmProviderEN": "VICHAIVEJ INTERNATIONAL HOSPITAL SAMUTSAKHON"}, {"ClmInsurerCode": "INS201900003", "ClmInsurerTH": "DEMO ASSURANCE CO.,LTD.", "ClmInsurerEN": "DEMO ASSURANCE CO.,LTD.", "ClmCompanyCode": "COM202400111", "ClmCompanyTH": "บริษัท DEMO", "ClmCompanyEN": "บริษัท DEMO", "ClmCardType": "Group Insurance", "ClmPolNo": "T001", "ClmNo": "C20250001121", "ClmSource": "Credit", "ClmType": "Illness", "ClmDiagCode": "R11", "ClmDiagTH": "คลื่นไส้ และ อาเจียน", "ClmDiagEN": "Nausea and vomiting                                                                               ", "ClmStatus": "Pending", "ClmStatusTxt": "อยู่ระหว่างดำเนินการ", "ClmStatusTxtEN": "In Progress", "ClmVisitDate": "26/03/2025", "ClmDischargeDate": "26/03/2025", "ClmIncurredAmt": "0", "ClmPayable": "0", "ClmPaymentDate": "", "ClmProviderCode": "PVR201600916", "ClmProviderTH": "เกษมราษฎร์ประชาชื่น", "ClmProviderEN": "KASEMRAD PRACHACHUEN HOSPITAL"}, {"ClmInsurerCode": "INS201900003", "ClmInsurerTH": "DEMO ASSURANCE CO.,LTD.", "ClmInsurerEN": "DEMO ASSURANCE CO.,LTD.", "ClmCompanyCode": "COM202400111", "ClmCompanyTH": "บริษัท DEMO", "ClmCompanyEN": "บริษัท DEMO", "ClmCardType": "Group Insurance", "ClmPolNo": "T001", "ClmNo": "C20250000955", "ClmSource": "Reimbursement", "ClmType": "Illness", "ClmDiagCode": "R10.0", "ClmDiagTH": "ปวดท้องเฉียบพลัน", "ClmDiagEN": "Acute abdomen                                                                                         ", "ClmStatus": "Approved", "ClmStatusTxt": "อนุมัติ", "ClmStatusTxtEN": "Approved", "ClmVisitDate": "25/03/2025", "ClmDischargeDate": "25/03/2025", "ClmIncurredAmt": "1999.50", "ClmPayable": "1999.50", "ClmPaymentDate": "", "ClmProviderCode": "PVR201600981", "ClmProviderTH": "นนทเวช", "ClmProviderEN": "NONTHAVEJ HOSPITAL"}, {"ClmInsurerCode": "INS201900003", "ClmInsurerTH": "DEMO ASSURANCE CO.,LTD.", "ClmInsurerEN": "DEMO ASSURANCE CO.,LTD.", "ClmCompanyCode": "COM202400111", "ClmCompanyTH": "บริษัท DEMO", "ClmCompanyEN": "บริษัท DEMO", "ClmCardType": "Group Insurance", "ClmPolNo": "T001", "ClmNo": "C20250000958", "ClmSource": "Reimbursement", "ClmType": "Illness", "ClmDiagCode": "R53", "ClmDiagTH": "อ่อนเพลียและอ่อนล้า", "ClmDiagEN": "Malaise and fatigue                                                                                ", "ClmStatus": "Rejected", "ClmStatusTxt": "ปฏิเสธ", "ClmStatusTxtEN": "Rejected", "ClmVisitDate": "24/03/2025", "ClmDischargeDate": "24/03/2025", "ClmIncurredAmt": "30000", "ClmPayable": "0", "ClmPaymentDate": "", "ClmProviderCode": "PVR202000022", "ClmProviderTH": "สินแพทย์ ศรีนครินทร์", "ClmProviderEN": "Sinphat Srinakarin  Hospital"}, {"ClmInsurerCode": "INS201900003", "ClmInsurerTH": "DEMO ASSURANCE CO.,LTD.", "ClmInsurerEN": "DEMO ASSURANCE CO.,LTD.", "ClmCompanyCode": "COM202400111", "ClmCompanyTH": "บริษัท DEMO", "ClmCompanyEN": "บริษัท DEMO", "ClmCardType": "Group Insurance", "ClmPolNo": "T001", "ClmNo": "C20250001158", "ClmSource": "Credit", "ClmType": "Illness", "ClmDiagCode": "J10", "ClmDiagTH": "ไข้หวัดใหญ่ที่ตรวจพบเชื้อไวรัสไข้หวัดใหญ่", "ClmDiagEN": "Influenza due to identified influenza virus", "ClmStatus": "Rejected", "ClmStatusTxt": "ปฏิเสธ", "ClmStatusTxtEN": "Rejected", "ClmVisitDate": "23/03/2025", "ClmDischargeDate": "24/03/2025", "ClmIncurredAmt": "55000", "ClmPayable": "0", "ClmPaymentDate": "", "ClmProviderCode": "PVR201600964", "ClmProviderTH": "สมิติเวชศรีนครินทร์ BDMS", "ClmProviderEN": "SAMITIVEJ SRINAKARIN HOSPITAL"}, {"ClmInsurerCode": "INS201900003", "ClmInsurerTH": "DEMO ASSURANCE CO.,LTD.", "ClmInsurerEN": "DEMO ASSURANCE CO.,LTD.", "ClmCompanyCode": "COM202400111", "ClmCompanyTH": "บริษัท DEMO", "ClmCompanyEN": "บริษัท DEMO", "ClmCardType": "Group Insurance", "ClmPolNo": "T001", "ClmNo": "C20250001055", "ClmSource": "Credit", "ClmType": "Illness", "ClmDiagCode": "", "ClmDiagTH": "", "ClmDiagEN": "", "ClmStatus": "Open", "ClmStatusTxt": "อยู่ระหว่างดำเนินการ", "ClmStatusTxtEN": "In Progress", "ClmVisitDate": "22/03/2025", "ClmDischargeDate": "23/03/2025", "ClmIncurredAmt": "0", "ClmPayable": "0", "ClmPaymentDate": "", "ClmProviderCode": "PVR201600981", "ClmProviderTH": "นนทเวช", "ClmProviderEN": "NONTHAVEJ HOSPITAL"}, {"ClmInsurerCode": "INS201900003", "ClmInsurerTH": "DEMO ASSURANCE CO.,LTD.", "ClmInsurerEN": "DEMO ASSURANCE CO.,LTD.", "ClmCompanyCode": "COM202400111", "ClmCompanyTH": "บริษัท DEMO", "ClmCompanyEN": "บริษัท DEMO", "ClmCardType": "Group Insurance", "ClmPolNo": "T001", "ClmNo": "C20250001124", "ClmSource": "Reimbursement", "ClmType": "Illness", "ClmDiagCode": "", "ClmDiagTH": "", "ClmDiagEN": "", "ClmStatus": "Open", "ClmStatusTxt": "อยู่ระหว่างดำเนินการ", "ClmStatusTxtEN": "In Progress", "ClmVisitDate": "15/03/2025", "ClmDischargeDate": "15/03/2025", "ClmIncurredAmt": "0", "ClmPayable": "0", "ClmPaymentDate": "", "ClmProviderCode": "PVR201600931", "ClmProviderTH": "นวมินทร์", "ClmProviderEN": "NAVAMINTHRA HOSPITAL"}, {"ClmInsurerCode": "INS201900003", "ClmInsurerTH": "DEMO ASSURANCE CO.,LTD.", "ClmInsurerEN": "DEMO ASSURANCE CO.,LTD.", "ClmCompanyCode": "COM202400111", "ClmCompanyTH": "บริษัท DEMO", "ClmCompanyEN": "บริษัท DEMO", "ClmCardType": "Group Insurance", "ClmPolNo": "T001", "ClmNo": "C20250000953", "ClmSource": "Reimbursement", "ClmType": "Illness", "ClmDiagCode": "J10", "ClmDiagTH": "ไข้หวัดใหญ่ที่ตรวจพบเชื้อไวรัสไข้หวัดใหญ่", "ClmDiagEN": "Influenza due to identified influenza virus", "ClmStatus": "Open", "ClmStatusTxt": "อยู่ระหว่างดำเนินการ", "ClmStatusTxtEN": "In Progress", "ClmVisitDate": "01/03/2025", "ClmDischargeDate": "01/03/2025", "ClmIncurredAmt": "2000", "ClmPayable": "2000", "ClmPaymentDate": "", "ClmProviderCode": "PVR201600956", "ClmProviderTH": "วิชัยยุทธ", "ClmProviderEN": "VICHAIYUT HOSPITAL"}, {"ClmInsurerCode": "INS201900003", "ClmInsurerTH": "DEMO ASSURANCE CO.,LTD.", "ClmInsurerEN": "DEMO ASSURANCE CO.,LTD.", "ClmCompanyCode": "COM202400111", "ClmCompanyTH": "บริษัท DEMO", "ClmCompanyEN": "บริษัท DEMO", "ClmCardType": "Group Insurance", "ClmPolNo": "T001", "ClmNo": "C20250000951", "ClmSource": "Reimbursement", "ClmType": "Illness", "ClmDiagCode": "A04", "ClmDiagTH": "ลำไส้ติดเชื้อแบครีเรียชนิดอื่น", "ClmDiagEN": "Other bacterial intestinal infections                                                   ", "ClmStatus": "Pending", "ClmStatusTxt": "อยู่ระหว่างดำเนินการ", "ClmStatusTxtEN": "In Progress", "ClmVisitDate": "26/02/2025", "ClmDischargeDate": "27/02/2025", "ClmIncurredAmt": "1000", "ClmPayable": "1000", "ClmPaymentDate": "", "ClmProviderCode": "PVR201600952", "ClmProviderTH": "เปาโลเกษตร BDMS", "ClmProviderEN": "PAOLOKASET HOSPITAL"}, {"ClmInsurerCode": "INS201900003", "ClmInsurerTH": "DEMO ASSURANCE CO.,LTD.", "ClmInsurerEN": "DEMO ASSURANCE CO.,LTD.", "ClmCompanyCode": "COM202400111", "ClmCompanyTH": "บริษัท DEMO", "ClmCompanyEN": "บริษัท DEMO", "ClmCardType": "Group Insurance", "ClmPolNo": "T001", "ClmNo": "C20250000950", "ClmSource": "Reimbursement", "ClmType": "Illness", "ClmDiagCode": "R53", "ClmDiagTH": "อ่อนเพลียและอ่อนล้า", "ClmDiagEN": "Malaise and fatigue                                                                                ", "ClmStatus": "Approved", "ClmStatusTxt": "อนุมัติ", "ClmStatusTxtEN": "Approved", "ClmVisitDate": "25/02/2025", "ClmDischargeDate": "25/02/2025", "ClmIncurredAmt": "500", "ClmPayable": "500", "ClmPaymentDate": "", "ClmProviderCode": "PVR201600959", "ClmProviderTH": "เวชธานี", "ClmProviderEN": "VEJTHANI HOSPITAL"}], "ErrorMessage": " "}