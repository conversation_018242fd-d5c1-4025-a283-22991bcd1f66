{"ListOfPolicyListSocial": [{"Name": "ทดสอบ2", "NameEN": "ทดสอบ2", "Surname": "<PERSON><PERSON><PERSON>", "SurnameEN": "<PERSON><PERSON><PERSON>", "CitizenID": "2019086318637", "CardType": "Group Insurance", "InsurerCode": "INS202200006", "InsurerName": "บริษัท บลูเวนเจอร์ ทีพีเอ จำกัด", "InsurerNameEN": "บริษัท บลูเวนเจอร์ ทีพีเอ จำกัด", "InsurerDedicateedLine": " ", "CompanyCode": "COM202200080", "CompanyName": "บริษัท บลูเวนเจอร์ ทีพีเอ จำกัด", "CompanyNameEN": "บริษัท บลูเวนเจอร์ ทีพีเอ จำกัด", "StaffNo": " ", "EffFrom": "15/02/2024", "EffTo": "20/12/2025", "PolNo": "BVTPA_2024", "PlanCode": "PLAN 3", "PlanName": "แผน 3", "TelNo": " ", "Email": "", "MemberCode": "*********-2 Chatbot4", "EvenMBAccident": "Inpatient Benefits|Outpatient Benefits|PA", "EvenMBAccidentTH": "Inpatient Benefits|Outpatient Benefits|PA", "EvenMBAccidentEN": "Inpatient Benefits|Outpatient Benefits|PA", "EvenMBIllness": "Dental|Flexible Benefits|Inpatient Benefits|Outpatient Benefits", "EvenMBIllnessTH": "Dental|Flexible Benefits|Inpatient Benefits|Outpatient Benefits", "EvenMBIllnessEN": "Dental|Flexible Benefits|Inpatient Benefits|Outpatient Benefits", "EvenMBOtherTH": "", "EvenMBOtherEN": "", "BirthDate": "", "Gender": "Unknown", "Age": "0", "Passport": "", "CountryCode": "", "CertificateNo": "BVTPA_2024"}, {"Name": "ทดสอบ2", "NameEN": "ทดสอบ2", "Surname": "<PERSON><PERSON><PERSON>", "SurnameEN": "<PERSON><PERSON><PERSON>", "CitizenID": "2019086318637", "CardType": "Group Insurance", "InsurerCode": "INS202300003", "InsurerName": "บริษัท เอบีซีประกันภัย จำกัด", "InsurerNameEN": "ABC Insurance Company Limited", "InsurerDedicateedLine": " ", "CompanyCode": "COM202300019", "CompanyName": "บริษัท เอบีซีประกันภัย จำกัด", "CompanyNameEN": "บริษัท เอบีซีประกันภัย จำกัด", "StaffNo": " ", "EffFrom": "01/01/2025", "EffTo": "31/12/2025", "PolNo": "T003", "PlanCode": "PLAN 3", "PlanName": "แผน 3", "TelNo": " ", "Email": "", "MemberCode": "*********-2 Chatbot3", "EvenMBAccident": "Outpatient Benefits|PA", "EvenMBAccidentTH": "Outpatient Benefits|PA", "EvenMBAccidentEN": "Outpatient Benefits|PA", "EvenMBIllness": "Flexible Benefits|Outpatient Benefits", "EvenMBIllnessTH": "Flexible Benefits|Outpatient Benefits", "EvenMBIllnessEN": "Flexible Benefits|Outpatient Benefits", "EvenMBOtherTH": "", "EvenMBOtherEN": "", "BirthDate": "", "Gender": "Unknown", "Age": "0", "Passport": "", "CountryCode": "", "CertificateNo": "T003"}, {"Name": "ทดสอบ2", "NameEN": "ทดสอบ2", "Surname": "<PERSON><PERSON><PERSON>", "SurnameEN": "<PERSON><PERSON><PERSON>", "CitizenID": "2019086318637", "CardType": "Group Insurance", "InsurerCode": "INS201900003", "InsurerName": "DEMO ASSURANCE CO.,LTD.", "InsurerNameEN": "DEMO ASSURANCE CO.,LTD.", "InsurerDedicateedLine": "*********", "CompanyCode": "COM202400111", "CompanyName": "บริษัท DEMO", "CompanyNameEN": "บริษัท DEMO", "StaffNo": " ", "EffFrom": "01/01/2025", "EffTo": "31/12/2025", "PolNo": "T001", "PlanCode": "PLAN 1", "PlanName": "แผน 1", "TelNo": " ", "Email": "", "MemberCode": "*********-2 Chatbot1", "EvenMBAccident": "HB|Inpatient Benefits|Outpatient Benefits|PA", "EvenMBAccidentTH": "HB|Inpatient Benefits|Outpatient Benefits|PA", "EvenMBAccidentEN": "HB|Inpatient Benefits|Outpatient Benefits|PA", "EvenMBIllness": "HB|Inpatient Benefits|Outpatient Benefits", "EvenMBIllnessTH": "HB|Inpatient Benefits|Outpatient Benefits", "EvenMBIllnessEN": "HB|Inpatient Benefits|Outpatient Benefits", "EvenMBOtherTH": "", "EvenMBOtherEN": "", "BirthDate": "19/08/1997", "Gender": "Unknown", "Age": "28", "Passport": "", "CountryCode": "", "CertificateNo": "T001"}, {"Name": "ทดสอบ2", "NameEN": "ทดสอบ2", "Surname": "<PERSON><PERSON><PERSON>", "SurnameEN": "<PERSON><PERSON><PERSON>", "CitizenID": "2019086318637", "CardType": "Group Insurance", "InsurerCode": "INS201900003", "InsurerName": "DEMO ASSURANCE CO.,LTD.", "InsurerNameEN": "DEMO ASSURANCE CO.,LTD.", "InsurerDedicateedLine": "*********", "CompanyCode": "COM202400111", "CompanyName": "บริษัท DEMO", "CompanyNameEN": "บริษัท DEMO", "StaffNo": " ", "EffFrom": "01/01/2025", "EffTo": "25/12/2025", "PolNo": "T002", "PlanCode": "PLAN 2", "PlanName": "แผน 2", "TelNo": " ", "Email": "", "MemberCode": "*********-2 Chatbot2", "EvenMBAccident": "Inpatient Benefits|Outpatient Benefits", "EvenMBAccidentTH": "Inpatient Benefits|Outpatient Benefits", "EvenMBAccidentEN": "Inpatient Benefits|Outpatient Benefits", "EvenMBIllness": "Inpatient Benefits|Outpatient Benefits", "EvenMBIllnessTH": "Inpatient Benefits|Outpatient Benefits", "EvenMBIllnessEN": "Inpatient Benefits|Outpatient Benefits", "EvenMBOtherTH": "", "EvenMBOtherEN": "", "BirthDate": "", "Gender": "Unknown", "Age": "0", "Passport": "", "CountryCode": "", "CertificateNo": "T002"}, {"Name": "ทดสอบ2", "NameEN": "ทดสอบ2", "Surname": "<PERSON><PERSON><PERSON>", "SurnameEN": "<PERSON><PERSON><PERSON>", "CitizenID": "2019086318637", "CardType": "Individual Policy", "InsurerCode": "INS202400022", "InsurerName": "บริษัท ประกันภัยไทยวิวัฒน์ จำกัด (มหาชน)", "InsurerNameEN": "Thaivivat Insurance Public Company Limited", "InsurerDedicateedLine": "*********", "CompanyCode": "COM202400181", "CompanyName": "กลุ่มธนาคารแห่งประเทศไทย (BOT)", "CompanyNameEN": "กลุ่มธนาคารแห่งประเทศไทย (BOT)", "StaffNo": " ", "EffFrom": "01/01/2025", "EffTo": "01/12/2025", "PolNo": "NHSBOT", "PlanCode": "NHSBOT1", "PlanName": "PLAN 1 พนักงาน (Auto Major)", "TelNo": " ", "Email": "", "MemberCode": "*********** Chatbot5", "EvenMBAccident": "New Health Standard|Outpatient Benefits", "EvenMBAccidentTH": "New Health Standard|Outpatient Benefits", "EvenMBAccidentEN": "New Health Standard|Outpatient Benefits", "EvenMBIllness": "Dental|New Health Standard|Outpatient Benefits", "EvenMBIllnessTH": "Dental|New Health Standard|Outpatient Benefits", "EvenMBIllnessEN": "Dental|New Health Standard|Outpatient Benefits", "EvenMBOtherTH": "", "EvenMBOtherEN": "", "BirthDate": "", "Gender": "Unknown", "Age": "0", "Passport": "", "CountryCode": "", "CertificateNo": "NHSBOT"}], "ErrorMessage": " "}