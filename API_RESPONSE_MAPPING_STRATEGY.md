# API Response Mapping Strategy for PoliciesTab Integration

## Overview

This document provides a detailed mapping strategy for integrating the TPA API responses from `api-policy-claims-fetch.md` with the existing UI components in `src/lib/components/customer/tabs/PoliciesTab.svelte`. The mapping ensures that external API data is correctly transformed to match the component's expected data structures.

## API Response Structure Analysis

### 1. Policy List Response (`PolicyListSocial`)

**Source**: Step 5 - `https://uat.thirdpartyadmin.co.th:4443/TPA.TMS.Web.API_PREPROV2/api/PolicyListSocial`

**Response Structure**:
```json
{
  "ListOfPolicyListSocial": [
    {
      "Name": "ทดสอบ2",
      "NameEN": "ทดสอบ2", 
      "Surname": "Chatbot",
      "SurnameEN": "Chatbot",
      "CitizenID": "2019086318637",
      "CardType": "Group Insurance",
      "InsurerCode": "INS202200006",
      "InsurerName": "บริษัท บลูเวนเจอร์ ทีพีเอ จำกัด",
      "InsurerNameEN": "บริษัท บลูเวนเจอร์ ทีพีเอ จำกัด",
      "CompanyCode": "COM202200080",
      "CompanyName": "บริษัท บลูเวนเจอร์ ทีพีเอ จำกัด",
      "CompanyNameEN": "บริษัท บลูเวนเจอร์ ทีพีเอ จำกัด",
      "EffFrom": "15/02/2024",
      "EffTo": "20/12/2025",
      "PolNo": "BVTPA_2024",
      "PlanCode": "PLAN 3",
      "PlanName": "แผน 3",
      "MemberCode": "*********-2 Chatbot4",
      "CertificateNo": "BVTPA_2024"
    }
  ]
}
```

### 2. Policy Details Response (`PolicyDetailSocial`)

**Source**: Step 7 - `https://uat.thirdpartyadmin.co.th:4443/TPA.TMS.Web.API_PREPROV2/api/PolicyDetailSocial`

**Response Structure**:
```json
{
  "ListOfPolDet": [
    {
      "MainBenefit": "Outpatient Benefits",
      "MainBenefitEN": "Outpatient Benefits",
      "Coverage": [
        {
          "CovNo": "1",
          "CovDesc": "ผู้ป่วยนอก",
          "CovDescEN": "ผู้ป่วยนอก", 
          "CovLimit": "2,000",
          "CovUtilized": " (0 ครั้ง)"
        }
      ]
    }
  ],
  "ListOfPolClaim": [
    {
      "ClmNo": "C20250001714",
      "ClmSource": "Reimbursement",
      "ClmType": "Accident",
      "ClmDiagCode": "A25.9",
      "ClmDiagTH": "ไข้ที่เกิดจากหนู ที่มิได้ระบุรายละเอียด",
      "ClmDiagEN": "Rat-bite fever,unspecified",
      "ClmStatus": "Open",
      "ClmStatusTxt": "อยู่ระหว่างดำเนินการ",
      "ClmStatusTxtEN": "In Progress",
      "ClmVisitDate": "23/05/2025",
      "ClmDischargeDate": "23/05/2025",
      "ClmIncurredAmt": "1000000",
      "ClmPayable": "2000",
      "ClmPaymentDate": "",
      "ClmProviderTH": "พญาไท 2 BDMS",
      "ClmProviderEN": "PHYATHAI 2 HOSPITAL"
    }
  ]
}
```

## Data Transformation Mapping

### 1. Policy Mapping: TPA API → Internal Policy Type

| **TPA API Field** | **Internal Field** | **Transformation Logic** | **Notes** |
|-------------------|-------------------|---------------------------|-----------|
| `PolNo` | `policy_number` | Direct mapping | Primary identifier |
| `Name + Surname` | `product.name` | Concatenate with space | Customer name becomes product name |
| `CardType` | `product.product_type` | Map to PolicyType enum | Requires mapping logic |
| `InsurerName` | `insurer` | Direct mapping (TH version) | Use Thai name |
| `InsurerNameEN` | `product.provider` | Direct mapping | Use English name |
| `CompanyName` | `product.category` | Direct mapping | Contract company |
| `EffFrom` | `start_date` | Parse DD/MM/YYYY → ISO string | Date format conversion |
| `EffTo` | `end_date` | Parse DD/MM/YYYY → ISO string | Date format conversion |
| `PlanCode` | `plan_code` | Direct mapping | Plan identifier |
| `PlanName` | `plan_name` | Direct mapping | Plan display name |
| `CertificateNo` | `policy_number` (alt) | Use if PolNo unavailable | Fallback identifier |
| `MemberCode` | Internal reference | Store for detail fetching | Not displayed |

**Missing Fields (Require Defaults/Calculation)**:
- `id`: Generate sequential ID
- `customer_id`: From request context
- `policy_status`: Derive from EffTo vs current date
- `issue_date`: Use current date or EffFrom
- `premium_amount`: Not available - set to 0 or estimate
- `coverage_amount`: Calculate from Coverage limits
- `currency`: Default to 'THB'
- `payment_frequency`: Default to 'ANNUAL'
- `total_claims`: Count from ListOfPolClaim
- `active_claims`: Count open claims
- `created_on/updated_on`: Current timestamp

### 2. Coverage Details Mapping

| **TPA API Field** | **Internal Field** | **Transformation Logic** |
|-------------------|-------------------|---------------------------|
| `ListOfPolDet` | `coverage_details` | Transform to structured object |
| `MainBenefit` | Coverage category | Group coverages by benefit type |
| `Coverage[].CovDesc` | Coverage description | Use Thai description |
| `Coverage[].CovLimit` | Coverage limit | Parse numeric value, remove commas |
| `Coverage[].CovUtilized` | Usage information | Parse utilization data |

**Coverage Details Structure**:
```typescript
coverage_details: {
  "Outpatient Benefits": {
    limit: 2000,
    utilized: 0,
    description: "ผู้ป่วยนอก",
    subcoverages: [...]
  },
  "Inpatient Benefits": {
    limit: 275000,
    utilized: 0,
    description: "ผู้ป่วยใน",
    subcoverages: [...]
  }
}
```

### 3. Claim Mapping: TPA API → Internal Claim Type

| **TPA API Field** | **Internal Field** | **Transformation Logic** | **Notes** |
|-------------------|-------------------|---------------------------|-----------|
| `ClmNo` | `claim_number` | Direct mapping | Primary identifier |
| `ClmPolNo` | `policy_number` | Direct mapping | Link to policy |
| `ClmType` | `claim_type` | Map to ClaimType enum | Requires mapping |
| `ClmStatus` | `claim_status` | Map to ClaimStatus enum | Status mapping |
| `ClmVisitDate` | `incident_date` | Parse DD/MM/YYYY → ISO | Date conversion |
| `ClmVisitDate` | `reported_date` | Same as incident_date | Assumption |
| `ClmIncurredAmt` | `claimed_amount` | Parse numeric, remove commas | Financial data |
| `ClmPayable` | `approved_amount` | Parse numeric, remove commas | Approved amount |
| `ClmDiagTH` | `description` | Direct mapping | Use Thai diagnosis |
| `ClmProviderTH` | `incident_location` | Direct mapping | Hospital/provider |
| `ClmPaymentDate` | `settlement_date` | Parse if not empty | Payment date |

**Missing Fields (Require Defaults)**:
- `id`: Generate sequential ID
- `policy_id`: Lookup from policy_number
- `customer_id`: From request context
- `paid_amount`: Use ClmPayable if payment date exists
- `currency`: Default to 'THB'
- `assigned_adjuster`: Not available - set to null
- `created_on/updated_on`: Current timestamp

### 4. Status Mapping Logic

**Policy Status Mapping**:
```typescript
function mapPolicyStatus(effTo: string): PolicyStatus {
  const endDate = parseDate(effTo); // DD/MM/YYYY → Date
  const now = new Date();
  const daysUntilExpiry = (endDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24);
  
  if (daysUntilExpiry < 0) return 'EXPIRED';
  if (daysUntilExpiry <= 30) return 'NEARLY_EXPIRED'; // Custom status
  return 'ACTIVE';
}
```

**Claim Status Mapping**:
```typescript
const claimStatusMap: Record<string, ClaimStatus> = {
  'Open': 'UNDER_REVIEW',
  'Approved': 'APPROVED', 
  'Rejected': 'REJECTED',
  'Paid': 'PAID',
  'Closed': 'CLOSED'
};
```

**Claim Type Mapping**:
```typescript
const claimTypeMap: Record<string, ClaimType> = {
  'Accident': 'ACCIDENT',
  'Illness': 'MEDICAL',
  'Medical': 'MEDICAL',
  'Dental': 'MEDICAL',
  'Hospitalization': 'HOSPITALIZATION'
};
```

### 5. Product Type Mapping

**Card Type → Product Type**:
```typescript
const productTypeMap: Record<string, PolicyType> = {
  'Group Insurance': 'HEALTH',
  'Individual Insurance': 'HEALTH', 
  'Life Insurance': 'LIFE',
  'Auto Insurance': 'AUTO',
  'Travel Insurance': 'TRAVEL',
  'Property Insurance': 'PROPERTY'
};
```

## Data Transformation Implementation

### 1. Policy Transformer Function

```typescript
function transformPolicy(
  policyListItem: any, 
  policyDetails: any[], 
  claims: any[], 
  customerId: number,
  index: number
): Policy {
  const coverageAmount = calculateTotalCoverage(policyDetails);
  const claimsForPolicy = claims.filter(c => c.ClmPolNo === policyListItem.PolNo);
  
  return {
    id: index + 1,
    policy_number: policyListItem.PolNo,
    customer_id: customerId,
    product: {
      id: index + 1,
      name: `${policyListItem.Name} ${policyListItem.Surname}`.trim(),
      product_type: mapProductType(policyListItem.CardType),
      description: `${policyListItem.CardType} - ${policyListItem.PlanName}`,
      category: policyListItem.CompanyName,
      provider: policyListItem.InsurerNameEN,
      coverage_details: extractCoverageDetails(policyDetails),
      exclusions: [] // Not available in API
    },
    policy_status: mapPolicyStatus(policyListItem.EffTo),
    issue_date: parseDate(policyListItem.EffFrom).toISOString(),
    start_date: parseDate(policyListItem.EffFrom).toISOString(),
    end_date: parseDate(policyListItem.EffTo).toISOString(),
    renewal_date: calculateRenewalDate(policyListItem.EffTo).toISOString(),
    premium_amount: 0, // Not available - could be estimated
    coverage_amount: coverageAmount,
    currency: 'THB',
    payment_frequency: 'ANNUAL',
    next_payment_date: calculateRenewalDate(policyListItem.EffTo).toISOString(),
    insurer: policyListItem.InsurerName,
    plan_code: policyListItem.PlanCode,
    plan_name: policyListItem.PlanName,
    coverage_details: transformCoverageDetails(policyDetails),
    total_claims: claimsForPolicy.length,
    active_claims: claimsForPolicy.filter(c => c.ClmStatus === 'Open').length,
    total_claims_amount: claimsForPolicy.reduce((sum, c) => sum + parseAmount(c.ClmIncurredAmt), 0),
    created_on: new Date().toISOString(),
    updated_on: new Date().toISOString()
  };
}
```

### 2. Claim Transformer Function

```typescript
function transformClaim(
  claimItem: any, 
  customerId: number, 
  policyId: number,
  index: number
): Claim {
  return {
    id: index + 1,
    claim_number: claimItem.ClmNo,
    policy_id: policyId,
    policy_number: claimItem.ClmPolNo,
    customer_id: customerId,
    claim_type: mapClaimType(claimItem.ClmType),
    claim_status: mapClaimStatus(claimItem.ClmStatus),
    incident_date: parseDate(claimItem.ClmVisitDate).toISOString(),
    reported_date: parseDate(claimItem.ClmVisitDate).toISOString(),
    claimed_amount: parseAmount(claimItem.ClmIncurredAmt),
    approved_amount: parseAmount(claimItem.ClmPayable),
    paid_amount: claimItem.ClmPaymentDate ? parseAmount(claimItem.ClmPayable) : 0,
    currency: 'THB',
    description: claimItem.ClmDiagTH || claimItem.ClmDiagEN || 'No diagnosis provided',
    incident_location: claimItem.ClmProviderTH,
    settlement_date: claimItem.ClmPaymentDate ? parseDate(claimItem.ClmPaymentDate).toISOString() : undefined,
    created_on: new Date().toISOString(),
    updated_on: new Date().toISOString()
  };
}
```

### 3. Statistics Calculator

```typescript
function calculateStatistics(policies: Policy[], claims: Claim[]): PolicyStatistics {
  const now = new Date();
  const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
  
  return {
    total_policies: policies.length,
    active_policies: policies.filter(p => p.policy_status === 'ACTIVE').length,
    expired_policies: policies.filter(p => p.policy_status === 'EXPIRED').length,
    pending_policies: policies.filter(p => p.policy_status === 'PENDING').length,
    cancelled_policies: policies.filter(p => p.policy_status === 'CANCELLED').length,
    waiting_period_policies: 0, // Not available in API
    nearly_expired_policies: policies.filter(p => p.policy_status === 'NEARLY_EXPIRED').length,
    total_premium_amount: policies.reduce((sum, p) => sum + p.premium_amount, 0),
    total_coverage_amount: policies.reduce((sum, p) => sum + p.coverage_amount, 0),
    average_premium: policies.length > 0 ? policies.reduce((sum, p) => sum + p.premium_amount, 0) / policies.length : 0,
    total_claims: claims.length,
    active_claims: claims.filter(c => c.claim_status === 'UNDER_REVIEW').length,
    approved_claims: claims.filter(c => c.claim_status === 'APPROVED' || c.claim_status === 'PAID').length,
    rejected_claims: claims.filter(c => c.claim_status === 'REJECTED').length,
    total_claims_amount: claims.reduce((sum, c) => sum + c.claimed_amount, 0),
    total_paid_amount: claims.reduce((sum, c) => sum + (c.paid_amount || 0), 0),
    policy_type_breakdown: calculatePolicyTypeBreakdown(policies),
    recent_policies: policies.filter(p => new Date(p.created_on) > thirtyDaysAgo).length,
    recent_claims: claims.filter(c => new Date(c.created_on) > thirtyDaysAgo).length
  };
}
```

## Key Discrepancies & Challenges

### 1. Missing Financial Data
- **Issue**: TPA API doesn't provide premium amounts
- **Solution**: Set to 0 or implement estimation logic based on coverage
- **Impact**: Premium-based filtering and statistics will be limited

### 2. Date Format Differences
- **Issue**: TPA uses DD/MM/YYYY, internal uses ISO strings
- **Solution**: Implement robust date parsing with format detection
- **Risk**: Date parsing errors could break the UI

### 3. Status Mapping Complexity
- **Issue**: TPA statuses don't directly map to internal enums
- **Solution**: Create comprehensive mapping with fallback logic
- **Risk**: Unmapped statuses could cause display issues

### 4. Coverage Structure Differences
- **Issue**: TPA has nested coverage arrays, internal expects flat structure
- **Solution**: Flatten and aggregate coverage data
- **Impact**: Detailed coverage display may be simplified

### 5. Missing Metadata
- **Issue**: No creation dates, user IDs, or audit trails in TPA API
- **Solution**: Generate synthetic metadata with current timestamps
- **Impact**: Historical data tracking will be limited

## UI Integration Considerations

### 1. Policy Cards Display
- **Status Badges**: Ensure all mapped statuses have corresponding UI colors
- **Coverage Display**: Aggregate coverage amounts for summary display
- **Date Formatting**: Use consistent date formatting throughout UI

### 2. Claims Display
- **Status Icons**: Map claim statuses to appropriate icons
- **Amount Formatting**: Handle large numbers and currency formatting
- **Provider Information**: Display hospital/provider names consistently

### 3. Filtering & Search
- **Status Filters**: Update filter options to include mapped statuses
- **Type Filters**: Ensure product type mapping works with existing filters
- **Search Fields**: Include all searchable fields from transformed data

### 4. Error Handling
- **Data Validation**: Validate transformed data before display
- **Fallback Values**: Provide sensible defaults for missing data
- **Error Messages**: Show user-friendly messages for transformation errors

## Implementation Recommendations

### 1. Phased Approach
1. **Phase 1**: Implement basic policy and claim transformation
2. **Phase 2**: Add coverage details and statistics calculation
3. **Phase 3**: Enhance error handling and edge cases
4. **Phase 4**: Optimize performance and add caching

### 2. Data Validation
- Implement schema validation for transformed data
- Add unit tests for all transformation functions
- Include edge case handling for malformed API responses

### 3. Performance Optimization
- Cache transformed data to avoid repeated processing
- Implement lazy loading for detailed coverage information
- Use efficient data structures for large datasets

### 4. Monitoring & Logging
- Log transformation errors for debugging
- Monitor API response variations
- Track transformation performance metrics

## Testing Strategy

### 1. Unit Tests
- Test each transformation function with sample API data
- Verify edge cases and error conditions
- Validate output against TypeScript interfaces

### 2. Integration Tests
- Test complete transformation pipeline
- Verify UI rendering with transformed data
- Test filtering and search functionality

### 3. Data Quality Tests
- Compare transformed data with mock data structure
- Validate statistical calculations
- Test date parsing with various formats

## Utility Functions Implementation

### 1. Date Parsing Utilities

```typescript
/**
 * Parse TPA date format (DD/MM/YYYY) to Date object
 */
function parseTPADate(dateStr: string): Date {
  if (!dateStr || dateStr.trim() === '') {
    return new Date();
  }

  const [day, month, year] = dateStr.split('/').map(Number);
  return new Date(year, month - 1, day); // month is 0-indexed
}

/**
 * Calculate renewal date (typically 1 year after end date)
 */
function calculateRenewalDate(effTo: string): Date {
  const endDate = parseTPADate(effTo);
  return new Date(endDate.getFullYear() + 1, endDate.getMonth(), endDate.getDate());
}
```

### 2. Amount Parsing Utilities

```typescript
/**
 * Parse TPA amount format (with commas) to number
 */
function parseAmount(amountStr: string): number {
  if (!amountStr || amountStr.trim() === '' || amountStr === '-') {
    return 0;
  }

  // Remove commas and parse as number
  const cleanAmount = amountStr.replace(/,/g, '');
  const parsed = parseFloat(cleanAmount);
  return isNaN(parsed) ? 0 : parsed;
}

/**
 * Calculate total coverage from policy details
 */
function calculateTotalCoverage(policyDetails: any[]): number {
  let total = 0;

  policyDetails.forEach(benefit => {
    benefit.Coverage?.forEach((coverage: any) => {
      if (coverage.CovLimit && coverage.CovLimit !== '') {
        total += parseAmount(coverage.CovLimit);
      }
    });
  });

  return total;
}
```

### 3. Coverage Details Transformation

```typescript
/**
 * Transform TPA coverage structure to internal format
 */
function transformCoverageDetails(policyDetails: any[]): Record<string, any> {
  const coverageDetails: Record<string, any> = {};

  policyDetails.forEach(benefit => {
    const benefitName = benefit.MainBenefitEN || benefit.MainBenefit;
    const subcoverages: any[] = [];
    let totalLimit = 0;

    benefit.Coverage?.forEach((coverage: any) => {
      if (coverage.CovNo && coverage.CovNo !== 'หมายเหตุ' && coverage.CovNo !== 'Remarks') {
        const limit = parseAmount(coverage.CovLimit);
        totalLimit += limit;

        subcoverages.push({
          number: coverage.CovNo,
          description: coverage.CovDescEN || coverage.CovDesc,
          limit: limit,
          utilized: coverage.CovUtilizedEN || coverage.CovUtilized || '-'
        });
      }
    });

    coverageDetails[benefitName] = {
      limit: totalLimit,
      utilized: 0, // Would need to calculate from claims
      description: benefitName,
      subcoverages: subcoverages
    };
  });

  return coverageDetails;
}
```

### 4. Policy Type Breakdown Calculator

```typescript
/**
 * Calculate policy type breakdown for statistics
 */
function calculatePolicyTypeBreakdown(policies: Policy[]): Record<PolicyType, number> {
  const breakdown: Record<PolicyType, number> = {
    'LIFE': 0,
    'HEALTH': 0,
    'AUTO': 0,
    'PROPERTY': 0,
    'TRAVEL': 0,
    'DISABILITY': 0,
    'CRITICAL_ILLNESS': 0
  };

  policies.forEach(policy => {
    if (breakdown.hasOwnProperty(policy.product.product_type)) {
      breakdown[policy.product.product_type as PolicyType]++;
    }
  });

  return breakdown;
}
```

## Complete Data Transformer Service

### PolicyDataTransformer Class

```typescript
export class PolicyDataTransformer {
  /**
   * Main transformation method
   */
  transformToCustomerPoliciesData(
    policyListResponse: any,
    policyDetailsResponses: any[],
    customerId: number,
    customerName: string = '',
    customerEmail: string = ''
  ): CustomerPoliciesData {

    const policyList = policyListResponse.ListOfPolicyListSocial || [];
    const allClaims: any[] = [];
    const allPolicyDetails: any[] = [];

    // Aggregate all policy details and claims
    policyDetailsResponses.forEach(response => {
      if (response.ListOfPolDet) {
        allPolicyDetails.push(...response.ListOfPolDet);
      }
      if (response.ListOfPolClaim) {
        allClaims.push(...response.ListOfPolClaim);
      }
    });

    // Transform policies
    const policies = policyList.map((policyItem: any, index: number) =>
      this.transformPolicy(policyItem, allPolicyDetails, allClaims, customerId, index)
    );

    // Transform claims
    const claims = allClaims.map((claimItem: any, index: number) => {
      const relatedPolicy = policies.find(p => p.policy_number === claimItem.ClmPolNo);
      return this.transformClaim(claimItem, customerId, relatedPolicy?.id || 0, index);
    });

    // Calculate statistics
    const statistics = this.calculateStatistics(policies, claims);

    return {
      customer_id: customerId,
      customer_name: customerName,
      customer_email: customerEmail,
      policies: policies,
      claims: claims,
      statistics: statistics,
      last_updated: new Date().toISOString()
    };
  }

  private transformPolicy(/* implementation as shown above */): Policy { /* ... */ }
  private transformClaim(/* implementation as shown above */): Claim { /* ... */ }
  private calculateStatistics(/* implementation as shown above */): PolicyStatistics { /* ... */ }
}
```

## Error Handling & Validation

### 1. Data Validation Schema

```typescript
/**
 * Validate transformed policy data
 */
function validatePolicy(policy: Policy): { valid: boolean; errors: string[] } {
  const errors: string[] = [];

  if (!policy.policy_number) errors.push('Policy number is required');
  if (!policy.product?.name) errors.push('Product name is required');
  if (!policy.start_date) errors.push('Start date is required');
  if (!policy.end_date) errors.push('End date is required');
  if (policy.coverage_amount < 0) errors.push('Coverage amount cannot be negative');

  return {
    valid: errors.length === 0,
    errors
  };
}

/**
 * Validate transformed claim data
 */
function validateClaim(claim: Claim): { valid: boolean; errors: string[] } {
  const errors: string[] = [];

  if (!claim.claim_number) errors.push('Claim number is required');
  if (!claim.policy_number) errors.push('Policy number is required');
  if (!claim.incident_date) errors.push('Incident date is required');
  if (claim.claimed_amount < 0) errors.push('Claimed amount cannot be negative');

  return {
    valid: errors.length === 0,
    errors
  };
}
```

### 2. Transformation Error Handling

```typescript
/**
 * Safe transformation with error handling
 */
export class SafePolicyDataTransformer extends PolicyDataTransformer {
  transformToCustomerPoliciesData(
    policyListResponse: any,
    policyDetailsResponses: any[],
    customerId: number,
    customerName: string = '',
    customerEmail: string = ''
  ): { data: CustomerPoliciesData | null; errors: string[] } {

    const errors: string[] = [];

    try {
      const data = super.transformToCustomerPoliciesData(
        policyListResponse,
        policyDetailsResponses,
        customerId,
        customerName,
        customerEmail
      );

      // Validate transformed data
      data.policies.forEach((policy, index) => {
        const validation = validatePolicy(policy);
        if (!validation.valid) {
          errors.push(`Policy ${index + 1}: ${validation.errors.join(', ')}`);
        }
      });

      data.claims.forEach((claim, index) => {
        const validation = validateClaim(claim);
        if (!validation.valid) {
          errors.push(`Claim ${index + 1}: ${validation.errors.join(', ')}`);
        }
      });

      return { data, errors };

    } catch (error) {
      errors.push(`Transformation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
      return { data: null, errors };
    }
  }
}
```

## Integration with PoliciesTab Component

### 1. Updated Service Call

```typescript
// In customers.service.ts
async getCustomerPoliciesAndClaims(id: string, token: string): Promise<CustomerPoliciesResponse> {
  try {
    const workflowExecutor = new PolicyWorkflowExecutor();
    const rawData = await workflowExecutor.executeWorkflow(id);

    const transformer = new SafePolicyDataTransformer();
    const { data, errors } = transformer.transformToCustomerPoliciesData(
      rawData.policyListResponse,
      rawData.policyDetailsResponses,
      parseInt(id),
      rawData.customerName,
      rawData.customerEmail
    );

    if (errors.length > 0) {
      console.warn('Data transformation warnings:', errors);
    }

    if (!data) {
      throw new Error('Data transformation failed');
    }

    return {
      customer_policies: [data],
      res_status: 200
    };

  } catch (error) {
    console.error('Policy API integration failed:', error);
    throw error;
  }
}
```

### 2. Component Error Handling Enhancement

```typescript
// In PoliciesTab.svelte - Enhanced loadPoliciesData function
async function loadPoliciesData() {
  try {
    loading = true;
    error = '';

    if (!customer?.customer_id) {
      throw new Error('No customer ID provided');
    }

    if (!access_token) {
      throw new Error('No access token provided');
    }

    try {
      const result = await services.customers.getCustomerPoliciesAndClaims(
        customer.customer_id.toString(),
        access_token
      );

      if (result.res_status === 200 && result.customer_policies?.length > 0) {
        const apiData = result.customer_policies[0];

        // Validate the transformed data structure
        if (!apiData.policies || !Array.isArray(apiData.policies)) {
          throw new Error('Invalid policy data structure received');
        }

        if (!apiData.claims || !Array.isArray(apiData.claims)) {
          throw new Error('Invalid claims data structure received');
        }

        policiesData = apiData;

        // Log successful transformation
        console.log('Successfully loaded and transformed policy data:', {
          policies: apiData.policies.length,
          claims: apiData.claims.length,
          lastUpdated: apiData.last_updated
        });

      } else {
        throw new Error(result.error_msg || 'Failed to load policies from API');
      }

    } catch (apiError) {
      console.warn('API call failed, using mock data:', apiError);

      // Enhanced fallback with user notification
      error = 'Unable to load live policy data. Showing sample data.';
      policiesData = createMockPoliciesData(customer.customer_id);
    }

    // Initialize filtered data
    if (policiesData) {
      filteredPolicies = policiesData.policies || [];
      filteredClaims = policiesData.claims || [];
    }

  } catch (err) {
    error = err instanceof Error ? err.message : 'Failed to load policies data';
    console.error('Error loading policies:', err);
  } finally {
    loading = false;
  }
}
```

This mapping strategy provides a comprehensive foundation for integrating the TPA API responses with the existing PoliciesTab component while maintaining data integrity and user experience. The implementation includes robust error handling, data validation, and fallback mechanisms to ensure reliable operation even when API responses vary or contain unexpected data.
