import io
import zipfile
import json
import requests
import logging
import re
from datetime import datetime
from django.db import transaction
from typing import Dict, Any, List
from django.db.models import Q, Count, Max, Prefetch, OuterRef, Subquery, Exists, Avg
from django.utils import timezone
from django.utils.html import escape
from django.http import HttpResponse, FileResponse
from django.db import models
from django.shortcuts import get_object_or_404
from django_filters import rest_framework as django_filters
from rest_framework import generics, mixins, status, filters
from rest_framework.request import Request
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework_simplejwt.authentication import JWTAuthentication
from rest_framework.permissions import (
    IsA<PERSON>enticated,
    AllowAny,
    IsAuthenticatedOrReadOnly,
    IsAdminUser,
)
from rest_framework.filters import OrderingFilter
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework.filters import SearchFilter, OrderingFilter
from rest_framework.pagination import PageNumberPagination

from connectors.services.platform_routing_service import PlatformRoutingService
from customer.services import CustomerMemoryService
from customer._services.linking_service import CustomerLinkingService
from setting.models import MessageTemplate
from user.permissions import TicketOwnershipPermission

from .models import Customer, CustomerLinkingHistory, CustomerMemory, CustomerPlatformIdentity, CustomerTag, Gender, Interface, CustomerNote
from .serializers import CustomerLinkingHistorySerializer, CustomerMemorySerializer, CustomerPlatformIdentitySerializer, CustomerPlatformIdentityWithCustomerSerializer, CustomerSerializer, CustomerTagBasicSerializer, CustomerTagSerializer, CustomerWithIdentitiesSerializer, CustomerWithPlatformsSerializer, GenderSerializer, InterfaceSerializer, CustomerNoteSerializer, LinkingCodeResponseSerializer, LinkingExecutionSerializer, LinkingValidationSerializer, UnlinkPlatformSerializer

from devproject.utils.utils import LoggingMixin
from devproject.utils.azure_storage import AzureBlobStorage
from ticket.models import Ticket, Message, TicketAnalysis
from ticket.serializers import ChatCenterMessageSerializer, MessageWithFilesSerializer, TicketAnalysisSerializer, TicketSerializer, MessageSerializer
from llm_rag_doc.models import PolicyHolder
from llm_rag_doc.serializers import PolicyHolderSerializer
# from linechatbot.tasks import send_message_via_route_message_to_customer
from customer._services.message_file_service import MessageFileService
# from customer._services import message_file_service

from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi

logger = logging.getLogger('django.api_logs')

class CustomerListCreateView(
    LoggingMixin, generics.GenericAPIView, mixins.ListModelMixin, mixins.CreateModelMixin
):

    serializer_class = CustomerSerializer
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    queryset = Customer.objects.all()

    filter_backends = [OrderingFilter]
    ordering_fields = ['customer_id']  # Fields that can be used for ordering
    ordering = ['customer_id']  # Order by id ascending

    def perform_create(self, serializer):
        user = self.request.user
        serializer.save(created_by=user)
        return super().perform_create(serializer)
    
    def get(self, request: Request, *args, **kwargs):
        return self.list(request, *args, **kwargs)

    def post(self, request: Request, *args, **kwargs):

        serializer = self.get_serializer(data=request.data)
        if serializer.is_valid():
            self.perform_create(serializer)
            response = {"message": "Customer Created Successfully", "data": serializer.data}
            return Response(data=response, status=status.HTTP_201_CREATED)
        return Response(data=serializer.errors, status=status.HTTP_400_BAD_REQUEST)

class CustomerRetrieveUpdateDeleteView(
    LoggingMixin, generics.GenericAPIView, mixins.RetrieveModelMixin, mixins.UpdateModelMixin, mixins.DestroyModelMixin
):
    
    serializer_class = CustomerSerializer
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    queryset = Customer.objects.all()

    def perform_update(self, serializer):
        user = self.request.user
        serializer.save(updated_by=user)
        return super().perform_update(serializer)

    def retrieve(self, request, *args, **kwargs):
        instance = self.get_object()
        
        # If customer's picture_url is null, try to get it from platform identities
        if not instance.picture_url:
            # Get the first active platform identity with a picture_url
            platform_identity = instance.platform_identities.filter(
                is_active=True,
                picture_url__isnull=False
            ).exclude(picture_url='').first()
            
            if platform_identity:
                # Temporarily set the picture_url for serialization
                instance.picture_url = platform_identity.picture_url
        
        serializer = self.get_serializer(instance)
        return Response(serializer.data)

    def get(self, request: Request, *args, **kwargs):
        return self.retrieve(request, *args, **kwargs)

    def put(self, request: Request, *args, **kwargs):

        partial = kwargs.pop('partial', True)
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=partial)
        if serializer.is_valid():
            self.perform_update(serializer)
            response = {"message": "Customer Updated Successfully", "data": serializer.data}
            return Response(data=response, status=status.HTTP_200_OK)
        return Response(data=serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def delete(self, request: Request, *args, **kwargs):

        instance = self.get_object()
        self.perform_destroy(instance)
        response = {"message": "Customer Deleted Successfully"}
        return Response(data=response, status=status.HTTP_200_OK)

# ========== PAGINATION CLASSES ==========
class StandardResultsPagination(PageNumberPagination):
    page_size = 20
    page_size_query_param = 'limit'
    max_page_size = 100

# ========== FILTER CLASSES ==========
class CustomerFilter(django_filters.FilterSet):
    """
    Custom filter for Customer model supporting multiple values per filter
    """
    tags = django_filters.CharFilter(method='filter_tags')
    platforms = django_filters.CharFilter(method='filter_platforms')

    class Meta:
        model = Customer
        fields = ['customer_id', 'tags', 'platforms', 'account_status', 'customer_type']
    
    def filter_tags(self, queryset, name, value):
        """
        Filter customers by tags (supports multiple comma-separated values)
        Format: 'tags=vip,premium,new' or 'tags=1,2,3'
        """
        if not value:
            return queryset

        tags = value.split(',')
        if not tags:
            return queryset

        # Try to filter by tag IDs first
        try:
            tag_ids = [int(t) for t in tags]
            return queryset.filter(customer_tags__id__in=tag_ids).distinct()
        except ValueError:
            # If not IDs, try filtering by tag names
            return queryset.filter(customer_tags__name__in=tags).distinct()

    def filter_platforms(self, queryset, name, value):
        """
        Filter customers by platforms (supports multiple comma-separated values)
        Format: 'platforms=LINE,WHATSAPP' or 'platform=LINE'
        """
        if not value:
            return queryset

        platforms = [p.strip().upper() for p in value.split(',') if p.strip()]
        if not platforms:
            return queryset

        return queryset.filter(
            platform_identities__platform__in=platforms,
            platform_identities__is_active=True
        ).distinct()

# ========== CUSTOMER VIEWS ==========
class CustomerListPaginatedView(generics.ListAPIView):
    """
    Paginated list of customers with filters for platform and tags.

    Endpoint: GET /api/customers/paginated/

    Query Parameters:
    - platforms: Filter by platform (comma-separated) e.g., ?platforms=LINE,WHATSAPP
    - tags: Filter by tag names or IDs (comma-separated) e.g., ?tags=vip,premium
    - account_status: Filter by account status e.g., ?account_status=ACTIVE
    - customer_type: Filter by customer type e.g., ?customer_type=VIP
    - search: Multi-term search across customer_id, name, email, phone, notes
    - search_fields: Limit search to specific fields (comma-separated) e.g., ?search_fields=customer_id,name,email,notes
      Valid search fields: customer_id, name, first_name, last_name, email, phone, notes
    - ordering: Order by fields e.g., ?ordering=-created_on,name
    - limit: Number of results per page (default: 20, max: 100)
    - page: Page number
    """
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]
    
    pagination_class = StandardResultsPagination
    
    filter_backends = [
        django_filters.DjangoFilterBackend,
        filters.OrderingFilter  # Remove SearchFilter as we'll implement custom search
    ]
    filterset_class = CustomerFilter
    ordering_fields = [
        'customer_id', 
        'name',
        'email',
        'first_platform_name'  # For platform sorting
    ]
    ordering = ['customer_id']  # Default ordering

    # Define valid searchable fields and their corresponding search types
    SEARCHABLE_FIELDS = {
        'customer_id': 'customer_id',
        'name': 'name_fields',
        'first_name': 'name_fields',
        'last_name': 'name_fields',
        'email': 'email',
        'phone': 'phone',
        'notes': 'notes'
    }

    def get_queryset(self):
        """
        Get optimized queryset with only the fields needed for the customer list display
        """
        # # TODO - DELETE THIS SECTION OF CODES - Remove first platform subquery as we'll fetch all platforms
        # # Get the first active platform for each customer using a subquery
        # first_platform = CustomerPlatformIdentity.objects.filter(
        #     customer=OuterRef('pk'),
        #     is_active=True
        # ).order_by('-last_interaction')

        # TODO - DELETE THIS SECTION OF CODES - Remove first tag subquery as we'll fetch all tags
        # # Get the first tag for each customer using a subquery
        # first_tag = CustomerTag.objects.filter(
        #     customers=OuterRef('pk')
        # ).order_by('id')

        # Check if search is requested to optimize prefetching
        search_query = self.request.query_params.get('search', '').strip()

        # Join related tables with select_related and annotate with additional data
        queryset = Customer.objects.select_related(
            'gender_id',
            'main_interface_id'
        ).prefetch_related(
            'customer_tags',  # ADD THIS LINE - Prefetch all tags to avoid N+1 queries
            models.Prefetch(
                'platform_identities',
                # queryset=CustomerPlatformIdentity.objects.filter(is_active=True).order_by('-last_interaction')
                queryset=CustomerPlatformIdentity.objects.filter(is_active=True).order_by('channel_name')
            )
        )

        # Prefetch notes if search is requested
        if search_query:
            queryset = queryset.prefetch_related(
                models.Prefetch(
                    'customer_note_customer',
                    queryset=CustomerNote.objects.filter(is_active=True).only('content', 'customer_id'),
                    to_attr='active_notes'
                )
            )

        queryset = queryset.annotate(
            # # TODO - DELETE THIS SECTION OF CODES - Remove first platform subquery as we'll fetch all platforms
            # # Get first platform info
            # first_platform_id=Subquery(first_platform.values('id')[:1]),
            # first_platform_name=Subquery(first_platform.values('platform')[:1]),
            # first_platform_display_name=Subquery(first_platform.values('display_name')[:1]),

            # # TODO - DELETE THIS SECTION OF CODES - Remove first tag annotations
            # # Get first tag info
            # first_tag_id=Subquery(first_tag.values('id')[:1]),
            # first_tag_name=Subquery(first_tag.values('name')[:1]),
            # first_tag_color=Subquery(first_tag.values('color')[:1]),

            # Count platforms and tags
            platform_count=Count('platform_identities', filter=Q(platform_identities__is_active=True), distinct=True),
            tag_count=Count('customer_tags', distinct=True)
        )

        # # TODO - DELETE THIS SECTION OF CODES - Remove values() to keep full Customer objects with prefetched tags
        # ).values(
        #     'customer_id',
        #     'universal_id',
        #     'name',
        #     'first_name',
        #     'last_name',
        #     'email',
        #     'phone',
        #     'customer_type',
        #     'account_status',
        #     'gender_id__name',
        #     'main_interface_id__name',
        #     'last_contact_date',
        #     'total_purchases',
        #     'total_spent',
        #     'created_on',
        #     'updated_on',
        #     # Annotated fields
        #     'first_platform_id',
        #     'first_platform_name',
        #     'first_platform_display_name',
        #     'first_tag_id',
        #     'first_tag_name',
        #     'first_tag_color',
        #     'platform_count',
        #     'tag_count'
        # )
        
        # Only show active customers by default
        queryset = queryset.filter(account_status='ACTIVE')
        
        return queryset

    def validate_search_fields(self, search_fields_param):
        """
        Validate the search_fields parameter and return a list of valid field names.

        Args:
            search_fields_param (str): Comma-separated list of field names

        Returns:
            tuple: (valid_fields_list, error_message)
                - valid_fields_list: List of valid field names, or None if error
                - error_message: Error message if validation fails, or None if success
        """
        if not search_fields_param:
            return None, None

        # Split and clean field names
        field_names = [field.strip().lower() for field in search_fields_param.split(',') if field.strip()]

        if not field_names:
            return None, "search_fields parameter cannot be empty"

        # Validate each field name
        invalid_fields = []
        valid_fields = []

        for field_name in field_names:
            if field_name in self.SEARCHABLE_FIELDS:
                valid_fields.append(field_name)
            else:
                invalid_fields.append(field_name)

        if invalid_fields:
            valid_field_names = list(self.SEARCHABLE_FIELDS.keys())
            return None, f"Invalid search fields: {', '.join(invalid_fields)}. Valid fields are: {', '.join(valid_field_names)}"

        return valid_fields, None

    def filter_queryset(self, queryset):
        """
        Override filter_queryset to add custom search and secondary sorting by customer_id
        """
        # Apply DjangoFilterBackend filters first (platform, tags)
        for backend in self.filter_backends:
            if backend == filters.OrderingFilter:
                continue  # Skip ordering for now, we'll handle it later
            queryset = backend().filter_queryset(self.request, queryset, self)

        # Validate search_fields parameter if provided
        search_fields_param = self.request.query_params.get('search_fields', '').strip()
        search_fields, validation_error = self.validate_search_fields(search_fields_param)

        if validation_error:
            # Store the error to be handled in the list method
            self._search_fields_error = validation_error
            return queryset

        # Apply custom search (includes notes in multi-field search)
        search_query = self.sanitize_search_input(self.request.query_params.get('search', '').strip())
        if search_query:
            queryset = self.apply_custom_search(queryset, search_query, search_fields)
        
        # Get ordering from query params
        ordering = self.request.query_params.get('ordering', '')
        
        # Default ordering
        if not ordering:
            return queryset.order_by('customer_id')
        
        # Add customer_id as secondary sort for consistency
        return queryset.order_by(ordering, 'customer_id')

    def sanitize_search_input(self, search_query):
        """
        Sanitize search input to prevent SQL injection and XSS
        """
        if not search_query:
            return ""

        # Remove HTML tags and escape special characters
        search_query = escape(search_query.strip())

        # Limit length to prevent DoS
        if len(search_query) > 500:
            search_query = search_query[:500]

        # Remove potentially dangerous SQL patterns
        dangerous_patterns = [
            r'--', r'/\*', r'\*/', r'xp_', r'sp_',
            r'exec\s', r'execute\s', r'union\s', r'select\s'
        ]

        for pattern in dangerous_patterns:
            search_query = re.sub(pattern, '', search_query, flags=re.IGNORECASE)

        return search_query

    def apply_custom_search(self, queryset, search_query, search_fields=None):
        """
        Apply custom search logic that splits by spaces and searches across specified or all fields.

        Args:
            queryset: The queryset to filter
            search_query: The search term(s)
            search_fields: List of specific fields to search in, or None for all fields

        Available search fields:
        - customer_id: Customer ID field (exact match)
        - name, first_name, last_name: Name fields (contains match)
        - email: Email field (contains match)
        - phone: Phone field (contains match)
        - notes: Notes content (contains match)
        """
        # Split search query by spaces
        search_terms = [term.strip() for term in search_query.split() if term.strip()]

        if not search_terms:
            return queryset

        # Determine which fields to search in
        if search_fields:
            # Use only specified fields
            search_field_types = set()
            for field in search_fields:
                search_field_types.add(self.SEARCHABLE_FIELDS[field])
        else:
            # Use all available fields (default behavior)
            search_field_types = set(self.SEARCHABLE_FIELDS.values())

        # For each search term, create a Q object that matches the specified fields
        final_q = Q()

        for term in search_terms:
            term_q = Q()

            # Apply search based on selected field types
            if 'customer_id' in search_field_types:
                # ID field (customer_id) - exact matching only
                try:
                    if term.isdigit():
                        # Exact match for customer_id
                        term_q |= Q(customer_id=int(term))
                except ValueError:
                    pass

            if 'name_fields' in search_field_types:
                # Name fields (first_name, last_name, name)
                term_q |= Q(first_name__icontains=term)
                term_q |= Q(last_name__icontains=term)
                term_q |= Q(name__icontains=term)

            if 'email' in search_field_types:
                # Email field
                term_q |= Q(email__icontains=term)

            if 'phone' in search_field_types:
                # Phone field
                term_q |= Q(phone__icontains=term)

            if 'notes' in search_field_types:
                # Notes - search in customer notes content
                term_q |= Q(
                    customer_note_customer__content__icontains=term,
                    customer_note_customer__is_active=True
                )

            # Add this term's Q object to the final query
            # If term_q is empty (no valid search conditions), return empty queryset
            if not term_q.children:
                # No valid search conditions for this term, return empty result
                return queryset.none()

            final_q &= term_q

        # Apply the search and remove duplicates
        return queryset.filter(final_q).distinct()
    
    def list(self, request, *args, **kwargs):
        """
        Override the default list method to format the response data.
        """
        # Check for search_fields validation error
        if hasattr(self, '_search_fields_error'):
            return Response(
                {'error': self._search_fields_error},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Get paginated queryset
        queryset = self.filter_queryset(self.get_queryset())
        page = self.paginate_queryset(queryset)
        
        if page is not None:
            # Prepare response format using the annotated data
            customers = []
            for customer in page:
                # Build platforms array with first platform
                platforms = []

                # if customer['first_platform_id']:
                #     platforms.append({
                #         'id': customer['first_platform_id'],
                #         'name': customer['first_platform_name'],
                #         'display_name': customer['first_platform_display_name']
                #     })

                # Build platforms array with all platform identities
                for platform_identity in customer.platform_identities.all():
                    platforms.append({
                        'id': platform_identity.id,
                        'platform': platform_identity.platform,
                        'platform_user_id': platform_identity.platform_user_id,
                        'provider_id': platform_identity.provider_id,
                        'provider_name': platform_identity.provider_name,
                        'channel_id': platform_identity.channel_id,
                        'channel_name': platform_identity.channel_name,
                        'display_name': platform_identity.display_name,
                        'picture_url': platform_identity.picture_url,
                        'is_verified': platform_identity.is_verified,
                        'last_interaction': platform_identity.last_interaction.isoformat() if platform_identity.last_interaction else None
                    })
                
                # # Build tag object with first tag
                # tag = None
                # if customer['first_tag_id']:
                #     tag = {
                #         'id': customer['first_tag_id'],
                #         'name': customer['first_tag_name'],
                #         'color': customer['first_tag_color']
                #     }
                
                # TODO - UPDATE THIS SECTION OF CODES - Build tags array with all tags
                # Build tags array with all tags
                tags = []
                for tag in customer.customer_tags.all():
                    tags.append({
                        'id': tag.id,
                        'name': tag.name,
                        'color': tag.color
                    })

                # customers.append({
                #     'customer_id': customer['customer_id'],
                #     'universal_id': str(customer['universal_id']),
                #     'name': self._get_full_name(customer) or customer['name'],
                #     'email': customer['email'],
                #     'phone': customer['phone'],
                #     'customer_type': customer['customer_type'],
                #     'account_status': customer['account_status'],
                #     'platforms': {
                #         'id': customer['first_platform_id'],
                #         'name': customer['first_platform_name']
                #     } if customer['first_platform_id'] else None,
                #     'platform_count': customer['platform_count'],
                #     'tag': tag,
                #     'tag_count': customer['tag_count'],
                #     'gender': customer['gender_id__name'],
                #     'main_interface': customer['main_interface_id__name'],
                #     'last_contact_date': customer['last_contact_date'].strftime('%d %b %Y %H:%M') if customer['last_contact_date'] else None,
                #     'total_purchases': customer['total_purchases'] or 0,
                #     'total_spent': float(customer['total_spent']) if customer['total_spent'] else 0,
                #     'created_on': customer['created_on'].strftime('%d %b %Y %H:%M') if customer['created_on'] else None,
                #     'updated_on': customer['updated_on'].strftime('%d %b %Y %H:%M') if customer['updated_on'] else None,
                #     'created_ago': self._get_time_ago(customer['created_on']) if customer['created_on'] else None,
                #     'updated_ago': self._get_time_ago(customer['updated_on']) if customer['updated_on'] else None,
                # })
            
                # customers.append({
                #     # TODO - UPDATE THIS SECTION OF CODES - Access attributes directly from Customer object
                #     'customer_id': customer.customer_id,
                #     'universal_id': str(customer.universal_id),
                #     'name': customer.get_full_name() or customer.name,
                #     'email': customer.email,
                #     'phone': customer.phone,
                #     'customer_type': customer.customer_type,
                #     'account_status': customer.account_status,
                #     'platforms': {
                #         'id': customer.first_platform_id,
                #         'name': customer.first_platform_name
                #     } if hasattr(customer, 'first_platform_id') and customer.first_platform_id else None,
                #     'platform_count': customer.platform_count if hasattr(customer, 'platform_count') else 0,
                #     # TODO - UPDATE THIS SECTION OF CODES - Replace single tag with tags array
                #     'tags': tags,  # Return all tags as an array
                #     'tag_count': customer.tag_count if hasattr(customer, 'tag_count') else len(tags),
                #     'gender': customer.gender_id.name if customer.gender_id else None,
                #     'main_interface': customer.main_interface_id.name if customer.main_interface_id else None,
                #     'last_contact_date': customer.last_contact_date.strftime('%d %b %Y %H:%M') if customer.last_contact_date else None,
                #     'total_purchases': customer.total_purchases or 0,
                #     'total_spent': float(customer.total_spent) if customer.total_spent else 0,
                #     'created_on': customer.created_on.strftime('%d %b %Y %H:%M') if customer.created_on else None,
                #     'updated_on': customer.updated_on.strftime('%d %b %Y %H:%M') if customer.updated_on else None,
                #     'created_ago': self._get_time_ago(customer.created_on) if customer.created_on else None,
                #     'updated_ago': self._get_time_ago(customer.updated_on) if customer.updated_on else None,
                # })

                customers.append({
                    # TODO - UPDATE THIS SECTION OF CODES - Access attributes directly from Customer object
                    'customer_id': customer.customer_id,
                    'universal_id': str(customer.universal_id),
                    'name': customer.get_full_name() or customer.name,
                    'email': customer.email,
                    'phone': customer.phone,
                    'customer_type': customer.customer_type,
                    'account_status': customer.account_status,
                    # TODO - UPDATE THIS SECTION OF CODES - Replace single platform with platforms array
                    'platforms': platforms,  # Return all platforms as an array
                    'platform_count': customer.platform_count if hasattr(customer, 'platform_count') else len(platforms),
                    # TODO - UPDATE THIS SECTION OF CODES - Replace single tag with tags array
                    'tags': tags,  # Return all tags as an array
                    'tag_count': customer.tag_count if hasattr(customer, 'tag_count') else len(tags),
                    'gender': customer.gender_id.name if customer.gender_id else None,
                    'main_interface': customer.main_interface_id.name if customer.main_interface_id else None,
                    'last_contact_date': customer.last_contact_date.isoformat() if customer.last_contact_date else None,
                    'total_purchases': customer.total_purchases or 0,
                    'total_spent': float(customer.total_spent) if customer.total_spent else 0,
                    'created_on': customer.created_on.isoformat() if customer.created_on else None,
                    'updated_on': customer.updated_on.isoformat() if customer.updated_on else None,
                    'created_ago': self._get_time_ago(customer.created_on) if customer.created_on else None,
                    'updated_ago': self._get_time_ago(customer.updated_on) if customer.updated_on else None,
                })

            # Use the paginator's get_paginated_response
            response = self.get_paginated_response(customers)

            # Add search context if search was performed
            search_query = request.query_params.get('search', '').strip()
            search_fields_param = request.query_params.get('search_fields', '').strip()

            if search_query:
                search_context = {
                    'search_type': 'general_with_notes',
                    'search_term': search_query,
                    'notes_included': True
                }

                # Add search_fields information if provided
                if search_fields_param:
                    search_fields, _ = self.validate_search_fields(search_fields_param)
                    if search_fields:
                        search_context['search_fields'] = search_fields
                        search_context['search_type'] = 'filtered_fields'
                        # Update notes_included based on whether notes field is included
                        search_context['notes_included'] = 'notes' in search_fields

                response.data['search_context'] = search_context

            return response

        # If pagination is disabled, return all results
        return Response([])
    
    def _get_full_name(self, customer):
        """Helper method to get full name from customer data"""
        first_name = customer.get('first_name') or ''
        last_name = customer.get('last_name') or ''
        if first_name and last_name:
            return f"{first_name} {last_name}".strip()
        return first_name or last_name or None
    
    def _get_time_ago(self, timestamp):
        """
        Convert timestamp to human-readable time ago format
        """
        if not timestamp:
            return None
            
        from django.utils import timezone
        from datetime import timedelta
        
        now = timezone.now()
        diff = now - timestamp
        
        if diff < timedelta(minutes=1):
            return "Just now"
        elif diff < timedelta(hours=1):
            minutes = int(diff.total_seconds() / 60)
            return f"{minutes} minute{'s' if minutes != 1 else ''} ago"
        elif diff < timedelta(days=1):
            hours = int(diff.total_seconds() / 3600)
            return f"{hours} hour{'s' if hours != 1 else ''} ago"
        elif diff < timedelta(days=7):
            days = diff.days
            return f"{days} day{'s' if days != 1 else ''} ago"
        elif diff < timedelta(days=30):
            weeks = diff.days // 7
            return f"{weeks} week{'s' if weeks != 1 else ''} ago"
        elif diff < timedelta(days=365):
            months = diff.days // 30
            return f"{months} month{'s' if months != 1 else ''} ago"
        else:
            years = diff.days // 365
            return f"{years} year{'s' if years != 1 else ''} ago"

class GenderListCreateView(
    LoggingMixin, generics.GenericAPIView, mixins.ListModelMixin, mixins.CreateModelMixin
):

    serializer_class = GenderSerializer
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAdminUser]

    queryset = Gender.objects.all()

    filter_backends = [OrderingFilter]
    ordering_fields = ['gender_id']  # Fields that can be used for ordering
    ordering = ['gender_id']  # Order by gender_id ascending

    def perform_create(self, serializer):
        user = self.request.user
        serializer.save(created_by=user)
        return super().perform_create(serializer)
    
    def get(self, request: Request, *args, **kwargs):
        return self.list(request, *args, **kwargs)

    def post(self, request: Request, *args, **kwargs):

        serializer = self.get_serializer(data=request.data)
        if serializer.is_valid():
            self.perform_create(serializer)
            response = {"message": "Gender Created Successfully", "data": serializer.data}
            return Response(data=response, status=status.HTTP_201_CREATED)
        return Response(data=serializer.errors, status=status.HTTP_400_BAD_REQUEST)

class GenderRetrieveUpdateDeleteView(
    LoggingMixin, generics.GenericAPIView, mixins.RetrieveModelMixin, mixins.UpdateModelMixin, mixins.DestroyModelMixin,
):
    
    serializer_class = GenderSerializer
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAdminUser]

    queryset = Gender.objects.all()

    def perform_update(self, serializer):
        user = self.request.user
        serializer.save(updated_by=user)
        return super().perform_update(serializer)

    def get(self, request: Request, *args, **kwargs):
        return self.retrieve(request, *args, **kwargs)

    def put(self, request: Request, *args, **kwargs):

        partial = kwargs.pop('partial', False)
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=partial)
        if serializer.is_valid():
            self.perform_update(serializer)
            response = {"message": "Gender Updated Successfully", "data": serializer.data}
            return Response(data=response, status=status.HTTP_200_OK)
        return Response(data=serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def delete(self, request: Request, *args, **kwargs):

        instance = self.get_object()
        self.perform_destroy(instance)
        response = {"message": "Gender Deleted Successfully"}
        return Response(data=response, status=status.HTTP_200_OK)

class InterfaceListCreateView(
    LoggingMixin, generics.GenericAPIView, mixins.ListModelMixin, mixins.CreateModelMixin
):

    serializer_class = InterfaceSerializer
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAdminUser]

    queryset = Interface.objects.all()

    filter_backends = [OrderingFilter]
    ordering_fields = ['id']  # Fields that can be used for ordering
    ordering = ['id']  # Order by id ascending

    def perform_create(self, serializer):
        user = self.request.user
        serializer.save(created_by=user)
        return super().perform_create(serializer)
    
    def get(self, request: Request, *args, **kwargs):
        return self.list(request, *args, **kwargs)

    def post(self, request: Request, *args, **kwargs):

        serializer = self.get_serializer(data=request.data)
        if serializer.is_valid():
            self.perform_create(serializer)
            response = {"message": "Interface Created Successfully", "data": serializer.data}
            return Response(data=response, status=status.HTTP_201_CREATED)
        return Response(data=serializer.errors, status=status.HTTP_400_BAD_REQUEST)

class InterfaceRetrieveUpdateDeleteView(
    LoggingMixin, generics.GenericAPIView, mixins.RetrieveModelMixin, mixins.UpdateModelMixin, mixins.DestroyModelMixin,
):
    
    serializer_class = InterfaceSerializer
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAdminUser]

    queryset = Interface.objects.all()

    def perform_update(self, serializer):
        user = self.request.user
        serializer.save(updated_by=user)
        return super().perform_update(serializer)

    def get(self, request: Request, *args, **kwargs):
        return self.retrieve(request, *args, **kwargs)

    def put(self, request: Request, *args, **kwargs):

        partial = kwargs.pop('partial', False)
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=partial)
        if serializer.is_valid():
            self.perform_update(serializer)
            response = {"message": "Interface Updated Successfully", "data": serializer.data}
            return Response(data=response, status=status.HTTP_200_OK)
        return Response(data=serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def delete(self, request: Request, *args, **kwargs):

        instance = self.get_object()
        self.perform_destroy(instance)
        response = {"message": "Interface Deleted Successfully"}
        return Response(data=response, status=status.HTTP_200_OK)

class CustomerNoteList(generics.ListCreateAPIView):
    serializer_class = CustomerNoteSerializer
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]
    filter_backends = [OrderingFilter]
    ordering_fields = ['id', 'created_on', 'updated_on']
    ordering = ['id']

    def get_queryset(self):
        # Filter notes by customer_id if provided
        customer_id = self.request.query_params.get('customer_id', None)
        queryset = CustomerNote.objects.filter(is_active=True)
        if customer_id is not None:
            queryset = queryset.filter(customer_id=customer_id)
        return queryset

    def perform_create(self, serializer):
        serializer.save(
            created_by=self.request.user,
            updated_by=self.request.user
        )

class CustomerNoteDetail(generics.RetrieveUpdateDestroyAPIView):
    queryset = CustomerNote.objects.all()
    serializer_class = CustomerNoteSerializer
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def perform_update(self, serializer):
        serializer.save(updated_by=self.request.user)

    def destroy(self, request, *args, **kwargs):
        instance = self.get_object()
        # Soft delete - just mark as inactive
        instance.is_active = False
        instance.updated_by = request.user
        instance.save()
        return Response(
            {
                "message": f"Note for customer '{instance.customer}' has been deleted successfully",
                "status": "success"
            }, 
            status=status.HTTP_200_OK
        )

class CustomerSpecificNotesView(APIView):
    """
    View for managing a specific customer's notes.
    Allows listing all notes for a customer, and retrieving, updating or deleting specific notes.
    """
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]
    # permission_classes = [IsAuthenticated]

    # def has_permission(self, request, customer):
    #     ticket_id = self.kwargs.get('pk')
    #     try:
    #         return TicketOwnershipPermission().has_object_permission()
    #     except:
    #         return False
    
    def get(self, request, customer_id, note_id=None):
        """
        Get a specific customer's notes or a single note if note_id is provided
        """
        # Verify customer exists
        customer = get_object_or_404(Customer, customer_id=customer_id)
        
        if note_id:
            # Get specific note
            note = get_object_or_404(CustomerNote, id=note_id, customer=customer, is_active=True)
            serializer = CustomerNoteSerializer(note)
            return Response(serializer.data)
        else:
            # Get all notes for this customer
            notes = CustomerNote.objects.filter(customer=customer, is_active=True)
            serializer = CustomerNoteSerializer(notes, many=True)
            return Response(serializer.data)
    
    def post(self, request, customer_id):
        """
        Create a new note for a specific customer
        """
        # Verify customer exists
        customer = get_object_or_404(Customer, customer_id=customer_id)
        
        # Create serializer with customer ID from URL
        data = request.data.copy()
        data['customer'] = customer.customer_id
        
        serializer = CustomerNoteSerializer(data=data)
        if serializer.is_valid():
            serializer.save(
                created_by=request.user,
                updated_by=request.user
            )
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
    
    def put(self, request, customer_id, note_id):
        """
        Update a specific note for a specific customer
        """
        # Verify customer exists
        customer = get_object_or_404(Customer, customer_id=customer_id)
        
        # Get the note and verify it belongs to this customer
        note = get_object_or_404(CustomerNote, id=note_id, customer=customer, is_active=True)
        
        serializer = CustomerNoteSerializer(note, data=request.data, partial=True)
        if serializer.is_valid():
            serializer.save(updated_by=request.user)
            return Response(serializer.data)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
    
    def delete(self, request, customer_id, note_id):
        """
        Soft delete a specific note for a specific customer
        """
        # Verify customer exists
        customer = get_object_or_404(Customer, customer_id=customer_id)
        
        # Get the note and verify it belongs to this customer
        note = get_object_or_404(CustomerNote, id=note_id, customer=customer, is_active=True)
        
        # Soft delete
        note.is_active = False
        note.updated_by = request.user
        note.save()
        
        return Response(
            {
                "message": f"Note '{note.content[:50]}...' for customer '{customer}' has been deleted successfully",
                "status": "success"
            }, 
            status=status.HTTP_200_OK
        )

class GetCustomerTicketsView(APIView):

    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]
    
    def get(self, request, customer_id):
        """
        Get all tickets associated with a specific customer
        
        Args:
            customer_id: ID of the customer to fetch tickets for
            
        Returns:
            List of ticket objects associated with the customer
        """
        try:
            # Verify customer exists
            customer = get_object_or_404(Customer, customer_id=customer_id)
            
            # Get all tickets for the customer
            tickets = Ticket.objects.filter(customer_id=customer)
            
            # Serialize the tickets
            serializer = TicketSerializer(tickets, many=True)
            
            return Response({
                'customer_id': customer_id,
                'customer_name': customer.name,
                'tickets': serializer.data
            }, status=status.HTTP_200_OK)
            
        except Exception as e:
            return Response({
                'error': f'Error fetching tickets: {str(e)}'
            }, status=status.HTTP_400_BAD_REQUEST)

class CustomerFileUploadView(APIView):

    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def post(self, request, customer_id):
        """
        Upload a file for a specific customer
        URL: POST /customer/{customer_id}/files/upload/
        """

        if 'file' not in request.FILES:
            return Response({
                'error': 'No file was submitted. Please include a file with the key "file"'
            }, status=status.HTTP_400_BAD_REQUEST)

        try:
            customer = get_object_or_404(Customer, customer_id=customer_id)
            file = request.FILES['file']
            
            # Get file extension
            filename = file.name.lower()
            allowed_extensions = ['.jpg', '.jpeg', '.png', '.pdf']
            
            # Check both content type and file extension
            allowed_types = ['image/jpeg', 'image/png', 'application/pdf']
            # is_valid_type = False
            
            # Check file extension
            is_valid_extension = any(filename.endswith(ext) for ext in allowed_extensions)
            
            # Check content type
            is_valid_content_type = file.content_type in allowed_types
            
            # # TODO - Delete or Log these
            # print(f"File name: {filename}")
            # print(f"Content type: {file.content_type}")
            # print(f"Valid extension: {is_valid_extension}")
            # print(f"Valid content type: {is_valid_content_type}")
            
            if not (is_valid_extension and is_valid_content_type):
                return Response({
                    'error': 'Invalid file type. Allowed types are: JPEG, PNG, PDF',
                    'received_content_type': file.content_type,
                    'filename': filename
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # Use the customer's upload_file method
            file_url = customer.upload_file(file)
            
            return Response({
                'message': 'File uploaded successfully',
                'url': file_url,
                'filename': file.name,
                'size': file.size
            }, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({
                'error': f'Error uploading file: {str(e)}'
            }, status=status.HTTP_400_BAD_REQUEST)

class CustomerFileDeleteView(APIView):

    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def delete(self, request, customer_id, filename):
        """
        Delete a specific file for a customer
        URL: DELETE /customer/{customer_id}/files/delete/{filename}/
        """
        try:
            # TODO - Delete this or Log this (Print debug information)
            print(f"Attempting to delete file: {filename} for customer: {customer_id}")
            
            customer = get_object_or_404(Customer, customer_id=customer_id)
            
            # Check if file exists before attempting deletion
            files = customer.list_files()
            file_exists = any(file['name'] == filename for file in files)
            
            if not file_exists:
                return Response({
                    'error': f'File {filename} not found for customer {customer_id}'
                }, status=status.HTTP_404_NOT_FOUND)
            
            success = customer.delete_file(filename)
            
            if success:
                return Response({
                    'message': f'File {filename} deleted successfully'
                }, status=status.HTTP_200_OK)
            else:
                return Response({
                    'error': f'Failed to delete file {filename}'
                }, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            return Response({
                'error': f'Error deleting file: {str(e)}'
            }, status=status.HTTP_400_BAD_REQUEST)

class CustomerFileListView(APIView):

    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request, customer_id):
        """
        List all files for a specific customer
        URL: GET /customer/{customer_id}/files/
        """
        try:
            customer = get_object_or_404(Customer, customer_id=customer_id)
            files = customer.list_files()
            
            return Response({
                'files': files
            }, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({
                'error': str(e)
            }, status=status.HTTP_400_BAD_REQUEST)

class CustomerFileDownloadView(APIView):

    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request, customer_id, filename):
        """
        Download a specific file for a customer
        URL: GET /customer/{customer_id}/files/download/{filename}/
        """
        try:
            customer = get_object_or_404(Customer, customer_id=customer_id)
            
            # Check if file exists
            files = customer.list_files()
            
            file_info = next((file for file in files if file['name'] == filename), None)
            
            if not file_info:
                return Response({
                    'error': f'File {filename} not found'
                }, status=status.HTTP_404_NOT_FOUND)

            # Get Azure storage instance
            azure_storage = AzureBlobStorage()
            blob_name = f"{customer.blob_folder}{filename}"
            
            try:
                # Get blob client
                blob_client = azure_storage.container_client.get_blob_client(blob_name)
                
                # Download the blob
                download_stream = blob_client.download_blob()
                content_type = download_stream.properties.content_settings.content_type or 'application/octet-stream'
                
                # Create response
                response = HttpResponse(
                    download_stream.readall(),
                    content_type=content_type
                )
                response['Content-Disposition'] = f'attachment; filename="{filename}"'
                return response
                
            except Exception as azure_error:
                print(f"Azure download error: {str(azure_error)}")
                return Response({
                    'error': f'Error accessing file from storage: {str(azure_error)}'
                }, status=status.HTTP_400_BAD_REQUEST)

        except Exception as e:
            print(f"General error: {str(e)}")
            return Response({
                'error': f'Error downloading file: {str(e)}'
            }, status=status.HTTP_400_BAD_REQUEST)


class CustomerBulkFileDownloadView(APIView):

    authentication_classes = [JWTAuthentication]
    # permission_classes = [IsAuthenticated]
    permission_classes = []

    def get(self, request, customer_id):
        """
        Download multiple files as ZIP for a customer
        URL: POST /customer/{customer_id}/files/files/download-bulk/
        Request body: {"filenames": ["file1.jpg", "file2.pdf"]} (optional)
        If filenames is not provided or empty, downloads all files
        """
        try:
            customer = get_object_or_404(Customer, customer_id=customer_id)
            azure_storage = AzureBlobStorage()
            
            # Get all available files for the customer
            available_files = customer.list_files()
            
            # Get list of files to download
            requested_filenames = request.data.get('filenames', [])
            
            # If no filenames provided, download all files
            files_to_download = available_files
            if requested_filenames:
                files_to_download = [
                    file for file in available_files 
                    if file['name'] in requested_filenames
                ]
            
            if not files_to_download:
                return Response({
                    'error': 'No files found to download'
                }, status=status.HTTP_404_NOT_FOUND)
            
            # Create ZIP file in memory
            zip_buffer = io.BytesIO()
            with zipfile.ZipFile(zip_buffer, 'w', zipfile.ZIP_DEFLATED) as zip_file:
                for file_info in files_to_download:
                    try:
                        filename = file_info['name']
                        blob_name = f"{customer.blob_folder}{filename}"
                        # TODO - Delete this or Log this
                        print(f"Attempting to download blob: {blob_name}")
                        
                        # Get blob client and download
                        blob_client = azure_storage.container_client.get_blob_client(blob_name)
                        download_stream = blob_client.download_blob()
                        
                        # Add file to ZIP
                        zip_file.writestr(filename, download_stream.readall())
                        # TODO - Delete this or Log this
                        print(f"Successfully added {filename} to ZIP")
                        
                    except Exception as e:
                        print(f"Error adding {filename} to ZIP: {str(e)}")
                        continue
            
            # Prepare response
            zip_buffer.seek(0)
            response = HttpResponse(
                zip_buffer.getvalue(),
                content_type='application/zip'
            )
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            response['Content-Disposition'] = f'attachment; filename="customer_{customer_id}_files_{timestamp}.zip"'

            return response

        except Exception as e:
            print(f"General error in bulk download: {str(e)}")
            return Response({
                'error': f'Error downloading files: {str(e)}'
            }, status=status.HTTP_400_BAD_REQUEST)

class CustomerPoliciesView(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]
    
    def get(self, request, customer_id):
        """
        Get all policies (PolicyHolder instances) for a specific customer
        
        Args:
            customer_id: ID of the customer to fetch policies for
            
        Returns:
            List of all policies associated with the customer
        """
        try:
            # Verify customer exists
            customer = get_object_or_404(Customer, customer_id=customer_id)
            
            # # Check if user has permission to view this customer's policies
            # if customer.created_by != request.user and not request.user.is_superuser:
            #     return Response({
            #         'error': 'You do not have permission to view these policies'
            #     }, status=status.HTTP_403_FORBIDDEN)
            
            # Get all policies for the customer
            policies = PolicyHolder.objects.filter(
                customer_id=customer
            ).select_related('product_id').order_by('-issue_date')
            
            # Get policy statistics
            total_policies = policies.count()
            active_policies = policies.filter(policy_status=PolicyHolder.PolicyStatus.ACTIVE).count()
            waiting_policies = policies.filter(policy_status=PolicyHolder.PolicyStatus.WAITING_PERIOD).count()
            expired_policies = policies.filter(policy_status=PolicyHolder.PolicyStatus.EXPIRED).count()
            nearly_expired_policies = policies.filter(policy_status=PolicyHolder.PolicyStatus.NEARLY_EXPIRED).count()
            
            # Serialize the policies
            serializer = PolicyHolderSerializer(policies, many=True)
            
            return Response({
                'customer_id': customer_id,
                'customer_name': customer.name,
                'customer_email': customer.email,
                'policies': serializer.data,
                'statistics': {
                    'total_policies': total_policies,
                    'active_policies': active_policies,
                    'waiting_period_policies': waiting_policies,
                    'expired_policies': expired_policies,
                    'nearly_expired_policies': nearly_expired_policies
                }
            }, status=status.HTTP_200_OK)
            
        except Exception as e:
            return Response({
                'error': f'Error fetching customer policies: {str(e)}'
            }, status=status.HTTP_400_BAD_REQUEST)

class CustomerConversationMessagesView(APIView):
    """
    Get all messages from tickets associated with a specific customer
    """
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]
    
    def get(self, request, customer_id):
        """
        Get all messages from tickets where the customer is specified by customer_id
        
        Args:
            customer_id: ID of the customer to fetch messages for
            
        Returns:
            All messages from all tickets associated with the customer, 
            grouped by ticket
        """
        try:
            # Verify customer exists
            customer = get_object_or_404(Customer, customer_id=customer_id)
            
            # Get all tickets for the customer
            tickets = Ticket.objects.filter(customer_id=customer).order_by('created_on')
            
            if not tickets.exists():
                return Response({
                    'customer_id': customer_id,
                    'customer_name': customer.name,
                    'message': 'No tickets found for this customer',
                    'tickets': []
                }, status=status.HTTP_200_OK)
            
            # Prepare response with messages grouped by ticket
            ticket_messages = []
            for ticket in tickets:
                # Get messages for this ticket
                messages = Message.objects.filter(ticket_id=ticket).order_by('created_on')
                message_serializer = MessageSerializer(messages, many=True)
                
                ticket_messages.append({
                    'ticket_id': ticket.id,
                    'ticket_status': ticket.status_id.name,
                    'created_on': ticket.created_on,
                    'owner': ticket.owner_id.name if ticket.owner_id else None,
                    'messages_count': messages.count(),
                    'messages': message_serializer.data
                })
            
            # Get total message count across all tickets
            total_message_count = sum(item['messages_count'] for item in ticket_messages)
            
            # Create response data
            response_data = {
                'customer_id': customer_id,
                'customer_name': customer.name,
                'email': customer.email,
                'phone': customer.phone,
                'total_tickets': tickets.count(),
                'total_messages': total_message_count,
                'tickets': ticket_messages
            }
            
            return Response(response_data, status=status.HTTP_200_OK)
            
        except Exception as e:
            import traceback
            print(f"Error in CustomerConversationMessagesView.get: {str(e)}")
            print(traceback.format_exc())
            return Response({
                'error': f'Error fetching customer messages: {str(e)}'
            }, status=status.HTTP_400_BAD_REQUEST)
    
    def post(self, request, customer_id):
        """
        Get customer messages as a ZIP file
        
        Request body should contain:
        {
            "format": "zip"
        }
        """
        try:
            # Check request format
            request_format = request.data.get('format')
            
            # Debug information
            print(f"POST request data: {request.data}")
            print(f"Requested format: {request_format}")
            
            if request_format != 'zip':
                return Response({
                    'error': 'Invalid format specified. Use "zip" to download messages as a ZIP file.'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # Verify customer exists
            customer = get_object_or_404(Customer, customer_id=customer_id)
            
            # Get all tickets for the customer
            tickets = Ticket.objects.filter(customer_id=customer).order_by('created_on')
            
            if not tickets.exists():
                return self.generate_empty_zip_file(customer)
            
            # Prepare response with messages grouped by ticket
            ticket_messages = []
            for ticket in tickets:
                # Get messages for this ticket
                messages = Message.objects.filter(ticket_id=ticket).order_by('created_on')
                message_serializer = MessageSerializer(messages, many=True)
                
                ticket_messages.append({
                    'ticket_id': ticket.id,
                    'ticket_status': ticket.status_id.name,
                    'created_on': ticket.created_on,
                    'owner': ticket.owner_id.name if ticket.owner_id else None,
                    'messages_count': messages.count(),
                    'messages': message_serializer.data
                })
            
            # Get total message count across all tickets
            total_message_count = sum(item['messages_count'] for item in ticket_messages)
            
            # Create response data
            response_data = {
                'customer_id': customer_id,
                'customer_name': customer.name,
                'email': customer.email,
                'phone': customer.phone,
                'total_tickets': tickets.count(),
                'total_messages': total_message_count,
                'tickets': ticket_messages
            }
            
            # Generate and return ZIP file
            print("Generating ZIP file...")
            return self.generate_zip_file(customer, tickets, response_data)
            
        except Exception as e:
            import traceback
            print(f"Error in CustomerConversationMessagesView.post: {str(e)}")
            print(traceback.format_exc())
            return Response({
                'error': f'Error generating ZIP file: {str(e)}'
            }, status=status.HTTP_400_BAD_REQUEST)
            
    def generate_empty_zip_file(self, customer):
        """Generate an empty ZIP file when no tickets are found"""
        import io
        import zipfile
        import json
        from datetime import datetime
        from django.http import HttpResponse
        
        # Create ZIP file in memory
        zip_buffer = io.BytesIO()
        
        with zipfile.ZipFile(zip_buffer, 'w', zipfile.ZIP_DEFLATED) as zip_file:
            # Add JSON summary file
            empty_data = {
                'customer_id': customer.customer_id,
                'customer_name': customer.name,
                'message': 'No tickets found for this customer',
                'tickets': []
            }
            
            zip_file.writestr(
                'summary.json', 
                json.dumps(empty_data, indent=2, default=str)
            )
            
            # Add empty CSV
            csv_content = "Ticket ID,Date,Sender,Message\n"
            zip_file.writestr("all_messages.csv", csv_content)
        
        # Prepare response
        zip_buffer.seek(0)
        
        response = HttpResponse(
            zip_buffer.getvalue(),
            content_type='application/zip'
        )
        
        # Set filename with customer name and date
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        customer_name = customer.name.replace(" ", "_") if customer.name else f"customer_{customer.customer_id}"
        filename = f"{customer_name}_messages_{timestamp}.zip"
        
        response['Content-Disposition'] = f'attachment; filename="{filename}"'
        
        return response
    
    def generate_zip_file(self, customer, tickets, response_data):
        """
        Generate a ZIP file containing customer message history
        
        Args:
            customer: Customer object
            tickets: QuerySet of Ticket objects
            response_data: Dict containing the JSON response data
            
        Returns:
            HttpResponse with ZIP file
        """
        
        # Create ZIP file in memory
        zip_buffer = io.BytesIO()
        
        try:
            with zipfile.ZipFile(zip_buffer, 'w', zipfile.ZIP_DEFLATED) as zip_file:
                # Add JSON summary file
                zip_file.writestr(
                    'summary.json', 
                    json.dumps(response_data, indent=2, default=str)
                )
                
                # Add a text file for each ticket with all messages
                for ticket_data in response_data['tickets']:
                    ticket_id = ticket_data['ticket_id']
                    ticket_status = ticket_data['ticket_status']
                    ticket_created = ticket_data['created_on']
                    
                    # Create text file with ticket info and messages
                    text_content = [
                        f"Ticket ID: {ticket_id}",
                        f"Status: {ticket_status}",
                        f"Created: {ticket_created}",
                        f"Owner: {ticket_data['owner']}",
                        f"Total Messages: {ticket_data['messages_count']}",
                        "\n--- MESSAGES ---\n"
                    ]
                    
                    # Add all messages to the text file
                    for msg in ticket_data['messages']:
                        # sender = "Customer" if not msg['is_self'] else "Agent"
                        sender = "Customer" if not msg['is_self'] else msg['user_name']
                        created_date = msg.get('created_on', 'Unknown date')
                        message_text = msg['message']
                        
                        text_content.append(f"[{created_date}] {sender}: {message_text}")
                        text_content.append("-" * 40)
                    
                    # Write the text file to the ZIP
                    zip_file.writestr(
                        f"ticket_{ticket_id}.txt",
                        "\n".join(text_content)
                    )
                
                # Add message history as CSV
                csv_content = io.StringIO()
                csv_content.write("Ticket ID,Date,Sender,Message\n")
                
                for ticket_data in response_data['tickets']:
                    ticket_id = ticket_data['ticket_id']
                    for msg in ticket_data['messages']:
                        sender = "Customer" if not msg['is_self'] else "Agent"
                        created_date = msg.get('created_on', '')
                        # Handle CSV escaping for message text (quote with double quotes)
                        # message_text = f'"{msg["message"].replace("\"", "\"\"")}"'
                        message_text = msg["message"].replace('"','\\"')
                        
                        csv_content.write(f"{ticket_id},{created_date},{sender},{message_text}\n")
                
                zip_file.writestr("all_messages.csv", csv_content.getvalue())
            
            # Prepare response
            zip_buffer.seek(0)
            
            # Create the HTTP response with the ZIP file
            response = HttpResponse(
                zip_buffer.getvalue(),
                content_type='application/zip'
            )
            
            # Set filename with customer name and date
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            customer_name = customer.name.replace(" ", "_") if customer.name else f"customer_{customer.customer_id}"
            filename = f"{customer_name}_messages_{timestamp}.zip"
            
            response['Content-Disposition'] = f'attachment; filename="{filename}"'
            
            return response
        
        except Exception as e:
            print(f"Error generating ZIP file: {str(e)}")
            import traceback
            print(traceback.format_exc())
            raise

class CustomerTagListCreateView(
    LoggingMixin, generics.GenericAPIView, mixins.ListModelMixin, mixins.CreateModelMixin
):
    serializer_class = CustomerTagSerializer
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAdminUser]

    queryset = CustomerTag.objects.all()

    filter_backends = [OrderingFilter]
    ordering_fields = ['id']
    ordering = ['id']

    def perform_create(self, serializer):
        user = self.request.user
        serializer.save(created_by=user)
        return super().perform_create(serializer)
    
    def get(self, request: Request, *args, **kwargs):
        return self.list(request, *args, **kwargs)

    def post(self, request: Request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        if serializer.is_valid():
            self.perform_create(serializer)
            response = {"message": "CustomerTag Created Successfully", "data": serializer.data}
            return Response(data=response, status=status.HTTP_201_CREATED)
        return Response(data=serializer.errors, status=status.HTTP_400_BAD_REQUEST)

class CustomerTagRetrieveUpdateDeleteView(
    LoggingMixin, generics.GenericAPIView, mixins.RetrieveModelMixin, mixins.UpdateModelMixin, mixins.DestroyModelMixin,
):
    serializer_class = CustomerTagSerializer
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAdminUser]

    queryset = CustomerTag.objects.all()

    def perform_update(self, serializer):
        user = self.request.user
        serializer.save(updated_by=user)
        return super().perform_update(serializer)

    def get(self, request: Request, *args, **kwargs):
        return self.retrieve(request, *args, **kwargs)

    def put(self, request: Request, *args, **kwargs):
        partial = kwargs.pop('partial', False)
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=partial)
        if serializer.is_valid():
            self.perform_update(serializer)
            response = {"message": "CustomerTag Updated Successfully", "data": serializer.data}
            return Response(data=response, status=status.HTTP_200_OK)
        return Response(data=serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def delete(self, request: Request, *args, **kwargs):
        instance = self.get_object()
        self.perform_destroy(instance)
        response = {"message": "CustomerTag Deleted Successfully"}
        return Response(data=response, status=status.HTTP_200_OK)

class CustomerTagsView(APIView):
    """
    API view for managing a customer's tags
    GET: Retrieve a list of tags assigned to a customer
    POST: Assign tags to a customer
    PUT: Update a customer's tags
    DELETE: Remove tags from a customer
    """
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]
    
    def get(self, request, customer_id):
        """Get tags assigned to a specific customer"""
        try:
            customer = get_object_or_404(Customer, customer_id=customer_id)
            tags = customer.customer_tags.all()
            serializer = CustomerTagBasicSerializer(tags, many=True)
            
            return Response({
                'customer_id': customer_id,
                'tags': serializer.data,
                'total_tags': tags.count()
            }, status=status.HTTP_200_OK)
            
        except Exception as e:
            return Response({
                'error': f'Error fetching customer tags: {str(e)}'
            }, status=status.HTTP_400_BAD_REQUEST)
    
    def post(self, request, customer_id):
        """Assign tags to a customer"""
        try:
            customer = get_object_or_404(Customer, customer_id=customer_id)
            tag_ids = request.data.get('tag_ids', [])
            
            if not tag_ids:
                return Response({
                    'error': 'No tag_ids provided'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # Validate tag IDs
            tags = CustomerTag.objects.filter(id__in=tag_ids)
            if len(tags) != len(tag_ids):
                missing_ids = set(tag_ids) - set(tags.values_list('id', flat=True))
                return Response({
                    'error': f'Tags with IDs {missing_ids} not found'
                }, status=status.HTTP_404_NOT_FOUND)
            
            # Add tags to customer
            customer.customer_tags.add(*tags)
            
            # Return updated tags
            updated_tags = customer.customer_tags.all()
            serializer = CustomerTagBasicSerializer(updated_tags, many=True)
            
            return Response({
                'message': 'Tags assigned successfully',
                'customer_id': customer_id,
                'tags': serializer.data,
                'total_tags': updated_tags.count()
            }, status=status.HTTP_200_OK)
            
        except Exception as e:
            return Response({
                'error': f'Error assigning tags: {str(e)}'
            }, status=status.HTTP_400_BAD_REQUEST)
    
    def put(self, request, customer_id):
        """Replace all tags for a customer"""
        try:
            customer = get_object_or_404(Customer, customer_id=customer_id)
            tag_ids = request.data.get('tag_ids', [])
            
            # Validate tag IDs (if any provided)
            if tag_ids:
                tags = CustomerTag.objects.filter(id__in=tag_ids)
                if len(tags) != len(tag_ids):
                    missing_ids = set(tag_ids) - set(tags.values_list('id', flat=True))
                    return Response({
                        'error': f'Tags with IDs {missing_ids} not found'
                    }, status=status.HTTP_404_NOT_FOUND)
                
                # Clear existing tags and set new ones
                customer.customer_tags.clear()
                customer.customer_tags.add(*tags)
            else:
                # If no tags provided, clear all
                customer.customer_tags.clear()
            
            # Return updated tags
            updated_tags = customer.customer_tags.all()
            serializer = CustomerTagBasicSerializer(updated_tags, many=True)
            
            return Response({
                'message': 'Tags updated successfully',
                'customer_id': customer_id,
                'tags': serializer.data,
                'total_tags': updated_tags.count()
            }, status=status.HTTP_200_OK)
            
        except Exception as e:
            return Response({
                'error': f'Error updating tags: {str(e)}'
            }, status=status.HTTP_400_BAD_REQUEST)

    def delete(self, request, customer_id):
        """Remove tags from a customer"""
        try:
            customer = get_object_or_404(Customer, customer_id=customer_id)
            tag_ids = request.data.get('tag_ids', [])
            
            if not tag_ids:
                return Response({
                    'error': 'No tag_ids provided'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # Validate tag IDs
            tags = CustomerTag.objects.filter(id__in=tag_ids)
            if not tags.exists():
                return Response({
                    'error': 'No valid tags to remove'
                }, status=status.HTTP_404_NOT_FOUND)
            
            # Remove tags from customer
            customer.customer_tags.remove(*tags)
            
            # Return remaining tags
            remaining_tags = customer.customer_tags.all()
            serializer = CustomerTagBasicSerializer(remaining_tags, many=True)
            
            return Response({
                'message': 'Tags removed successfully',
                'customer_id': customer_id,
                'tags': serializer.data,
                'total_tags': remaining_tags.count()
            }, status=status.HTTP_200_OK)
            
        except Exception as e:
            return Response({
                'error': f'Error removing tags: {str(e)}'
            }, status=status.HTTP_400_BAD_REQUEST)
        
# TODO - Display a customer's image
class CustomerMessageHistoryView(APIView):
    """
    API endpoint for retrieving a customer's message history across multiple tickets,
    filtered by interface type and with pagination support.
    """
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]
    
    def get(self, request, customer_id):
        """
        Get paginated message history for a specific customer across all tickets
        
        Query Parameters:
            interface: Filter messages by interface type (e.g., 'LINE', 'WHATSAPP')
            limit: Number of messages to return (default: 20)
            before_id: Return messages before this message ID
            before_timestamp: Return messages before this timestamp
        
        Returns:
            Paginated list of messages from the customer's conversation history
        """
        try:
            # Verify customer exists
            customer = get_object_or_404(Customer, customer_id=customer_id)
            
            # Parse query parameters
            interface_name = request.query_params.get('interface')
            limit = int(request.query_params.get('limit', 20))
            before_id = request.query_params.get('before_id')
            before_timestamp = request.query_params.get('before_timestamp')

            # interface_name = request.data.get('interface')
            # limit = int(request.query_params.get('limit', 20))
            # before_id = request.query_params.get('before_id')
            # before_timestamp = request.query_params.get('before_timestamp')
            
            # Validate limit
            if limit <= 0 or limit > 100:
                return Response({
                    'error': 'Limit must be between 1 and 100'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # Get tickets for this customer
            tickets_query = Ticket.objects.filter(customer_id=customer)
            
            # Filter by interface if specified
            if interface_name:
                try:
                    interface = Interface.objects.get(name=interface_name)
                    tickets_query = tickets_query.filter(ticket_interface=interface)
                except Interface.DoesNotExist:
                    return Response({
                        'error': f'Interface "{interface_name}" not found'
                    }, status=status.HTTP_404_NOT_FOUND)
            
            # Get all ticket IDs for this customer
            ticket_ids = tickets_query.values_list('id', flat=True)
            
            if not ticket_ids:
                # return Response({
                #     'customer_id': customer_id,
                #     'customer_name': customer.name,
                #     'messages': [],
                #     'has_more': False
                # }, status=status.HTTP_200_OK)

                return Response({
                    'customer': {
                        'id': customer.customer_id,
                        'name': customer.name,
                        'email': customer.email,
                        'phone': customer.phone,
                        'line_user_id': customer.line_user_id.line_user_id if customer.line_user_id else None,
                        'line_display_name': customer.line_user_id.display_name if customer.line_user_id else None,
                        'gender': customer.gender_id.name if customer.gender_id else None,
                        'interface': customer.main_interface_id.name if customer.main_interface_id else None,
                    },
                    'messages': [],
                    'has_more': False
                }, status=status.HTTP_200_OK)
            
            # Initialize message query
            message_query = Message.objects.filter(ticket_id__in=ticket_ids)
            
            # Apply pagination filters
            if before_id:
                try:
                    # Get the message to use as reference point
                    reference_message = Message.objects.get(id=before_id)
                    message_query = message_query.filter(created_on__lt=reference_message.created_on)
                except Message.DoesNotExist:
                    return Response({
                        'error': f'Message with ID {before_id} not found'
                    }, status=status.HTTP_404_NOT_FOUND)
            elif before_timestamp:
                try:
                    timestamp = datetime.fromisoformat(before_timestamp.replace('Z', '+00:00'))
                    message_query = message_query.filter(created_on__lt=timestamp)
                except ValueError:
                    return Response({
                        'error': 'Invalid timestamp format. Use ISO format (YYYY-MM-DDTHH:MM:SS.sssZ)'
                    }, status=status.HTTP_400_BAD_REQUEST)
            
            # Order by creation date (descending) and limit
            message_query = message_query.order_by('-created_on')[:limit+1]  # Get one extra to check if there are more
            
            # Convert to list to check if there are more messages
            messages = list(message_query)
            has_more = len(messages) > limit
            
            if has_more:
                messages = messages[:limit]  # Remove the extra message
            
            # Serialize messages
            serializer = MessageSerializer(messages, many=True)
            
            # Add ticket context to each message
            messages_with_context = []
            for message, message_data in zip(messages, serializer.data):
                ticket = message.ticket_id
                messages_with_context.append({
                    **message_data,
                    'ticket_id': ticket.id,
                    'ticket_status': ticket.status_id.name,
                    'ticket_created_on': ticket.created_on,
                    'ticket_owner': ticket.owner_id.name if ticket.owner_id else None
                })
            
            # Return the result

            # return Response({
            #     'customer_id': customer_id,
            #     'customer_name': customer.name,
            #     'interface': interface_name,
            #     'messages': messages_with_context,
            #     'has_more': has_more,
            #     'oldest_message_id': messages[-1].id if messages else None,
            #     'oldest_timestamp': messages[-1].created_on.isoformat() if messages else None
            # }, status=status.HTTP_200_OK)

            return Response({
                'customer': {
                    'id': customer.customer_id,
                    'name': customer.name,
                    'email': customer.email,
                    'phone': customer.phone,
                    'line_user_id': customer.line_user_id.line_user_id if customer.line_user_id else None,
                    'line_display_name': customer.line_user_id.display_name if customer.line_user_id else None,
                    'gender': customer.gender_id.name if customer.gender_id else None,
                    'main_interface': customer.main_interface_id.name if customer.main_interface_id else None,
                },
                'interface': interface_name,
                'messages': messages_with_context,
                'has_more': has_more,
                'oldest_message_id': messages[-1].id if messages else None,
                'oldest_timestamp': messages[-1].created_on.isoformat() if messages else None
            }, status=status.HTTP_200_OK)
            
        except Exception as e:
            return Response({
                'error': f'Error fetching customer message history: {str(e)}'
            }, status=status.HTTP_400_BAD_REQUEST)
        
class CustomerMemoryListCreateView(generics.ListCreateAPIView):
    serializer_class = CustomerMemorySerializer
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]
    
    queryset = CustomerMemory.objects.all().order_by('created_on')
    
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['customer', 'is_important', 'relation_type']
    search_fields = ['entity_one', 'entity_two', 'detail_en', 'detail_th']
    ordering_fields = ['created_on', 'is_important']
    
    def get_queryset(self):
        queryset = super().get_queryset()
        
        # Filter by ticket if provided
        ticket_id = self.request.query_params.get('ticket_id')
        if ticket_id:
            queryset = queryset.filter(ticket__id=ticket_id)
            
        return queryset
    
    def perform_create(self, serializer):
        # Track the user who created the memory
        serializer.save(created_by=self.request.user)

    def post(self, request, *args, **kwargs):
        # Check if customer exists
        customer_id = request.data.get('customer')
        if customer_id:
            try:
                customer = Customer.objects.get(customer_id=customer_id)
            except Customer.DoesNotExist:
                return Response({
                    "status": "error",
                    "message": f"Customer with ID {customer_id} not found. Memory not created."
                }, status=status.HTTP_200_OK)  # Return 200 instead of 404
        
        # Check if ticket exists
        ticket_id = request.data.get('ticket')
        if ticket_id:
            from ticket.models import Ticket
            try:
                ticket = Ticket.objects.get(id=ticket_id)
            except Ticket.DoesNotExist:
                return Response({
                    "status": "error",
                    "message": f"Ticket with ID {ticket_id} not found. Memory not created."
                }, status=status.HTTP_200_OK)  # Return 200 instead of 404

        serializer = self.get_serializer(data=request.data)
        if serializer.is_valid():
            self.perform_create(serializer)
            response = {"message": "Customer's Memory Created Successfully", "data": serializer.data}
            return Response(data=response, status=status.HTTP_201_CREATED)
        return Response(data=serializer.errors, status=status.HTTP_400_BAD_REQUEST)

class CustomerMemoryDetailView(generics.RetrieveUpdateDestroyAPIView):
    serializer_class = CustomerMemorySerializer
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]
    
    queryset = CustomerMemory.objects.all()

    def perform_update(self, serializer):
        # Track the user who updated the memory
        serializer.save(updated_by=self.request.user)

class CustomerMemoriesView(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]
    
    def get(self, request, customer_id):
        """
        Get all memories for a specific customer
        """
        try:
            customer = Customer.objects.get(customer_id=customer_id)
            memories = CustomerMemory.objects.filter(customer=customer).order_by('-created_on')
            
            # Apply filtering
            is_important = request.query_params.get('is_important')
            if is_important is not None:
                is_important_bool = is_important.lower() == 'true'
                memories = memories.filter(is_important=is_important_bool)
                
            relation_type = request.query_params.get('relation_type')
            if relation_type:
                memories = memories.filter(relation_type=relation_type)
                
            # Apply search
            search = request.query_params.get('search')
            if search:
                memories = memories.filter(
                    models.Q(entity_one__icontains=search) |
                    models.Q(entity_two__icontains=search) |
                    models.Q(detail_en__icontains=search) |
                    models.Q(detail_th__icontains=search)
                )
            
            serializer = CustomerMemorySerializer(memories, many=True)
            
            return Response({
                'customer_id': customer_id,
                'count': memories.count(),
                'memories': serializer.data
            })
            
        except Customer.DoesNotExist:
            return Response({
                'error': f'Customer with ID {customer_id} not found'
            }, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            return Response({
                'error': f'Error retrieving customer memories: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

class TriggerMemoryExtractionView(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]
    
    def post(self, request, ticket_id):
        """
        Manually trigger memory extraction for a ticket
        """
        from ticket.models import Ticket
        
        try:
            ticket = Ticket.objects.get(id=ticket_id)
            
            # Extract memories
            memories = CustomerMemoryService.extract_memories_from_ticket(
                ticket=ticket,
                user=request.user
            )
            
            if memories:
                return Response({
                    'success': True,
                    'message': f'Successfully extracted {len(memories)} memories',
                    'memories': CustomerMemorySerializer(memories, many=True).data
                })
            else:
                return Response({
                    'success': False,
                    'message': 'No memories extracted'
                }, status=status.HTTP_422_UNPROCESSABLE_ENTITY)
                
        except Ticket.DoesNotExist:
            return Response({
                'error': f'Ticket with ID {ticket_id} not found'
            }, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            return Response({
                'error': f'Error extracting memories: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        
class CustomerDetailView(generics.RetrieveAPIView):
    """Get customer details with all platform identities."""
    
    # permission_classes = [IsAuthenticated]
    serializer_class = CustomerWithIdentitiesSerializer
    queryset = Customer.objects.all()
    lookup_field = 'customer_id'


# class CustomerPlatformIdentitiesView(generics.ListAPIView):
#     """List all platform identities for a customer."""
    
#     # permission_classes = [IsAuthenticated]
#     serializer_class = CustomerPlatformIdentitySerializer
    
#     def get_queryset(self):
#         customer_id = self.kwargs['customer_id']
#         return CustomerPlatformIdentity.objects.filter(
#             customer__customer_id=customer_id,
#             is_active=True
#         ).order_by('-last_interaction')


class GenerateLinkingCodeView(APIView):
    """Generate a linking code for account linking."""
    
    permission_classes = [IsAuthenticated]
    
    def post(self, request, customer_id):
        """Generate linking code for specified customer."""
        try:
            customer = get_object_or_404(Customer, customer_id=customer_id)
            
            # Check permissions - only staff or the customer themselves
            # TODO: Add proper permission checking based on your auth model
            
            result = CustomerLinkingService.generate_linking_code(customer)
            
            serializer = LinkingCodeResponseSerializer(data=result)
            serializer.is_valid()
            
            return Response(serializer.data, status=status.HTTP_200_OK)
            
        except Exception as e:
            logger.error(f"Error generating linking code: {str(e)}")
            return Response(
                {'success': False, 'error': str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class ValidateLinkingCodeView(APIView):
    """Validate a linking code without executing the link."""
    
    permission_classes = []  # Public endpoint
    
    def post(self, request):
        """Validate linking code."""
        serializer = LinkingValidationSerializer(data=request.data)
        
        if not serializer.is_valid():
            return Response(
                {'valid': False, 'errors': serializer.errors},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        result = CustomerLinkingService.validate_linking_code(
            code=serializer.validated_data['code'],
            platform=serializer.validated_data['platform'],
            platform_user_id=serializer.validated_data['platform_user_id'],
            provider_id=serializer.validated_data.get('provider_id'),
            channel_id=serializer.validated_data.get('channel_id')
        )
        
        return Response(result, status=status.HTTP_200_OK)


class LinkAccountsView(APIView):
    """Execute account linking using a valid code."""
    
    permission_classes = []  # Public endpoint
    
    def post(self, request):
        """Execute account linking."""
        serializer = LinkingExecutionSerializer(data=request.data)
        
        if not serializer.is_valid():
            return Response(
                {'success': False, 'errors': serializer.errors},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Get user if authenticated
        initiated_by = request.user if request.user.is_authenticated else None
        
        result = CustomerLinkingService.execute_linking(
            code=serializer.validated_data['code'],
            platform=serializer.validated_data['platform'],
            platform_user_id=serializer.validated_data['platform_user_id'],
            provider_id=serializer.validated_data.get('provider_id'),
            channel_id=serializer.validated_data.get('channel_id'),
            display_name=serializer.validated_data.get('display_name'),
            platform_data=serializer.validated_data.get('platform_data', {}),
            initiated_by=initiated_by
        )
        
        if result['success']:
            return Response(result, status=status.HTTP_200_OK)
        else:
            return Response(result, status=status.HTTP_400_BAD_REQUEST)


class UnlinkPlatformIdentityView(APIView):
    """Unlink a platform identity from a customer."""
    
    permission_classes = [IsAuthenticated]
    
    def post(self, request, customer_id):
        """Unlink platform identity."""
        customer = get_object_or_404(Customer, customer_id=customer_id)
        
        serializer = UnlinkPlatformSerializer(data=request.data)
        
        if not serializer.is_valid():
            return Response(
                {'success': False, 'errors': serializer.errors},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        result = CustomerLinkingService.unlink_platform_identity(
            customer=customer,
            platform_identity_id=serializer.validated_data['platform_identity_id'],
            reason=serializer.validated_data.get('reason')
        )
        
        if result['success']:
            return Response(result, status=status.HTTP_200_OK)
        else:
            return Response(result, status=status.HTTP_400_BAD_REQUEST)


class CustomerLinkingHistoryView(generics.ListAPIView):
    """View linking history for a customer."""
    
    permission_classes = [IsAuthenticated]
    serializer_class = CustomerLinkingHistorySerializer
    
    def get_queryset(self):
        customer_id = self.kwargs['customer_id']
        return CustomerLinkingHistory.objects.filter(
            primary_customer__customer_id=customer_id
        ).order_by('-created_on')
    
class CustomerChatCenterListView(APIView):
    def get(self, request):
        include_activity = request.query_params.get('include_activity', 'false').lower() == 'true'
        
        customers = Customer.objects.filter(
            account_status='ACTIVE'
        ).prefetch_related(
            'platform_identities',
            'customer_tags'
        )
        
        customer_data = []
        for customer in customers:
            data = {
                'customer_id': customer.customer_id,
                'name': customer.get_full_name(),
                'email': customer.email,
                'phone': customer.phone,
                'customer_type': customer.customer_type,
                'platforms': [
                    {
                        'platform': p.platform,
                        'verified': p.is_verified,
                        'last_interaction': p.last_interaction
                    }
                    for p in customer.platform_identities.filter(is_active=True)
                ],
                'tags': [tag.name for tag in customer.customer_tags.all()]
            }
            
            if include_activity:
                # Add activity data
                recent_ticket = customer.ticket_customer.order_by('-updated_on').first()
                data.update({
                    'last_message_time': customer.last_message_time,
                    'open_tickets': customer.ticket_customer.exclude(
                        status_id__name='closed'
                    ).count(),
                    'total_messages': Message.objects.filter(
                        ticket_id__customer_id=customer
                    ).count(),
                    'latest_ticket_status': recent_ticket.status_id.name if recent_ticket else None
                })
            
            customer_data.append(data)
        
        return Response({
            'customers': customer_data,
            'total': len(customer_data)
        })

# # Version 01 - Return only messages according to a customer's platform_identity
# class CustomerPlatformMessagesView(APIView):
#     """
#     Get messages for a specific customer platform identity
#     URL: GET /api/customers/{customer_id}/platforms/{platform_id}/messages/
#     """
#     # authentication_classes = [JWTAuthentication]
#     # permission_classes = [IsAuthenticated]
    
#     def get(self, request: Request, customer_id: int, platform_id: int):
#         try:
#             # Verify customer exists
#             customer = Customer.objects.get(customer_id=customer_id)
            
#             # Verify platform identity belongs to customer
#             platform_identity = CustomerPlatformIdentity.objects.get(
#                 id=platform_id,
#                 customer=customer,
#                 is_active=True
#             )
            
#             # Get query parameters
#             limit = int(request.query_params.get('limit', 50))
#             before_message_id = request.query_params.get('before_message_id')
#             after_message_id = request.query_params.get('after_message_id')
            
#             # Build query
#             messages_query = Message.objects.filter(
#                 platform_identity=platform_identity
#             ).select_related(
#                 'ticket_id',
#                 'created_by',
#                 'platform_identity'
#             ).order_by('-created_on')
            
#             # Apply filters
#             if before_message_id:
#                 messages_query = messages_query.filter(id__lt=before_message_id)
            
#             if after_message_id:
#                 messages_query = messages_query.filter(id__gt=after_message_id)
            
#             # Get messages with limit + 1 to check if there are more
#             messages = list(messages_query[:limit + 1])
#             has_more = len(messages) > limit
            
#             # Trim to actual limit
#             if has_more:
#                 messages = messages[:limit]
            
#             # Reverse to get chronological order
#             messages.reverse()
            
#             # Serialize messages
#             serializer = ChatCenterMessageSerializer(messages, many=True)
            
#             # Get user profile images
#             for msg_data, msg_obj in zip(serializer.data, messages):
#                 # For messages from system/agents
#                 if msg_obj.is_self and msg_obj.created_by:
#                     if hasattr(msg_obj.created_by, 'line_user_id') and msg_obj.created_by.line_user_id:
#                         msg_data['user_image_url'] = msg_obj.created_by.line_user_id.picture_url
#                 # For messages from customers
#                 elif not msg_obj.is_self:
#                     if platform_identity.picture_url:
#                         msg_data['user_image_url'] = platform_identity.picture_url
            
#             return Response({
#                 'messages': serializer.data,
#                 'has_more': has_more,
#                 'platform': {
#                     'id': platform_identity.id,
#                     'platform': platform_identity.platform,
#                     'display_name': platform_identity.display_name,
#                     'channel_name': platform_identity.channel_name
#                 }
#             })
            
#         except Customer.DoesNotExist:
#             return Response(
#                 {'error': 'Customer not found'},
#                 status=status.HTTP_404_NOT_FOUND
#             )
#         except CustomerPlatformIdentity.DoesNotExist:
#             return Response(
#                 {'error': 'Platform identity not found or does not belong to customer'},
#                 status=status.HTTP_404_NOT_FOUND
#             )
#         except Exception as e:
#             logger.error(f"Error getting platform messages: {str(e)}")
#             return Response(
#                 {'error': str(e)},
#                 status=status.HTTP_500_INTERNAL_SERVER_ERROR
#             )

# # Version 02
# class CustomerPlatformMessagesView(APIView):
#     """
#     Get messages for a specific customer platform identity
#     URL: GET /api/customers/{customer_id}/platforms/{platform_id}/messages/
#     """
    
#     def get(self, request: Request, customer_id: int, platform_id: int):
#         try:
#             # Verify customer exists
#             customer = Customer.objects.get(customer_id=customer_id)
            
#             # Verify platform identity belongs to customer
#             platform_identity = CustomerPlatformIdentity.objects.get(
#                 id=platform_id,
#                 customer=customer,
#                 is_active=True
#             )
            
#             # Get query parameters
#             limit = int(request.query_params.get('limit', 50))
#             before_message_id = request.query_params.get('before_message_id')
#             after_message_id = request.query_params.get('after_message_id')
            
#             # Build query - Get all messages from tickets that have messages from this platform identity
#             # First, get all ticket IDs that have messages from this platform
#             ticket_ids = Message.objects.filter(
#                 platform_identity=platform_identity
#             ).values_list('ticket_id', flat=True).distinct()
            
#             # Then get ALL messages from those tickets
#             messages_query = Message.objects.filter(
#                 ticket_id__in=ticket_ids
#             ).select_related(
#                 'ticket_id',
#                 'created_by',
#                 'platform_identity'
#             ).order_by('-created_on')
            
#             # Apply filters
#             if before_message_id:
#                 messages_query = messages_query.filter(id__lt=before_message_id)
            
#             if after_message_id:
#                 messages_query = messages_query.filter(id__gt=after_message_id)
            
#             # Get messages with limit + 1 to check if there are more
#             messages = list(messages_query[:limit + 1])
#             has_more = len(messages) > limit
            
#             # Trim to actual limit
#             if has_more:
#                 messages = messages[:limit]
            
#             # Reverse to get chronological order
#             messages.reverse()
            
#             # Serialize messages
#             serializer = ChatCenterMessageSerializer(messages, many=True)
            
#             # Get user profile images
#             for msg_data, msg_obj in zip(serializer.data, messages):
#                 # For messages from system/agents
#                 if msg_obj.is_self and msg_obj.created_by:
#                     if hasattr(msg_obj.created_by, 'line_user_id') and msg_obj.created_by.line_user_id:
#                         msg_data['user_image_url'] = msg_obj.created_by.line_user_id.picture_url
#                 # For messages from customers
#                 elif not msg_obj.is_self:
#                     if platform_identity.picture_url:
#                         msg_data['user_image_url'] = platform_identity.picture_url
            
#             return Response({
#                 'messages': serializer.data,
#                 'has_more': has_more,
#                 'platform': {
#                     'id': platform_identity.id,
#                     'platform': platform_identity.platform,
#                     'display_name': platform_identity.display_name,
#                     'channel_name': platform_identity.channel_name
#                 }
#             })
            
#         except Customer.DoesNotExist:
#             return Response(
#                 {'error': 'Customer not found'},
#                 status=status.HTTP_404_NOT_FOUND
#             )
#         except CustomerPlatformIdentity.DoesNotExist:
#             return Response(
#                 {'error': 'Platform identity not found or does not belong to customer'},
#                 status=status.HTTP_404_NOT_FOUND
#             )
#         except Exception as e:
#             logger.error(f"Error getting platform messages: {str(e)}")
#             return Response(
#                 {'error': str(e)},
#                 status=status.HTTP_500_INTERNAL_SERVER_ERROR
#             )


# class CustomerPlatformSendMessageView(APIView):
#     """
#     Send a message through a specific platform
#     URL: POST /api/customers/{customer_id}/platforms/{platform_id}/send/
#     """
#     # authentication_classes = [JWTAuthentication]
#     # permission_classes = [IsAuthenticated]
    
#     def post(self, request: Request, customer_id: int, platform_id: int):
#         try:
#             # Verify customer exists
#             customer = Customer.objects.get(customer_id=customer_id)
            
#             # Verify platform identity belongs to customer
#             platform_identity = CustomerPlatformIdentity.objects.get(
#                 id=platform_id,
#                 customer=customer,
#                 is_active=True
#             )
            
#             # Get message content
#             message_content = request.data.get('message')
#             message_type = request.data.get('message_type', 'TEXT')
#             metadata = request.data.get('metadata', {})
            
#             if not message_content:
#                 return Response(
#                     {'error': 'Message content is required'},
#                     status=status.HTTP_400_BAD_REQUEST
#                 )
            
#             # Get or create ticket for this conversation
#             ticket = self._get_or_create_ticket(customer, platform_identity, request.user)
            
#             # Create message record first
#             message = Message.objects.create(
#                 ticket_id=ticket,
#                 message=message_content,
#                 user_name=request.user.name,
#                 is_self=True,  # Message from agent
#                 message_type=message_type,
#                 status=Message.MessageStatus.SENDING,
#                 platform_identity=platform_identity,
#                 metadata={
#                     **metadata,
#                     'sent_by_user_id': request.user.id,
#                     'sent_via': 'web_interface'
#                 },
#                 created_by=request.user
#             )
            
#             # Use PlatformRoutingService to send the message
#             routing_result = PlatformRoutingService.route_message_to_customer(
#                 customer=customer,
#                 message_content=message_content,
#                 message_type=message_type,
#                 preferred_platform_identity_id=platform_id,
#                 metadata={
#                     'ticket_id': ticket.id,
#                     'message_id': message.id,
#                     **metadata
#                 }
#             )
            
#             # Update message status based on routing result
#             if routing_result['success']:
#                 message.status = Message.MessageStatus.DELIVERED
#                 message.delivered_on = timezone.now()
#             else:
#                 message.status = Message.MessageStatus.FAILED
#                 message.metadata['error'] = routing_result.get('error', 'Unknown error')
            
#             message.save()
            
#             # Serialize and return
#             serializer = ChatCenterMessageSerializer(message)
#             response_data = serializer.data
            
#             # Add user image URL
#             if hasattr(request.user, 'line_user_id') and request.user.line_user_id:
#                 response_data['user_image_url'] = request.user.line_user_id.picture_url
            
#             # Broadcast to WebSocket
#             from ticket.tasks import broadcast_to_websocket
#             broadcast_to_websocket.delay(
#                 ticket_id=ticket.id,
#                 message_id=message.id,
#                 action='new_message'
#             )
            
#             return Response(response_data, status=status.HTTP_201_CREATED)
            
#         except Customer.DoesNotExist:
#             return Response(
#                 {'error': 'Customer not found'},
#                 status=status.HTTP_404_NOT_FOUND
#             )
#         except CustomerPlatformIdentity.DoesNotExist:
#             return Response(
#                 {'error': 'Platform identity not found or does not belong to customer'},
#                 status=status.HTTP_404_NOT_FOUND
#             )
#         except Exception as e:
#             logger.error(f"Error sending message: {str(e)}")
#             return Response(
#                 {'error': str(e)},
#                 status=status.HTTP_500_INTERNAL_SERVER_ERROR
#             )
    
#     def _get_or_create_ticket(self, customer, platform_identity, user):
#         """Get active ticket or create new one"""
#         from ticket.models import Status, OwnerLog, StatusLog
        
#         # Look for active tickets
#         active_tickets = Ticket.objects.filter(
#             customer_id=customer
#         ).exclude(
#             status_id__name='closed'
#         ).order_by('-created_on')
        
#         if active_tickets.exists():
#             return active_tickets.first()
        
#         # Create new ticket
#         open_status = Status.objects.get(name='open')
        
#         ticket = Ticket.objects.create(
#             customer_id=customer,
#             status_id=open_status,
#             owner_id=user,
#             ticket_interface=platform_identity.customer.main_interface_id,
#             created_by=user
#         )
        
#         # Create logs
#         OwnerLog.objects.create(
#             ticket_id=ticket,
#             owner_id=user,
#             created_by=user
#         )
        
#         StatusLog.objects.create(
#             ticket_id=ticket,
#             status_id=open_status,
#             created_by=user
#         )
        
#         return ticket


# class CustomerPlatformMarkReadView(APIView):
#     """
#     Mark messages as read
#     URL: POST /api/customers/{customer_id}/platforms/{platform_id}/mark-read/
#     """
#     # authentication_classes = [JWTAuthentication]
#     # permission_classes = [IsAuthenticated]
    
#     def post(self, request: Request, customer_id: int, platform_id: int):
#         try:
#             # Verify customer exists
#             customer = Customer.objects.get(customer_id=customer_id)
            
#             # Verify platform identity belongs to customer
#             platform_identity = CustomerPlatformIdentity.objects.get(
#                 id=platform_id,
#                 customer=customer,
#                 is_active=True
#             )
            
#             # Get message IDs
#             message_ids = request.data.get('message_ids', [])
            
#             if not message_ids:
#                 return Response(
#                     {'error': 'message_ids is required'},
#                     status=status.HTTP_400_BAD_REQUEST
#                 )
            
#             # Update messages to read status
#             with transaction.atomic():
#                 updated_count = Message.objects.filter(
#                     id__in=message_ids,
#                     platform_identity=platform_identity,
#                     is_self=False,  # Only mark customer messages as read
#                     status__in=[
#                         Message.MessageStatus.DELIVERED,
#                         Message.MessageStatus.SENT
#                     ]
#                 ).update(
#                     status=Message.MessageStatus.READ,
#                     read_on=timezone.now()
#                 )
                
#                 # Broadcast status updates via WebSocket
#                 from ticket.tasks import broadcast_message_status
                
#                 for message_id in message_ids:
#                     try:
#                         message = Message.objects.get(id=message_id)
#                         broadcast_message_status.delay(
#                             ticket_id=message.ticket_id.id,
#                             message_id=message_id,
#                             status=Message.MessageStatus.READ
#                         )
#                     except Message.DoesNotExist:
#                         continue
            
#             return Response({
#                 'success': True,
#                 'updated_count': updated_count,
#                 'message_ids': message_ids
#             })
            
#         except Customer.DoesNotExist:
#             return Response(
#                 {'error': 'Customer not found'},
#                 status=status.HTTP_404_NOT_FOUND
#             )
#         except CustomerPlatformIdentity.DoesNotExist:
#             return Response(
#                 {'error': 'Platform identity not found or does not belong to customer'},
#                 status=status.HTTP_404_NOT_FOUND
#             )
#         except Exception as e:
#             logger.error(f"Error marking messages as read: {str(e)}")
#             return Response(
#                 {'error': str(e)},
#                 status=status.HTTP_500_INTERNAL_SERVER_ERROR
#             )


# class CustomerStatsView(APIView):
#     """
#     Get customer statistics
#     URL: GET /api/customers/{customer_id}/stats/
#     """
#     # authentication_classes = [JWTAuthentication]
#     # permission_classes = [IsAuthenticated]
    
#     def get(self, request: Request, customer_id: int):
#         try:
#             customer = Customer.objects.get(customer_id=customer_id)
            
#             # Get statistics
#             from django.db.models import Count, Q, Avg
#             from datetime import timedelta
            
#             now = timezone.now()
            
#             # Ticket statistics
#             tickets = Ticket.objects.filter(customer_id=customer)
#             ticket_stats = tickets.aggregate(
#                 total=Count('id'),
#                 open=Count('id', filter=~Q(status_id__name='closed')),
#                 closed=Count('id', filter=Q(status_id__name='closed'))
#             )
            
#             # Message statistics
#             messages = Message.objects.filter(ticket_id__customer_id=customer)
#             message_stats = messages.aggregate(
#                 total=Count('id'),
#                 last_24h=Count('id', filter=Q(created_on__gte=now - timedelta(hours=24))),
#                 last_7d=Count('id', filter=Q(created_on__gte=now - timedelta(days=7))),
#                 last_30d=Count('id', filter=Q(created_on__gte=now - timedelta(days=30)))
#             )
            
#             # Platform statistics
#             active_platforms = customer.platform_identities.filter(
#                 is_active=True
#             ).values_list('platform', flat=True).distinct()
            
#             # Calculate average response time (simplified)
#             # This would need more complex logic in production
#             avg_response_time = None
            
#             stats = {
#                 'customer_id': customer.customer_id,
#                 'total_tickets': ticket_stats['total'],
#                 'open_tickets': ticket_stats['open'],
#                 'closed_tickets': ticket_stats['closed'],
#                 'total_messages': message_stats['total'],
#                 'last_24h_messages': message_stats['last_24h'],
#                 'last_7d_messages': message_stats['last_7d'],
#                 'last_30d_messages': message_stats['last_30d'],
#                 'avg_response_time': avg_response_time,
#                 'customer_since': customer.created_on.isoformat() if customer.created_on else None,
#                 'lifetime_value': float(customer.lifetime_value) if customer.lifetime_value else 0,
#                 'active_platforms': list(active_platforms)
#             }
            
#             return Response(stats)
            
#         except Customer.DoesNotExist:
#             return Response(
#                 {'error': 'Customer not found'},
#                 status=status.HTTP_404_NOT_FOUND
#             )
#         except Exception as e:
#             logger.error(f"Error getting customer stats: {str(e)}")
#             return Response(
#                 {'error': str(e)},
#                 status=status.HTTP_500_INTERNAL_SERVER_ERROR
#             )



class CustomerPlatformPagination(PageNumberPagination):
    page_size = 20
    page_size_query_param = 'limit'
    max_page_size = 100


class CustomerListWithActivityView(generics.ListAPIView):
    """
    Get customers with activity information and platform badges.
    Optimized for the customer list view.
    """
    # permission_classes = [IsAuthenticated]
    permission_classes = []
    
    def get(self, request):
        try:
            # Get query parameters
            search = request.query_params.get('search', '')
            platform_filter = request.query_params.get('platform', '')
            has_open_tickets = request.query_params.get('has_open_tickets', '')
            page = int(request.query_params.get('page', 1))
            limit = int(request.query_params.get('limit', 50))
            
            # Base queryset with optimizations
            queryset = Customer.objects.filter(
                account_status='ACTIVE'
            ).prefetch_related(
                Prefetch(
                    'platform_identities',
                    queryset=CustomerPlatformIdentity.objects.filter(is_active=True),
                    to_attr='active_platforms'
                )
            )
            
            # Apply search filter
            if search:
                queryset = queryset.filter(
                    Q(name__icontains=search) |
                    Q(email__icontains=search) |
                    Q(phone__icontains=search) |
                    Q(first_name__icontains=search) |
                    Q(last_name__icontains=search)
                )
            
            # Apply platform filter
            if platform_filter:
                queryset = queryset.filter(
                    platform_identities__platform=platform_filter.upper(),
                    platform_identities__is_active=True
                ).distinct()
            
            # Apply open tickets filter
            if has_open_tickets == 'true':
                queryset = queryset.filter(
                    ticket_customer__status_id__name__in=['open', 'assigned', 'waiting']
                ).distinct()
            
            # Annotate with activity data
            latest_message_subquery = Message.objects.filter(
                ticket_id__customer_id=OuterRef('pk')
            ).order_by('-created_on').values('created_on')[:1]
            
            queryset = queryset.annotate(
                last_message_time=Subquery(latest_message_subquery),
                open_ticket_count=Count(
                    'ticket_customer',
                    filter=~Q(ticket_customer__status_id__name='closed'),
                    distinct=True
                ),
                total_messages=Count('ticket_customer__message_ticket', distinct=True)
            ).order_by('-last_message_time', '-created_on')
            
            # Pagination
            total_count = queryset.count()
            start = (page - 1) * limit
            end = start + limit
            customers = queryset[start:end]
            
            # Serialize customer data
            customer_data = []
            for customer in customers:
                # Get platform badges
                platforms = []
                for p in customer.active_platforms[:5]:  # Limit to 5 platforms for UI
                    platforms.append({
                        'id': p.id,
                        'platform': p.platform,
                        'verified': p.is_verified,
                        'display_name': p.display_name,
                        'last_interaction': p.last_interaction
                    })
                
                customer_data.append({
                    'customer_id': customer.customer_id,
                    'name': customer.get_full_name() or customer.name,
                    'email': customer.email,
                    'phone': customer.phone,
                    'customer_type': customer.customer_type,
                    'platforms': platforms,
                    'last_message_time': customer.last_message_time.isoformat() if customer.last_message_time else None,
                    'open_tickets': customer.open_ticket_count,
                    'total_messages': customer.total_messages,
                    'created_on': customer.created_on.isoformat() if customer.created_on else None
                })
            
            return Response({
                'customers': customer_data,
                'total': total_count,
                'page': page,
                'page_size': limit,
                'has_more': total_count > end
            })
            
        except Exception as e:
            logger.error(f"Error in CustomerListWithActivityView: {str(e)}")
            return Response(
                {'error': str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class CustomerPlatformIdentitiesView(generics.ListAPIView):
    """
    Get paginated platform identities for a customer.
    """
    serializer_class = CustomerPlatformIdentitySerializer
    pagination_class = CustomerPlatformPagination
    # permission_classes = [IsAuthenticated]
    permission_classes = []
    
    def get_queryset(self):
        customer_id = self.kwargs['customer_id']
        return CustomerPlatformIdentity.objects.filter(
            customer__customer_id=customer_id,
            is_active=True
        ).select_related('customer').order_by('-last_interaction', '-created_on')
    
    def list(self, request, *args, **kwargs):
        queryset = self.get_queryset()
        page = self.paginate_queryset(queryset)
        
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            response = self.get_paginated_response(serializer.data)
            # Add custom fields to response
            response.data['has_more'] = response.data.get('next') is not None
            return response
        
        serializer = self.get_serializer(queryset, many=True)
        return Response({
            'results': serializer.data,
            'has_more': False,
            'total': len(serializer.data)
        })

# class CustomerPlatformIdentityDetailView(generics.RetrieveAPIView):
#     """
#     Get details of a specific platform identity for a customer.
#     """
#     serializer_class = CustomerPlatformIdentitySerializer
#     # permission_classes = [IsAuthenticated]
#     permission_classes = []
    
#     def get_object(self):
#         customer_id = self.kwargs.get('customer_id')
#         platform_id = self.kwargs.get('platform_id')
        
#         # Get the platform identity, ensuring it belongs to the specified customer
#         platform_identity = get_object_or_404(
#             CustomerPlatformIdentity,
#             id=platform_id,
#             customer__customer_id=customer_id,
#             is_active=True
#         )
        
#         return platform_identity
    
#     def retrieve(self, request, *args, **kwargs):
#         try:
#             instance = self.get_object()
#             serializer = self.get_serializer(instance)
            
#             # Format response to match the structure of the list endpoint
#             response_data = {
#                 "count": 1,
#                 "next": None,
#                 "previous": None,
#                 "results": [serializer.data],
#                 "has_more": False
#             }
            
#             return Response(response_data)
            
#         except CustomerPlatformIdentity.DoesNotExist:
#             return Response(
#                 {'error': 'Platform identity not found or does not belong to this customer'},
#                 status=status.HTTP_404_NOT_FOUND
#             )
#         except Exception as e:
#             logger.error(f"Error in CustomerPlatformIdentityDetailView: {str(e)}")
#             return Response(
#                 {'error': str(e)},
#                 status=status.HTTP_500_INTERNAL_SERVER_ERROR
#             )

class CustomerPlatformIdentityDetailView(generics.RetrieveAPIView):
    """
    Get details of a specific platform identity for a customer.
    """
    serializer_class = CustomerPlatformIdentitySerializer
    # permission_classes = [IsAuthenticated]
    permission_classes = []
    
    def get_object(self):
        customer_id = self.kwargs.get('customer_id')
        platform_id = self.kwargs.get('platform_id')
        
        # Get the platform identity, ensuring it belongs to the specified customer
        platform_identity = get_object_or_404(
            CustomerPlatformIdentity,
            id=platform_id,
            customer__customer_id=customer_id,
            is_active=True
        )
        
        return platform_identity
    
    # def retrieve(self, request, *args, **kwargs):
    #     try:
    #         instance = self.get_object()
    #         serializer = self.get_serializer(instance)
            
    #         # Return only the serialized data
    #         return Response(serializer.data)
            
    #     except CustomerPlatformIdentity.DoesNotExist:
    #         return Response(
    #             {'error': 'Platform identity not found or does not belong to this customer'},
    #             status=status.HTTP_404_NOT_FOUND
    #         )
    #     except Exception as e:
    #         logger.error(f"Error in CustomerPlatformIdentityDetailView: {str(e)}")
    #         return Response(
    #             {'error': str(e)},
    #             status=status.HTTP_500_INTERNAL_SERVER_ERROR
    #         )

    def retrieve(self, request, *args, **kwargs):
        try:
            instance = self.get_object()
            serializer = self.get_serializer(instance)
            
            # Get the latest ticket ID for this platform identity
            # Find the most recent message from this platform identity
            latest_message = Message.objects.filter(
                platform_identity=instance,
                # is_self=False  # Only consider messages from the customer
            ).select_related('ticket_id').order_by('-created_on').first()
            
            latest_ticket_id = None
            if latest_message and latest_message.ticket_id:
                latest_ticket_id = latest_message.ticket_id.id
            
            # Return the serialized data with latest_ticket_id
            response_data = serializer.data
            response_data['latest_ticket_id'] = latest_ticket_id
            
            return Response(response_data)
            
        except CustomerPlatformIdentity.DoesNotExist:
            return Response(
                {'error': 'Platform identity not found or does not belong to this customer'},
                status=status.HTTP_404_NOT_FOUND
            )
        except Exception as e:
            logger.error(f"Error in CustomerPlatformIdentityDetailView: {str(e)}")
            return Response(
                {'error': str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

class CustomerPlatformMessagesView(APIView):
    """
    Get latest message for each platform identity.
    """
    # permission_classes = [IsAuthenticated]
    permission_classes = []
    
    def get(self, request, customer_id):
        try:
            customer = get_object_or_404(Customer, customer_id=customer_id)
            
            # Get platform IDs from query params if provided
            platform_ids = request.query_params.get('platform_ids', '')
            
            # Build query for platform identities
            platforms_query = CustomerPlatformIdentity.objects.filter(
                customer=customer,
                is_active=True
            )
            
            if platform_ids:
                platform_id_list = [int(pid) for pid in platform_ids.split(',') if pid]
                platforms_query = platforms_query.filter(id__in=platform_id_list)
            
            # Get latest message for each platform
            latest_messages = {}
            
            for platform in platforms_query:
                latest_message = Message.objects.filter(
                    platform_identity=platform
                ).order_by('-created_on').first()
                
                if latest_message:
                    latest_messages[platform.id] = MessageSerializer(latest_message).data
            
            return Response(latest_messages)
            
        except Exception as e:
            logger.error(f"Error in CustomerPlatformMessagesView: {str(e)}")
            return Response(
                {'error': str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class CustomerUnreadCountsView(APIView):
    """
    Get unread message counts for each platform identity.
    """
    # permission_classes = [IsAuthenticated]
    permission_classes = []
    
    def get(self, request, customer_id):
        try:
            customer = get_object_or_404(Customer, customer_id=customer_id)
            
            # Get platform IDs from query params if provided
            platform_ids = request.query_params.get('platform_ids', '')
            
            # Build query for platform identities
            platforms_query = CustomerPlatformIdentity.objects.filter(
                customer=customer,
                is_active=True
            )
            
            if platform_ids:
                platform_id_list = [int(pid) for pid in platform_ids.split(',') if pid]
                platforms_query = platforms_query.filter(id__in=platform_id_list)
            
            # Get unread counts
            unread_counts = {}
            
            for platform in platforms_query:
                unread_count = Message.objects.filter(
                    platform_identity=platform,
                    is_self=False,  # Only count customer messages
                    status__in=['SENT', 'DELIVERED']  # Not read
                ).count()
                
                unread_counts[platform.id] = unread_count
            
            return Response(unread_counts)
            
        except Exception as e:
            logger.error(f"Error in CustomerUnreadCountsView: {str(e)}")
            return Response(
                {'error': str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

# # This version - in API response there is no information of MessageTemplate when message_template is not empty
# class PlatformConversationView(APIView):
#     """
#     Get messages for a specific platform identity with pagination.
#     """
#     # permission_classes = [IsAuthenticated]
#     permission_classes = []
    
#     def get(self, request, customer_id, platform_id):
#         try:
#             # Verify platform belongs to customer
#             platform = get_object_or_404(
#                 CustomerPlatformIdentity,
#                 id=platform_id,
#                 customer__customer_id=customer_id
#             )
            
#             # Get query parameters
#             before_id = request.query_params.get('before')
#             limit = int(request.query_params.get('limit', 50))
            
#             # Build message query
#             messages_query = Message.objects.filter(
#                 platform_identity=platform
#             ).select_related(
#                 'ticket_id',
#                 'created_by',
#                 'platform_identity'
#             ).order_by('-created_on')
            
#             # Apply before filter for pagination
#             if before_id:
#                 messages_query = messages_query.filter(id__lt=before_id)
            
#             # Get messages
#             messages = list(messages_query[:limit + 1])
#             has_more = len(messages) > limit
            
#             if has_more:
#                 messages = messages[:limit]
            
#             # Reverse to get chronological order
#             messages.reverse()
            
#             # Serialize messages
#             serialized_messages = []
#             for msg in messages:
#                 msg_data = MessageSerializer(msg).data
                
#                 # Add user image URL if available
#                 if msg.is_self and msg.created_by:
#                     # For staff messages
#                     if hasattr(msg.created_by, 'avatar_url'):
#                         msg_data['user_image_url'] = msg.created_by.avatar_url
#                 else:
#                     # For customer messages
#                     if platform.picture_url:
#                         msg_data['user_image_url'] = platform.picture_url
                
#                 serialized_messages.append(msg_data)
            
#             return Response({
#                 'messages': serialized_messages,
#                 'has_more': has_more,
#                 'platform': CustomerPlatformIdentitySerializer(platform).data
#             })
            
#         except CustomerPlatformIdentity.DoesNotExist:
#             return Response(
#                 {'error': 'Platform identity not found'},
#                 status=status.HTTP_404_NOT_FOUND
#             )
#         except Exception as e:
#             logger.error(f"Error in PlatformConversationView: {str(e)}")
#             return Response(
#                 {'error': str(e)},
#                 status=status.HTTP_500_INTERNAL_SERVER_ERROR
#             )
    
#     def post(self, request, customer_id, platform_id):
#         """Send a message through a specific platform."""
#         try:
#             # Verify platform belongs to customer
#             platform = get_object_or_404(
#                 CustomerPlatformIdentity,
#                 id=platform_id,
#                 customer__customer_id=customer_id
#             )
            
#             # Get message data
#             message_content = request.data.get('message', '').strip()
#             message_type = request.data.get('message_type', 'TEXT')
            
#             if not message_content:
#                 return Response(
#                     {'error': 'Message content is required'},
#                     status=status.HTTP_400_BAD_REQUEST
#                 )
            
#             # Get or create active ticket for this customer
#             from ticket.models import Ticket, Status
#             from user.models import User
            
#             active_ticket = Ticket.objects.filter(
#                 customer_id=platform.customer
#             ).exclude(
#                 status_id__name='closed'
#             ).order_by('-created_on').first()
            
#             if not active_ticket:
#                 # Create new ticket
#                 open_status = Status.objects.get(name='open')
#                 system_user = User.objects.get(name='System')
                
#                 active_ticket = Ticket.objects.create(
#                     customer_id=platform.customer,
#                     status_id=open_status,
#                     owner_id=request.user if request.user.is_authenticated else system_user,
#                     ticket_interface=platform.customer.main_interface_id,
#                     created_by=request.user if request.user.is_authenticated else system_user
#                 )
            
#             # Create message
#             message = Message.objects.create(
#                 ticket_id=active_ticket,
#                 message=message_content,
#                 user_name=request.user.get_full_name() if request.user.is_authenticated else 'Agent',
#                 is_self=True,  # Message from business
#                 message_type=message_type,
#                 status=Message.MessageStatus.SENDING,
#                 platform_identity=platform,
#                 created_by=request.user if request.user.is_authenticated else None
#             )
            
#             # Queue message for sending through platform
#             from ticket.tasks import route_message_to_customer
#             route_message_to_customer.delay(
#                 customer_id=platform.customer.customer_id,
#                 message_content=message_content,
#                 message_type=message_type,
#                 preferred_platform_identity_id=platform.id
#             )
            
#             # Return serialized message
#             return Response(
#                 MessageSerializer(message).data,
#                 status=status.HTTP_201_CREATED
#             )
            
#         except Exception as e:
#             logger.error(f"Error sending message: {str(e)}")
#             return Response(
#                 {'error': str(e)},
#                 status=status.HTTP_500_INTERNAL_SERVER_ERROR
#             )

class PlatformConversationView(APIView):
    """
    Get messages for a specific platform identity with pagination.
    """
    # permission_classes = [IsAuthenticated]
    permission_classes = []
    
    def get(self, request, customer_id, platform_id):
        try:
            # Verify platform belongs to customer
            platform = get_object_or_404(
                CustomerPlatformIdentity,
                id=platform_id,
                customer__customer_id=customer_id
            )
            
            # Get query parameters
            before_id = request.query_params.get('before')
            limit = int(request.query_params.get('limit', 50))
            
            # Build message query with message_template optimization
            messages_query = Message.objects.filter(
                platform_identity=platform
            ).select_related(
                'ticket_id',
                'created_by',
                'platform_identity',
                'message_template'  # Add select_related for MessageTemplate
            ).order_by('-created_on')
            
            # Apply before filter for pagination
            if before_id:
                messages_query = messages_query.filter(id__lt=before_id)
            
            # Get messages
            messages = list(messages_query[:limit + 1])
            has_more = len(messages) > limit
            
            if has_more:
                messages = messages[:limit]
            
            # Reverse to get chronological order
            messages.reverse()
            
            # Serialize messages
            serialized_messages = []
            for msg in messages:
                msg_data = MessageSerializer(msg).data
                
                # Add user image URL if available
                if msg.is_self and msg.created_by:
                    # For staff messages
                    if hasattr(msg.created_by, 'avatar_url'):
                        msg_data['user_image_url'] = msg.created_by.avatar_url
                else:
                    # For customer messages
                    if platform.picture_url:
                        msg_data['user_image_url'] = platform.picture_url
                
                # Add message template information if available
                if msg.message_template:
                    msg_data['message_template'] = {
                        'id': msg.message_template.id,
                        'sentence': msg.message_template.sentence,
                        'label': msg.message_template.label,
                        'parent': msg.message_template.parent,
                        'message_type': msg.message_template.message_type
                    }
                
                # Add social_app (platform) information
                msg_data['social_app'] = platform.platform
                
                serialized_messages.append(msg_data)
            
            return Response({
                'messages': serialized_messages,
                'has_more': has_more,
                'platform': CustomerPlatformIdentitySerializer(platform).data,
                'social_app': platform.platform  # Add platform at top level
            })
            
        except CustomerPlatformIdentity.DoesNotExist:
            return Response(
                {'error': 'Platform identity not found'},
                status=status.HTTP_404_NOT_FOUND
            )
        except Exception as e:
            logger.error(f"Error in PlatformConversationView: {str(e)}")
            return Response(
                {'error': str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )




    # # TODO - Check this and its newer version and Delete this if no longer needed
    # # This 2nd version is work before new message status updates and batch processing of Chat Center page
    # def post(self, request, customer_id, platform_id):
    #     """Send a message through a specific platform."""
    #     try:
    #         # Verify platform belongs to customer
    #         platform = get_object_or_404(
    #             CustomerPlatformIdentity,
    #             id=platform_id,
    #             customer__customer_id=customer_id
    #         )

    #         # Deltet 
    #         logger.info(f"PlatformConversationView's POST method's request: {request}")
    #         logger.info(f"PlatformConversationView's POST method's request user: {request.user}")
    #         logger.info(f"PlatformConversationView's POST method's request data: {request.data}")
            
    #         # Get message data
    #         message_content = request.data.get('message', '').strip()
    #         message_type = request.data.get('message_type', 'TEXT')
    #         message_template_id = request.data.get('message_template_id')  # Add support for template ID
            
    #         # if not message_content:
    #         #     return Response(
    #         #         {'error': 'Message content is required'},
    #         #         status=status.HTTP_400_BAD_REQUEST
    #         #     )
            
    #         # Get files if multipart request
    #         files = []
    #         if request.content_type and 'multipart' in request.content_type:
    #             # Collect all files from the request
    #             for key in request.FILES:
    #                 if key.startswith('file_'):
    #                     files.append(request.FILES[key])
            
    #         # Validate that we have content or files
    #         if not message_content and not files:
    #             return Response(
    #                 {'error': 'Message content or files required'},
    #                 status=status.HTTP_400_BAD_REQUEST
    #             )
            
    #         # # Get or create active ticket for this customer
    #         # from ticket.models import Ticket, Status
    #         # from user.models import User
            
    #         # active_ticket = Ticket.objects.filter(
    #         #     customer_id=platform.customer
    #         # ).exclude(
    #         #     status_id__name='closed'
    #         # ).order_by('-created_on').first()
            
    #         # if not active_ticket:
    #         #     # Create new ticket
    #         #     open_status = Status.objects.get(name='open')
    #         #     system_user = User.objects.get(name='System')
                
    #         #     active_ticket = Ticket.objects.create(
    #         #         customer_id=platform.customer,
    #         #         status_id=open_status,
    #         #         owner_id=request.user if request.user.is_authenticated else system_user,
    #         #         ticket_interface=platform.customer.main_interface_id,
    #         #         created_by=request.user if request.user.is_authenticated else system_user
    #         #     )

    #         from ticket.services.ticket_service import TicketService
    #         active_ticket, ticket_created = TicketService.get_or_create_active_ticket(
    #             # platform_identity=platform_identity
    #             platform_identity=platform
    #         )

    #         # Get current user (for staff messages) or system user
    #         # current_user = request.user if request.user.is_authenticated else User.objects.get(name='System')
    #         current_user = active_ticket.owner_id
            
    #         # Upload files if present
    #         file_urls = []
    #         file_metadata = {}
            
    #         if files:
    #             try:
    #                 # file_urls, file_metadata = message_file_service.message_file_service.upload_message_files(
    #                 file_urls, file_metadata = MessageFileService().upload_message_files(
    #                     files=files,
    #                     user=current_user,
    #                     customer_id=customer_id,
    #                     ticket_id=active_ticket.id
    #                 )
    #             except Exception as e:
    #                 logger.error(f"Error uploading files: {str(e)}")
    #                 return Response(
    #                     {'error': 'Failed to upload files'},
    #                     status=status.HTTP_500_INTERNAL_SERVER_ERROR
    #                 )
            
    #         # Get message template if provided
    #         message_template = None
    #         if message_template_id:
    #             try:
    #                 message_template = MessageTemplate.objects.get(id=message_template_id, is_active=True)
    #             except MessageTemplate.DoesNotExist:
    #                 return Response(
    #                     {'error': 'Invalid message template ID'},
    #                     status=status.HTTP_400_BAD_REQUEST
    #                 )
            
    #         # # Create message
    #         # message = Message.objects.create(
    #         #     ticket_id=active_ticket,
    #         #     message=message_content,
    #         #     user_name=request.user.get_full_name() if request.user.is_authenticated else 'Agent',
    #         #     is_self=True,  # Message from business
    #         #     message_type=message_type,
    #         #     status=Message.MessageStatus.SENDING,
    #         #     platform_identity=platform,
    #         #     message_template=message_template,  # Add message template
    #         #     created_by=request.user if request.user.is_authenticated else None
    #         # )

    #         # Create message
    #         message = Message.objects.create(
    #             ticket_id=active_ticket,
    #             message=message_content or f"Sent {len(files)} file(s)",
    #             user_name=current_user.get_full_name() or current_user.username,
    #             is_self=True,  # Message from staff
    #             message_type=message_type,
    #             status=Message.MessageStatus.SENT,
    #             platform_identity=platform,
    #             created_by=current_user,
    #             # File fields
    #             file_url=file_urls,
    #             file_metadata=file_metadata,
    #             has_attachments=bool(files)
    #         )

    #         # TODO - Delete this or Log this
    #         logger.info(f"Message created: {message.id} for platform {platform.id}")
    #         logger.info(f"Message instance: {message}")

            
    #         # # Queue message for sending through platform
    #         # from ticket.tasks import route_message_to_customer
    #         # route_message_to_customer.delay(
    #         #     customer_id=platform.customer.customer_id,
    #         #     message_content=message_content,
    #         #     message_type=message_type,
    #         #     preferred_platform_identity_id=platform.id
    #         # )

    #         # # Send outgoing message to a customer
    #         # send_message_via_route_message_to_customer.delay(
    #         #     ticket_id=active_ticket.id,
    #         #     message_content=message_content,
    #         #     message_type=message_type,
    #         #     metadata={},
    #         #     event_reply_token=None,
    #         #     bool_create_outgoing_message=False
    #         # )

    #         # TODO - Check Broadcasting

    #         # # Get created message of a ticket
    #         # message = Message.objects.filter(
    #         #     ticket_id=active_ticket,
    #         #     is_self=True
    #         # ).order_by('-created_on').first()
    #         # # TODO - Delete this or Log this
    #         # logger.info(f"Message created: {message.id} for platform {platform.id}")
    #         # logger.info(f"Message instance: {message}")
            
    #         # # Prepare response with message template and social_app
    #         # response_data = MessageSerializer(message).data









    #         # Send through platform if text content exists
    #         if message_content or files:
    #             from connectors.services.platform_routing_service import PlatformRoutingService
                
    #             # Route message through platform
    #             result = PlatformRoutingService.route_message_to_customer(
    #                 customer=platform.customer,
    #                 message_content=message_content,
    #                 message_type=message_type,
    #                 preferred_platform_identity_id=platform.id,
    #                 metadata={
    #                     'message_id': message.id,
    #                     'ticket_id': active_ticket.id,
    #                     'file_urls': file_urls,
    #                     'file_metadata': file_metadata
    #                 }
    #             )
                
    #             if result.get('success'):
    #                 message.status = Message.MessageStatus.DELIVERED
    #                 message.save()
    #             else:
    #                 message.status = Message.MessageStatus.FAILED
    #                 message.save()

    #         # Broadcast updates
    #         from customer.tasks import broadcast_platform_message_update
    #         broadcast_platform_message_update.delay(platform.id, message.id)

    #         from ticket.serializers import MessageWithFilesSerializer
    #         response_data = MessageWithFilesSerializer(message).data
            















    #         # Add message template information if available
    #         if message.message_template:
    #             response_data['message_template'] = {
    #                 'id': message.message_template.id,
    #                 'sentence': message.message_template.sentence,
    #                 'label': message.message_template.label,
    #                 'parent': message.message_template.parent,
    #                 'message_type': message.message_template.message_type
    #             }
            
    #         # Add social_app information
    #         response_data['social_app'] = platform.platform
            
    #         # Return serialized message
    #         return Response(
    #             response_data,
    #             status=status.HTTP_201_CREATED
    #         )
        
    #     except CustomerPlatformIdentity.DoesNotExist:
    #         return Response(
    #             {'error': 'Platform identity not found'},
    #             status=status.HTTP_404_NOT_FOUND
    #         )
            
    #     except Exception as e:
    #         logger.error(f"Error sending message: {str(e)}")
    #         return Response(
    #             {'error': str(e)},
    #             status=status.HTTP_500_INTERNAL_SERVER_ERROR
    #         )






















    # This 3rd version - 
    def post(self, request, customer_id, platform_id):
        """Send a message through a specific platform."""
        try:
            # Verify platform belongs to customer
            platform = get_object_or_404(
                CustomerPlatformIdentity,
                id=platform_id,
                customer__customer_id=customer_id
            )

            # Deltet 
            logger.info(f"PlatformConversationView's POST method's request: {request}")
            logger.info(f"PlatformConversationView's POST method's request user: {request.user}")
            logger.info(f"PlatformConversationView's POST method's request data: {request.data}")
            
            # Extract request data
            message_content = request.data.get('message', '').strip()
            message_type = request.data.get('message_type', 'TEXT')
            message_template_id = request.data.get('message_template_id')  # Add support for template ID

            # NEW: Support for pre-uploaded files
            pre_uploaded_files = request.data.get('pre_uploaded_files', [])

            # Legacy support: Get files if multipart request (for backward compatibility)
            files = []
            if request.content_type and 'multipart' in request.content_type:
                # Collect all files from the request
                for key in request.FILES:
                    if key.startswith('file_'):
                        files.append(request.FILES[key])

            # Validate that we have content or files (either pre-uploaded or multipart)
            if not message_content and not files and not pre_uploaded_files:
                return Response(
                    {'error': 'Message content, files, or pre_uploaded_files required'},
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            # Get or create active ticket
            with transaction.atomic():
                # active_ticket = self._get_or_create_active_ticket(platform.customer, request.user)

                from ticket.services.ticket_service import TicketService
                active_ticket, ticket_created = TicketService.get_or_create_active_ticket(
                    # platform_identity=platform_identity
                    platform_identity=platform
                )
                
                # Get current user for message ownership
                current_user = active_ticket.owner_id
                
                # Create messages using our service
                from ticket.services.message_creation_service import MessageCreationService
                message_service = MessageCreationService()

                try:
                    # Choose appropriate method based on whether files are pre-uploaded
                    if pre_uploaded_files:
                        # Use new method for pre-uploaded files
                        batch_result = message_service.create_message_batch_with_preloaded_files(
                            platform_identity=platform,
                            ticket=active_ticket,
                            user=current_user,
                            message_content=message_content,
                            pre_uploaded_files=pre_uploaded_files,
                            message_template_id=message_template_id
                        )
                    else:
                        # Use legacy method for messages without pre-uploaded files
                        batch_result = message_service.create_message_batch(
                            platform_identity=platform,
                            ticket=active_ticket,
                            user=current_user,
                            message_content=message_content,
                            files=files,
                            message_template_id=message_template_id
                        )
                    
                    # Prepare response
                    response_data = self._prepare_batch_response(
                        batch_result=batch_result,
                        platform=platform
                    )
                    
                    # Determine status code based on results
                    if batch_result['summary']['failed'] == 0:
                        status_code = status.HTTP_201_CREATED
                    elif batch_result['summary']['successful'] == 0:
                        status_code = status.HTTP_400_BAD_REQUEST
                    else:
                        status_code = status.HTTP_207_MULTI_STATUS  # Partial success
                    
                    return Response(response_data, status=status_code)
                    
                except Exception as e:
                    logger.error(f"Error creating message batch: {str(e)}")
                    raise
                
        except CustomerPlatformIdentity.DoesNotExist:
            return Response(
                {'error': 'Platform identity not found'},
                status=status.HTTP_404_NOT_FOUND
            )
        except Exception as e:
            logger.error(f"Unexpected error in PlatformConversationView: {str(e)}")
            return Response(
                {'error': 'An unexpected error occurred', 'detail': str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    # def _get_or_create_active_ticket(self, customer, request_user):
    #     """Get or create an active ticket for the customer."""
    #     # Look for active ticket
    #     active_ticket = Ticket.objects.filter(
    #         customer_id=customer
    #     ).exclude(
    #         status_id__name='closed'
    #     ).order_by('-created_on').first()
        
    #     if not active_ticket:
    #         # Create new ticket
    #         open_status = Status.objects.get(name='open')
    #         system_user = User.objects.get(name='System')
    #         current_user = request_user if request_user.is_authenticated else system_user
            
    #         active_ticket = Ticket.objects.create(
    #             customer_id=customer,
    #             status_id=open_status,
    #             owner_id=current_user,
    #             ticket_interface=customer.main_interface_id,
    #             created_by=current_user
    #         )
            
    #         logger.info(f"Created new ticket {active_ticket.id} for customer {customer.customer_id}")
        
    #     return active_ticket
    
    def _prepare_batch_response(self, batch_result: Dict, platform) -> Dict:
        """Prepare the response data for batch message creation."""
        # Serialize all created messages
        messages_data = []
        for message in batch_result['messages']:
            serializer = MessageWithFilesSerializer(message)
            message_data = serializer.data
            
            # Add platform info
            message_data['social_app'] = platform.platform
            
            # Add template info if available
            if message.message_template:
                message_data['message_template'] = {
                    'id': message.message_template.id,
                    'sentence': message.message_template.sentence,
                    'label': message.message_template.label,
                    'parent': message.message_template.parent,
                    'message_type': message.message_template.message_type
                }
            
            messages_data.append(message_data)
        
        # Build response
        response = {
            'batch_id': batch_result['batch_id'],
            'messages': messages_data,
            'summary': batch_result['summary']
        }
        
        # Add failed items if any
        if batch_result['failed_items']:
            response['failed_items'] = batch_result['failed_items']
        
        return response



class MarkMessagesAsReadView(APIView):
    """
    Mark messages as read for a platform.
    """
    # permission_classes = [IsAuthenticated]
    permission_classes = []
    
    def post(self, request, customer_id, platform_id):
        try:
            # Verify platform belongs to customer
            platform = get_object_or_404(
                CustomerPlatformIdentity,
                id=platform_id,
                customer__customer_id=customer_id
            )
            
            message_ids = request.data.get('message_ids', [])
            
            if not message_ids:
                return Response(
                    {'error': 'message_ids is required'},
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            # Update messages to read
            updated_count = Message.objects.filter(
                id__in=message_ids,
                platform_identity=platform,
                is_self=False,  # Only mark customer messages as read
                status__in=[Message.MessageStatus.SENT, Message.MessageStatus.DELIVERED]
            ).update(
                status=Message.MessageStatus.READ,
                read_on=timezone.now()
            )
            
            return Response({
                'updated_count': updated_count,
                'message_ids': message_ids
            })
            
        except Exception as e:
            logger.error(f"Error marking messages as read: {str(e)}")
            return Response(
                {'error': str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

            
class MarkAllMessagesAsReadView(APIView):
    """
    To mark old customer messages as read after ticket being transferred from System to Agent.
    """
    # permission_classes = [IsAuthenticated]
    permission_classes = []

    def post(self, request, customer_id, platform_id):
        try:
            # Verify platform belongs to customer
            platform = get_object_or_404(
                CustomerPlatformIdentity,
                id=platform_id,
                customer__customer_id=customer_id
            )

            # Update all messages to read
            updated_count = Message.objects.filter(
                platform_identity=platform,
                is_self=False,  # Only mark customer messages as read
                status__in=[Message.MessageStatus.SENT, Message.MessageStatus.DELIVERED]
            ).update(
                status=Message.MessageStatus.READ,
                read_on=timezone.now()
            )

            return Response({
                'updated_count': updated_count
            })

        except Exception as e:
            logger.error(f"Error marking messages as read: {str(e)}")
            return Response(
                {'error': str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class CustomerDetailWithPlatformsView(generics.RetrieveAPIView):
    """
    Get customer details with all platform identities.
    """
    queryset = Customer.objects.all()
    serializer_class = CustomerWithPlatformsSerializer
    # permission_classes = [IsAuthenticated]
    permission_classes = []
    lookup_field = 'customer_id'
    
    def get_queryset(self):
        return super().get_queryset().prefetch_related(
            Prefetch(
                'platform_identities',
                queryset=CustomerPlatformIdentity.objects.filter(is_active=True)
            )
        )

# Add a custom pagination class if you want to customize the page size
class PlatformIdentityPagination(PageNumberPagination):
    page_size = 10  # Reduced default size for better performance
    page_size_query_param = 'page_size'
    max_page_size = 200

    def get_page_size(self, request):
        """Allow smaller page sizes for initial loads (minimum 1)"""
        page_size = super().get_page_size(request)
        return max(1, min(page_size, self.max_page_size))

class CustomerPlatformIdentityListView(generics.ListAPIView):
    """
    List all CustomerPlatformIdentity instances with pagination.
    Includes last message information and unread counts.
    """
    # authentication_classes = [JWTAuthentication]
    # permission_classes = [IsAuthenticated]
    pagination_class = PlatformIdentityPagination
    serializer_class = CustomerPlatformIdentityWithCustomerSerializer
    
    def get_queryset(self):
        from ticket.models import Ticket
        from django.db.models import OuterRef, Subquery

        # Base queryset with optimizations
        queryset = CustomerPlatformIdentity.objects.filter(
            is_active=True
        ).select_related('customer')

        # Apply filters from query params
        platform = self.request.query_params.get('platform')
        if platform:
            queryset = queryset.filter(platform__iexact=platform)

        customer_id = self.request.query_params.get('customer_id')
        if customer_id:
            queryset = queryset.filter(customer__customer_id=customer_id)

        # Search by username or display name
        search = self.request.query_params.get('search')
        if search:
            queryset = queryset.filter(
                Q(display_name__icontains=search) |
                Q(platform_user_id__icontains=search)
            )

        # Tab-based filtering - Apply BEFORE pagination
        tab = self.request.query_params.get('tab')
        current_user = self.request.query_params.get('current_user')

        if tab and current_user:
            # Annotate with latest ticket information
            latest_ticket_subquery = Ticket.objects.filter(
                platform_identity=OuterRef('pk')
            ).order_by('-created_on')

            queryset = queryset.annotate(
                latest_ticket_status=Subquery(
                    latest_ticket_subquery.values('status_id__name')[:1]
                ),
                latest_ticket_owner=Subquery(
                    latest_ticket_subquery.values('owner_id__username')[:1]
                )
            )

            # Apply tab-specific filters
            if tab == 'my-assigned':
                # Status is 'assigned' or 'pending_to_close' AND owner is current user
                queryset = queryset.filter(
                    latest_ticket_status__in=['assigned', 'pending_to_close'],
                    latest_ticket_owner=current_user
                )

            elif tab == 'my-closed':
                # Status is 'closed' AND owner is current user
                queryset = queryset.filter(
                    latest_ticket_status='closed',
                    latest_ticket_owner=current_user
                )

            elif tab == 'open':
                # Status is 'open'
                queryset = queryset.filter(
                    latest_ticket_status='open'
                )

            elif tab == 'others-assigned':
                # Status is not 'open' AND owner is not current user AND owner is not null
                queryset = queryset.filter(
                    latest_ticket_owner__isnull=False
                ).exclude(
                    latest_ticket_status='open'
                ).exclude(
                    latest_ticket_owner=current_user
                )

        # Order by last interaction
        return queryset.order_by('-last_interaction', '-created_on')
    
    def list(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset())
        page = self.paginate_queryset(queryset)
        
        if page is not None:
            serialized_data = self.serialize_platform_identities(page)
            return self.get_paginated_response(serialized_data)
        
        serialized_data = self.serialize_platform_identities(queryset)
        return Response(serialized_data)
    
    def serialize_platform_identities(self, platform_identities):
        """
        Serialize platform identities with additional fields like last message and unread count.
        Includes tab-based filtering logic.
        """
        results = []

        # Tab filtering is now handled at the queryset level
        # No need to get these parameters here anymore

        # Get all platform identity IDs for batch queries
        platform_ids = [p.id for p in platform_identities]
        
        # Batch query for last messages
        last_messages = {}
        last_message_times = {}
        
        # Subquery to get the latest message for each platform identity
        latest_messages = Message.objects.filter(
            platform_identity_id__in=platform_ids
        ).values('platform_identity_id').annotate(
            latest_created_on=Max('created_on')
        )
        
        # Create a mapping of platform_identity_id to latest message time
        latest_message_map = {
            item['platform_identity_id']: item['latest_created_on'] 
            for item in latest_messages
        }
        
        # Get the actual messages
        if latest_message_map:
            messages = Message.objects.filter(
                platform_identity_id__in=platform_ids,
                created_on__in=latest_message_map.values()
            ).select_related('platform_identity')
            
            for msg in messages:
                if msg.created_on == latest_message_map.get(msg.platform_identity_id):
                    last_messages[msg.platform_identity_id] = msg.message
                    last_message_times[msg.platform_identity_id] = msg.created_on
        
        # Batch query for unread counts
        unread_counts = {}
        unread_count_results = Message.objects.filter(
            platform_identity_id__in=platform_ids,
            is_self=False,  # Only count customer messages
            status__in=[Message.MessageStatus.SENT, Message.MessageStatus.DELIVERED]
        ).values('platform_identity_id').annotate(
            unread_count=Count('id')
        )
        
        for item in unread_count_results:
            unread_counts[item['platform_identity_id']] = item['unread_count']
        
        # Build results
        for platform_identity in platform_identities:
            # Get CustomerPlatformIdentity's Latest Ticket
            from ticket.services.ticket_service import TicketService
            latest_ticket = TicketService.get_latest_ticket(platform_identity=platform_identity)

            results.append({
                'id': platform_identity.id,
                'customer': platform_identity.customer.customer_id,
                'customer_fullname': platform_identity.customer.get_full_name(),
                'customer_national_id': platform_identity.customer.national_id if platform_identity.customer.national_id else None,
                'platform': platform_identity.platform,
                'platform_user_id': platform_identity.platform_user_id,
                'platform_username': platform_identity.display_name or platform_identity.platform_user_id,
                'platform_avatar_url': platform_identity.picture_url,
                'channel_name': platform_identity.channel_name,
                'is_active': platform_identity.is_active,
                'latest_ticket_id': latest_ticket.id if latest_ticket else None,
                'latest_ticket_owner_id': latest_ticket.owner_id.id if latest_ticket and latest_ticket.owner_id else None,
                'latest_ticket_owner': latest_ticket.owner_id.get_full_name() if latest_ticket and latest_ticket.owner_id else None,
                'latest_ticket_owner_username': latest_ticket.owner_id.username if latest_ticket and latest_ticket.owner_id else None,
                'latest_ticket_status': latest_ticket.status_id.name if latest_ticket else None,
                'latest_ticket_priority': latest_ticket.priority.name if latest_ticket else None,
                'last_message': last_messages.get(platform_identity.id, None),
                'last_message_time': last_message_times.get(platform_identity.id, None),
                'unread_count': unread_counts.get(platform_identity.id, 0),
                'created_at': platform_identity.created_on,
                'updated_at': platform_identity.updated_on
            })

        # Tab filtering is now handled at the queryset level in get_queryset()
        # No need to filter here anymore - just return the results
        return results

class PlatformMessagesView(APIView):
    """Get latest messages for multiple platform identities."""
    permission_classes = []
    
    def get(self, request):
        platform_ids = request.query_params.get('platform_ids', '').split(',')
        platform_ids = [int(pid) for pid in platform_ids if pid]
        
        if not platform_ids:
            return Response({})
        
        latest_messages = {}
        
        for platform_id in platform_ids:
            try:
                platform = CustomerPlatformIdentity.objects.get(
                    id=platform_id,
                    is_active=True
                )
                
                latest_message = Message.objects.filter(
                    platform_identity=platform
                ).order_by('-created_on').first()
                
                if latest_message:
                    latest_messages[platform_id] = MessageSerializer(latest_message).data
            except CustomerPlatformIdentity.DoesNotExist:
                continue
        
        return Response(latest_messages)


class PlatformUnreadCountsView(APIView):
    """Get unread counts for multiple platform identities."""
    permission_classes = []

    def get(self, request):
        platform_ids = request.query_params.get('platform_ids', '').split(',')
        platform_ids = [int(pid) for pid in platform_ids if pid]

        if not platform_ids:
            return Response({})

        unread_counts = {}

        for platform_id in platform_ids:
            try:
                platform = CustomerPlatformIdentity.objects.get(
                    id=platform_id,
                    is_active=True
                )

                unread_count = Message.objects.filter(
                    platform_identity=platform,
                    is_self=False,
                    status__in=['SENT', 'DELIVERED']
                ).count()

                unread_counts[platform_id] = unread_count
            except CustomerPlatformIdentity.DoesNotExist:
                unread_counts[platform_id] = 0

        return Response(unread_counts)


class PlatformFileUploadView(APIView):
    """
    Immediate file upload endpoint for platform conversations.
    Handles file uploads independently from message creation.
    """
    permission_classes = []  # Match PlatformConversationView authentication

    def post(self, request, customer_id, platform_id):
        """
        Upload files immediately when selected by user.

        Expected request format:
        - Content-Type: multipart/form-data
        - Files with keys starting with 'file_' (e.g., file_0, file_1, etc.)

        Returns:
        - uploaded_files: List of successfully uploaded files with metadata
        - failed_files: List of files that failed to upload with error messages
        - summary: Statistics about the upload operation
        """
        try:
            # 1. Verify platform identity belongs to customer
            platform = get_object_or_404(
                CustomerPlatformIdentity,
                id=platform_id,
                customer__customer_id=customer_id
            )

            logger.info(f"PlatformFileUploadView: Processing upload for customer {customer_id}, platform {platform_id}")

            # 2. Get or create active ticket for proper blob path generation
            from ticket.services.ticket_service import TicketService
            active_ticket, ticket_created = TicketService.get_or_create_active_ticket(
                platform_identity=platform
            )

            logger.info(f"Using ticket {active_ticket.id} (created: {ticket_created})")

            # 3. Extract files from multipart request (same logic as PlatformConversationView)
            files = []
            if request.content_type and 'multipart' in request.content_type:
                for key in request.FILES:
                    if key.startswith('file_'):
                        files.append(request.FILES[key])
                        logger.info(f"Found file: {request.FILES[key].name} ({request.FILES[key].size} bytes)")

            if not files:
                return Response({
                    'success': False,
                    'error': 'No files provided. Files should have keys starting with "file_"',
                    'uploaded_files': [],
                    'failed_files': [],
                    'summary': {
                        'total_attempted': 0,
                        'successful': 0,
                        'failed': 0,
                        'total_size': 0
                    }
                }, status=status.HTTP_400_BAD_REQUEST)

            # 4. Process files using MessageFileService
            from customer._services.message_file_service import MessageFileService
            file_service = MessageFileService()

            uploaded_files = []
            failed_files = []

            for file in files:
                try:
                    # Validate file using same logic as message creation
                    is_valid, error_msg = file_service.validate_file(file)
                    if not is_valid:
                        failed_files.append({
                            'original_name': file.name,
                            'size': file.size,
                            'error': error_msg
                        })
                        logger.warning(f"File validation failed for {file.name}: {error_msg}")
                        continue

                    # Generate blob path using same method as message creation
                    blob_path = active_ticket.owner_id.get_message_file_path(
                        file.name,
                        customer_id,
                        active_ticket.id
                    )

                    logger.info(f"Generated blob path: {blob_path}")

                    # Upload file to Azure Blob Storage
                    url, metadata = file_service.upload_file(file, blob_path)

                    # Create response entry with file_id for frontend tracking
                    import uuid
                    uploaded_files.append({
                        'file_id': str(uuid.uuid4()),  # Unique ID for frontend tracking
                        'original_name': file.name,
                        'url': url,
                        'metadata': metadata
                    })

                    logger.info(f"Successfully uploaded {file.name} to {blob_path}")

                except Exception as e:
                    failed_files.append({
                        'original_name': file.name,
                        'size': getattr(file, 'size', 0),
                        'error': f'Upload failed: {str(e)}'
                    })
                    logger.error(f"Failed to upload {file.name}: {str(e)}")

            # 5. Calculate summary statistics
            total_size = sum(f['metadata']['size'] for f in uploaded_files)

            # 6. Return comprehensive response
            response_data = {
                'success': len(uploaded_files) > 0,
                'ticket_id': active_ticket.id,
                'uploaded_files': uploaded_files,
                'failed_files': failed_files,
                'summary': {
                    'total_attempted': len(files),
                    'successful': len(uploaded_files),
                    'failed': len(failed_files),
                    'total_size': total_size
                }
            }

            # Determine appropriate HTTP status code
            if len(failed_files) == 0:
                status_code = status.HTTP_200_OK  # All files uploaded successfully
            elif len(uploaded_files) == 0:
                status_code = status.HTTP_400_BAD_REQUEST  # All files failed
            else:
                status_code = status.HTTP_207_MULTI_STATUS  # Partial success

            logger.info(f"Upload completed: {len(uploaded_files)} successful, {len(failed_files)} failed")
            return Response(response_data, status=status_code)

        except CustomerPlatformIdentity.DoesNotExist:
            logger.error(f"Platform identity not found: customer_id={customer_id}, platform_id={platform_id}")
            return Response({
                'success': False,
                'error': 'Platform identity not found',
                'uploaded_files': [],
                'failed_files': [],
                'summary': {
                    'total_attempted': 0,
                    'successful': 0,
                    'failed': 0,
                    'total_size': 0
                }
            }, status=status.HTTP_404_NOT_FOUND)

        except Exception as e:
            logger.error(f"Unexpected error in PlatformFileUploadView: {str(e)}")
            return Response({
                'success': False,
                'error': f'Upload failed: {str(e)}',
                'uploaded_files': [],
                'failed_files': [],
                'summary': {
                    'total_attempted': 0,
                    'successful': 0,
                    'failed': 0,
                    'total_size': 0
                }
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class PlatformFileDeleteView(APIView):
    """
    File deletion endpoint for uploaded files before message creation.
    Allows users to remove files they've uploaded but decided not to send.
    """
    permission_classes = []  # Match upload endpoint authentication

    def delete(self, request, customer_id, platform_id):
        """
        Delete a previously uploaded file using its blob_path.

        Expected request body:
        {
            "blob_path": "user/emp123/messages/customer_456/ticket_789/20240115_143022_document.pdf"
        }

        Returns:
        - success: Boolean indicating operation result
        - message: Human-readable result message
        - blob_path: The blob path that was processed
        """
        try:
            # 1. Verify platform identity belongs to customer
            platform = get_object_or_404(
                CustomerPlatformIdentity,
                id=platform_id,
                customer__customer_id=customer_id
            )

            logger.info(f"PlatformFileDeleteView: Processing deletion for customer {customer_id}, platform {platform_id}")

            # 2. Extract blob_path from request body
            blob_path = request.data.get('blob_path')
            if not blob_path:
                return Response({
                    'success': False,
                    'error': 'blob_path is required in request body',
                    'blob_path': None
                }, status=status.HTTP_400_BAD_REQUEST)

            logger.info(f"Attempting to delete blob: {blob_path}")

            # 3. Validate blob_path format and ownership
            # Ensure blob_path starts with expected prefix
            if not blob_path.startswith('user/'):
                return Response({
                    'success': False,
                    'error': 'Invalid blob path format. Must start with "user/"',
                    'blob_path': blob_path
                }, status=status.HTTP_400_BAD_REQUEST)

            # Ensure blob_path contains the correct customer_id for security
            if f"customer_{customer_id}" not in blob_path:
                logger.warning(f"Security violation: blob_path {blob_path} does not belong to customer {customer_id}")
                return Response({
                    'success': False,
                    'error': 'Blob path does not belong to this customer',
                    'blob_path': blob_path
                }, status=status.HTTP_403_FORBIDDEN)

            # Additional validation: ensure it's a message file path
            if "/messages/" not in blob_path:
                return Response({
                    'success': False,
                    'error': 'Invalid blob path. Must be a message file path',
                    'blob_path': blob_path
                }, status=status.HTTP_400_BAD_REQUEST)

            # 4. Attempt to delete file using MessageFileService
            from customer._services.message_file_service import MessageFileService
            file_service = MessageFileService()

            deletion_success = file_service.delete_file(blob_path)

            if deletion_success:
                logger.info(f"Successfully deleted blob: {blob_path}")
                return Response({
                    'success': True,
                    'message': 'File deleted successfully',
                    'blob_path': blob_path
                }, status=status.HTTP_200_OK)
            else:
                # File deletion failed (likely file doesn't exist)
                logger.warning(f"File deletion failed for blob: {blob_path}")
                return Response({
                    'success': False,
                    'error': 'File deletion failed. File may not exist or may have already been deleted',
                    'blob_path': blob_path
                }, status=status.HTTP_404_NOT_FOUND)

        except CustomerPlatformIdentity.DoesNotExist:
            logger.error(f"Platform identity not found: customer_id={customer_id}, platform_id={platform_id}")
            return Response({
                'success': False,
                'error': 'Platform identity not found',
                'blob_path': request.data.get('blob_path')
            }, status=status.HTTP_404_NOT_FOUND)

        except Exception as e:
            logger.error(f"Unexpected error in PlatformFileDeleteView: {str(e)}")
            return Response({
                'success': False,
                'error': f'File deletion failed: {str(e)}',
                'blob_path': request.data.get('blob_path')
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


# class CustomerTicketAnalysesView(APIView):
#     """
#     Get all ticket analyses for a specific customer
#     URL: GET /api/customers/{customer_id}/ticket-analyses/
#     """
#     authentication_classes = [JWTAuthentication]
#     permission_classes = [IsAuthenticated]
    
#     def get(self, request: Request, customer_id: int):
#         try:
#             # Verify customer exists
#             customer = Customer.objects.get(customer_id=customer_id)
            
#             # Get query parameters for filtering
#             limit = int(request.query_params.get('limit', 50))
#             offset = int(request.query_params.get('offset', 0))
#             sentiment = request.query_params.get('sentiment', None)
#             start_date = request.query_params.get('start_date', None)
#             end_date = request.query_params.get('end_date', None)
            
#             # Get all tickets for this customer
#             customer_tickets = Ticket.objects.filter(
#                 customer_id=customer
#             ).values_list('id', flat=True)
            
#             # Build query for analyses
#             analyses_query = TicketAnalysis.objects.filter(
#                 ticket_id__in=customer_tickets
#             ).select_related(
#                 'ticket',
#                 'created_by',
#                 'updated_by'
#             ).prefetch_related(
#                 'highlights'
#             ).order_by('-created_on')
            
#             # Apply filters
#             if sentiment and sentiment in ['Positive', 'Neutral', 'Negative']:
#                 analyses_query = analyses_query.filter(sentiment=sentiment)
            
#             if start_date:
#                 try:
#                     start_datetime = datetime.fromisoformat(start_date)
#                     analyses_query = analyses_query.filter(created_on__gte=start_datetime)
#                 except ValueError:
#                     pass
            
#             if end_date:
#                 try:
#                     end_datetime = datetime.fromisoformat(end_date)
#                     analyses_query = analyses_query.filter(created_on__lte=end_datetime)
#                 except ValueError:
#                     pass
            
#             # Get total count before pagination
#             total_count = analyses_query.count()
            
#             # Apply pagination
#             analyses = analyses_query[offset:offset + limit]
            
#             # Serialize the data
#             serializer = TicketAnalysisSerializer(analyses, many=True)
            
#             # Aggregate statistics
#             sentiment_counts = analyses_query.values('sentiment').annotate(
#                 count=Count('id')
#             )
            
#             sentiment_stats = {
#                 'Positive': 0,
#                 'Neutral': 0,
#                 'Negative': 0
#             }
            
#             for item in sentiment_counts:
#                 sentiment_stats[item['sentiment']] = item['count']
            
#             # Calculate average tokens and cost
#             aggregates = analyses_query.aggregate(
#                 avg_tokens=Avg('total_tokens'),
#                 avg_cost=Avg('total_cost'),
#                 total_analyses=Count('id')
#             )
            
#             return Response({
#                 'customer_id': customer_id,
#                 'customer_name': customer.name,
#                 'total_count': total_count,
#                 'limit': limit,
#                 'offset': offset,
#                 'has_more': total_count > (offset + limit),
#                 'sentiment_statistics': sentiment_stats,
#                 'average_tokens': aggregates['avg_tokens'],
#                 'average_cost': aggregates['avg_cost'],
#                 'analyses': serializer.data
#             })
            
#         except Customer.DoesNotExist:
#             return Response(
#                 {'error': 'Customer not found'},
#                 status=status.HTTP_404_NOT_FOUND
#             )
#         except Exception as e:
#             logger.error(f"Error getting customer ticket analyses: {str(e)}")
#             return Response(
#                 {'error': str(e)},
#                 status=status.HTTP_500_INTERNAL_SERVER_ERROR
#             )

class CustomerTicketAnalysesView(APIView):
    """
    Get all ticket analyses for a specific customer
    URL: GET /api/customers/{customer_id}/ticket-analyses/
    """
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]
    
    def get(self, request, customer_id):
        try:
            # Verify customer exists
            customer = Customer.objects.get(customer_id=customer_id)
            
            # Get query parameters for filtering
            limit = int(request.query_params.get('limit', 50))
            offset = int(request.query_params.get('offset', 0))
            sentiment = request.query_params.get('sentiment', None)
            start_date = request.query_params.get('start_date', None)
            end_date = request.query_params.get('end_date', None)
            action = request.query_params.get('action', None)  # New filter
            
            # Get all tickets for this customer
            customer_tickets = Ticket.objects.filter(
                customer_id=customer
            ).values_list('id', flat=True)
            
            # Build query for analyses
            analyses_query = TicketAnalysis.objects.filter(
                ticket_id__in=customer_tickets
            ).select_related(
                'ticket',
                'created_by',
                'updated_by'
            ).prefetch_related(
                'highlights',
                'keywords',  # Include keywords in prefetch
                'ticket__topics'  # Add prefetch for ticket topics
            ).order_by('-created_on')
            
            # Apply filters
            if sentiment and sentiment in ['Positive', 'Neutral', 'Negative']:
                analyses_query = analyses_query.filter(sentiment=sentiment)
            
            if action and action in ['manual', 'auto', 'bulk', 'reanalysis', 'api_call']:
                analyses_query = analyses_query.filter(action=action)
            
            if start_date:
                try:
                    start_datetime = datetime.fromisoformat(start_date)
                    analyses_query = analyses_query.filter(created_on__gte=start_datetime)
                except ValueError:
                    pass
            
            if end_date:
                try:
                    end_datetime = datetime.fromisoformat(end_date)
                    analyses_query = analyses_query.filter(created_on__lte=end_datetime)
                except ValueError:
                    pass
            
            # Get total count before pagination
            total_count = analyses_query.count()
            
            # Apply pagination
            analyses = analyses_query[offset:offset + limit]
            
            # Serialize the data
            serializer = TicketAnalysisSerializer(analyses, many=True)
            
            # Aggregate statistics
            sentiment_counts = analyses_query.values('sentiment').annotate(
                count=Count('id')
            )
            
            sentiment_stats = {
                'Positive': 0,
                'Neutral': 0,
                'Negative': 0
            }
            
            for item in sentiment_counts:
                sentiment_stats[item['sentiment']] = item['count']
            
            # Action statistics
            action_counts = analyses_query.values('action').annotate(
                count=Count('id')
            )
            
            action_stats = {}
            for item in action_counts:
                if item['action']:
                    action_stats[item['action']] = item['count']
            
            # Calculate average tokens and cost
            aggregates = analyses_query.aggregate(
                avg_tokens=Avg('total_tokens'),
                avg_cost=Avg('total_cost'),
                total_analyses=Count('id')
            )
            
            # Get keyword statistics
            keyword_stats = {
                'total_customer_keywords': 0,
                'total_user_keywords': 0,
                'unique_customer_keywords': set(),
                'unique_user_keywords': set()
            }
            
            # Count keywords across all analyses
            for analysis in analyses:
                customer_keywords = analysis.keywords.filter(keyword_type='customer')
                user_keywords = analysis.keywords.filter(keyword_type='user')
                
                keyword_stats['total_customer_keywords'] += customer_keywords.count()
                keyword_stats['total_user_keywords'] += user_keywords.count()
                
                for kw in customer_keywords:
                    keyword_stats['unique_customer_keywords'].add(kw.keyword)
                for kw in user_keywords:
                    keyword_stats['unique_user_keywords'].add(kw.keyword)
            
            # Convert sets to counts for JSON serialization
            keyword_stats['unique_customer_keywords'] = len(keyword_stats['unique_customer_keywords'])
            keyword_stats['unique_user_keywords'] = len(keyword_stats['unique_user_keywords'])
                        
            return Response({
                'customer_id': customer_id,
                'customer_name': customer.name,
                'total_count': total_count,
                'limit': limit,
                'offset': offset,
                'has_more': total_count > (offset + limit),
                'sentiment_statistics': sentiment_stats,
                'action_statistics': action_stats,
                'keyword_statistics': keyword_stats,
                'average_tokens': aggregates['avg_tokens'],
                'average_cost': aggregates['avg_cost'],
                'analyses': serializer.data
            })
            
        except Customer.DoesNotExist:
            return Response(
                {'error': 'Customer not found'},
                status=status.HTTP_404_NOT_FOUND
            )
        except Exception as e:
            logger.error(f"Error getting customer ticket analyses: {str(e)}")
            return Response(
                {'error': str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

# ========== LIST VIEWS FOR FILTERS ==========
class CustomerTagListForFilterView(APIView):
    """
    Simple list view for all customer tags to populate filter dropdown
    """
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]
    
    def get(self, request, *args, **kwargs):
        """Get all customer tags for filter dropdown"""
        try:
            tags = CustomerTag.objects.all().values('id', 'name', 'color').order_by('name')
            
            return Response({
                'tags': list(tags),
                'total_count': len(tags)
            }, status=status.HTTP_200_OK)
            
        except Exception as e:
            return Response({
                'error': f'Error fetching tags: {str(e)}'
            }, status=status.HTTP_400_BAD_REQUEST)

class CustomerPlatformListForFilterView(APIView):
    """
    Simple list view for all customer platforms to populate filter dropdown
    """
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request, *args, **kwargs):
        """Get all customer platforms for filter dropdown"""
        try:
            platform_choices = CustomerPlatformIdentity._meta.get_field('platform').choices
            
            platform_list = []
            for value, label in platform_choices:
                platform_list.append({
                    'value': value,
                    'label': label
                })

            return Response({
                'platforms': platform_list,
                'total_count': len(platform_list)
            }, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({
                'error': f'Error fetching platforms: {str(e)}'
            }, status=status.HTTP_400_BAD_REQUEST)

