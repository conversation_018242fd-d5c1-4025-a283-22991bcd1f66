from django.contrib import admin
from .models import MessageTemplate, SystemSettings, PendingSettingChange, SLA, ChatbotProfile

@admin.register(SystemSettings)
class SystemSettingsAdmin(admin.ModelAdmin):
    list_display = ('key', 'masked_value', 'value_type', 'requires_restart', 'updated_on')
    list_filter = ('is_sensitive', 'requires_restart', 'value_type')
    search_fields = ('key', 'description')
    readonly_fields = ('updated_by', 'updated_on')
    
    def masked_value(self, obj):
        """Display masked value for sensitive settings"""
        if obj.is_sensitive:
            return "****"
        return obj.value[:50] + '...' if len(obj.value) > 50 else obj.value
    
    masked_value.short_description = 'Value'

@admin.register(PendingSettingChange)
class PendingSettingChangeAdmin(admin.ModelAdmin):
    list_display = ('setting_key', 'requested_by', 'requested_on', 'applied')
    list_filter = ('applied',)
    search_fields = ('setting_key',)
    readonly_fields = ('requested_by', 'requested_on')

@admin.register(MessageTemplate)
class MessageTemplateAdmin(admin.ModelAdmin):
    list_display = ['id', 'section', 'parent', 'label', 'sentence', 'status', 'department', 'is_active', 'created_at', 'updated_at', 'created_by']
    list_filter = ['is_active', 'created_at', 'created_by']
    search_fields = ['label', 'sentence']
    readonly_fields = ['created_at', 'updated_at']
    
    # Organize fields in the edit form
    fieldsets = (
        ('Basic Information', {
            'fields': ('section', 'parent', 'label', 'sentence', 'is_active', 'status', 'department')
        }),
        ('Message Configuration', {
            'fields': (
                'message_type_text',
                'message_type_quick_reply',
                'message_type_image',
                'message_type_image_map',
                'message_type_image_carousel',
                'message_type_carousel',
                'message_type_confirm_template',
                'message_type_buttons_template'
            )
        }),
        ('Metadata', {
            'fields': ('created_at', 'updated_at', 'created_by', 'updated_by'),
            'classes': ('collapse',)
        }),
    )
    
    def save_model(self, request, obj, form, change):
        if not change:  # If creating new object
            obj.created_by = request.user
        obj.updated_by = request.user
        super().save_model(request, obj, form, change)
        
@admin.register(SLA)
class SLAAdmin(admin.ModelAdmin):
    list_display = ['id', 'name', 'category', 'channel', 'value', 'unit', 'created_at', 'updated_at']
    list_filter = ['channel', 'unit', 'created_at', 'updated_at']
    search_fields = ['name', 'channel', 'value', 'category']
    readonly_fields = ['created_at', 'updated_at']
    
@admin.register(ChatbotProfile)
class ChatbotProfileAdmin(admin.ModelAdmin):
    list_display = ['chatbot_id', 'chatbot_mascot_thai_name', 'chatbot_mascot_english_name', 'chatbot_conversation_type', 'chatbot_conversation_style', 'chatbot_gender', 'chatbot_role', 'created_by','created_on']