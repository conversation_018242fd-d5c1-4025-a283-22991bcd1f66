import json
from rest_framework import serializers
from setting.models import ChatbotProfile #, ConnectedFlow, ConversationFlow
from user.models import UserSchedule



class ChatbotProfileSerializer(serializers.ModelSerializer):
    class Meta:
        model = ChatbotProfile
        fields = '__all__'


# class ConversationFlowSerializer(serializers.ModelSerializer):
#     chatbot = ChatbotProfileSerializer

#     class Meta:
#         model = ConversationFlow
#         fields = ['conversationflow_id', 'social_app', 'line_channel', 'facebook_channel', 'whatsapp_channel','chatbot', 'linkto_flowno']
        
# class ConnectedFlowSerializer(serializers.ModelSerializer):
#     full_message_type = serializers.JSONField(read_only=True)

#     class Meta:
#         model = ConnectedFlow
#         fields = '__all__'