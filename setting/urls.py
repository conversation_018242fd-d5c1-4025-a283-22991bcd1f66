from django.urls import path

from . import views
from setting._views.setting_system_crud import SettingsView
from setting._views.setting_system_upload_image import SettingsImageUploadView
from setting._views.setting_business_hours_crud import BusinessHoursView
from setting._views.setting_sla import SLAViewSet

urlpatterns = [
    # path('api/settings/', views.SettingsView.as_view(), name='settings-list'),
    path('api/settings/restart/', views.RestartServiceView.as_view(), name='settings-restart'),
    path('api/schedule/business-hours/', BusinessHoursView.as_view(), name='business-hours'),
    path('api/schedule/available-users/', views.AvailableUsersView.as_view(), name='available-users'),
    path('api/message-template/', views.MessageTemplateView.as_view(), name='message-template'),
    # path('api/schedule/available-users/', views.AvailableUsersView.as_view(), name='available-users'),
    # path('api/schedule/user-schedules/', views.UserScheduleViewSet, name='user-schedules'),
    path('api/sla/', SLAViewSet.as_view(), name='sla-list-create-update'),
    path('api/settings/', SettingsView.as_view(), name='settings-list-v2'),
    path('api/settings/image/', SettingsImageUploadView.as_view(), name='settings-image-upload'),

]