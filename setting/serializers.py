import json
from rest_framework import serializers
from .models import MessageTemplate, SystemSettings, PendingSettingChange, SLA
from user.models import UserSchedule

class SLASerializer(serializers.ModelSerializer):
    class Meta:
        model = SLA
        fields = ['id', 'name', 'category', 'channel', 'value', 'unit', 'created_at', 'updated_at']
        
class SystemSettingsSerializer(serializers.ModelSerializer):
    class Meta:
        model = SystemSettings
        fields = ['id', 'key', 'value', 'value_type', 'description', 
                  'is_sensitive', 'requires_restart', 'updated_on']
        read_only_fields = ['updated_by', 'updated_on']
        
    def to_representation(self, instance):
        """Mask sensitive values in API responses"""
        ret = super().to_representation(instance)
        if instance.is_sensitive:
            ret['value'] = '****'
        return ret

class PendingSettingChangeSerializer(serializers.ModelSerializer):
    class Meta:
        model = PendingSettingChange
        fields = ['id', 'setting_key', 'requested_on', 'applied']
        read_only_fields = ['requested_by', 'requested_on', 'applied']

class BusinessHoursSerializer(serializers.Serializer):
    """Serializer for business hours"""
    sameAsBusinessHours = serializers.BooleanField(default=False, required=False)
    workShift = serializers.ListField()
    
    def validate(self, data):
        """Custom validation can be added here"""
        return data

class UserScheduleSerializer(serializers.ModelSerializer):
    """Serializer for user schedule model"""
    schedule = serializers.JSONField(required=False)
    
    class Meta:
        model = UserSchedule
        fields = ['id', 'user', 'same_as_business_hours', 'schedule', 'updated_on']
        read_only_fields = ['id', 'updated_on']

class ScheduleDataSerializer(serializers.Serializer):
    """Serializer for the actual schedule data structure"""
    sameAsBusinessHours = serializers.BooleanField(default=False)
    workShift = serializers.ListField(required=False)
    
    def validate(self, data):
        """Validation handled by service layer"""
        return data
    

class MessageTemplateSerializer(serializers.ModelSerializer):
    message_type = serializers.SerializerMethodField()
    message_type_data = serializers.SerializerMethodField()
    sentence_count = serializers.IntegerField(read_only=True)

    class Meta:
        model = MessageTemplate
        fields = [
            "id", "sentence", "label", "parent", 
            "status", "department", 
            "message_type", "message_type_data", "section", "is_active", "sentence_count"
        ]

    def get_message_type(self, obj):
        return obj.message_type

    def get_message_type_data(self, obj):
        """Extract and format message_type JSON field."""
        if not obj.message_type:
            return {}
            
        # If it's already a dict, return it
        if isinstance(obj.message_type, dict):
            return obj.message_type
            
        # If it's a string, try to parse it
        if isinstance(obj.message_type, str):
            try:
                return json.loads(obj.message_type)
            except:
                return {}
                
        return {}
    
    def validate_sentence(self, value):
        """Ensure sentence is always a list"""
        if not isinstance(value, list):
            raise serializers.ValidationError("Sentence must be a list of strings")
        
        # Ensure all items in the list are strings
        for item in value:
            if not isinstance(item, str):
                raise serializers.ValidationError("All items in sentence list must be strings")
        
        return value