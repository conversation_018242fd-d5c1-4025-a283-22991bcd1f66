"""
URL configuration for devproject project.

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/5.0/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.contrib import admin
from django.urls import include, path, re_path
from django.conf import settings
from django.conf.urls.static import static

from .views import send_mass_notifications_view

from drf_yasg.views import get_schema_view
from drf_yasg import openapi

schema_view = get_schema_view(
    openapi.Info(
        title="Salmate APIs",
        default_version='0.0.1',
        description="Salmate API documentation",
    ),
)

urlpatterns = [
    path('admin/', admin.site.urls),
    path("chatbot/", include("linechatbot.urls")),
    path("ticket/", include("ticket.urls")),
    path("user/", include("user.urls")),
    path("customer/", include("customer.urls")),
    path("llm_rag_doc/", include("llm_rag_doc.urls")),
    path("setting/", include("setting.urls")),
    path("connectors/", include("connectors.urls")),
    path("dashboard/", include("dashboard.urls")),
    path("subscription/", include("subscription.urls")),
    # path('api/v1/', include('api.urls_export')),
    path('export/', include('api.urls_export')),
    # path("export/", include("export.urls")),
    path('consent/', include('consent.urls')),

    # path("auth/", include("user.urls")),
    
    path('send_mass_notifications/', send_mass_notifications_view),

    re_path(r'^swagger(?P<format>\.json|\.yaml)$', schema_view.without_ui(cache_timeout=0), name='schema-json'),
    re_path(r'^swagger/$', schema_view.with_ui('swagger', cache_timeout=0), name='schema-swagger-ui'),
    re_path(r'^redoc/$', schema_view.with_ui('redoc', cache_timeout=0), name='schema-redoc'),
] 

if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT) # Sub directory depend on upload_to fields of models
    # urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)


# TODO - this static is used for dev test, when production it should be remove
